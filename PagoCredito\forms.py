from django import forms
from .models import Pago

TIPO = (('ABON<PERSON>','ABONO'),('TOTALIDA','TOTALIDA'))

class PagoForm(forms.ModelForm):
   
    class Meta:
        model = Pago
        fields = ['tipo','abono']

        widgets = { 
            'tipo':forms.Select(attrs={'class': 'selectpicker form-control','data-style':'btn-outline-info','placeholder':'Tipo','require':True},choices=TIPO), 
            'abono': forms.TextInput(attrs={'class': 'form-control','autofocus': True,'require':True,'placeholder':'0.00','id':'abono'}), 
        }


class UpdatePagoForm(forms.ModelForm):
   
    class Meta:
        model = Pago
        fields = ['factura','tipo','abono']

        widgets = { 
            'factura': forms.TextInput(attrs={'class': 'form-control','require':True,'autofocus': True,'placeholder':'Numero Factura'}),
            'tipo':forms.Select(attrs={'class': 'selectpicker form-control','data-style':'btn-outline-info','placeholder':'Tipo','require':True},choices=TIPO), 
            'abono': forms.TextInput(attrs={'class': 'form-control','autofocus': True,'require':True,'placeholder':'0.00','id':'abono'}), 
        }



