# Generated by Django 5.0.4 on 2025-02-22 16:30

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Producto', '0009_alter_bitacora_doc'),
        ('Sucursal', '0002_sucursal_ubicacion'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Envios',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('correlativo', models.IntegerField(default=0)),
                ('tienda', models.CharField(max_length=250)),
                ('origen', models.CharField(max_length=250)),
                ('destino', models.CharField(max_length=250)),
                ('observacion', models.CharField(max_length=250)),
                ('fecha_envio', models.DateField(auto_now_add=True)),
                ('fecha', models.DateTimeField(auto_now_add=True)),
                ('token', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('usuario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['id'],
            },
        ),
        migrations.CreateModel(
            name='DetalleEnvios',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tienda', models.CharField(blank=True, default='', max_length=250, null=True)),
                ('cantidad', models.IntegerField(default=0)),
                ('precio_compra', models.DecimalField(decimal_places=2, max_digits=12)),
                ('precio_venta', models.DecimalField(decimal_places=2, max_digits=12)),
                ('total', models.DecimalField(decimal_places=2, max_digits=12)),
                ('observacion', models.CharField(max_length=250)),
                ('fecha', models.DateTimeField(auto_now_add=True)),
                ('token', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('id_prod', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Producto.producto')),
                ('usuario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('correlativo', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Sucursal.envios')),
            ],
            options={
                'ordering': ['id'],
            },
        ),
    ]
