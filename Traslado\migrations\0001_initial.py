# Generated by Django 4.2.6 on 2023-12-13 21:16

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Producto', '0007_detallefactura_estado_productofactura_estado'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('Proveedor', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Traslado',
            fields=[
                ('traslado', models.CharField(max_length=350, primary_key=True, serialize=False)),
                ('fecha', models.DateField()),
                ('id_prov', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Proveedor.proveedor')),
                ('usuario', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-traslado'],
            },
        ),
        migrations.CreateModel(
            name='DetalleTraslado',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cantidad', models.IntegerField(default=0)),
                ('stock_antes', models.IntegerField(default=0)),
                ('stock_ahora', models.IntegerField(default=0)),
                ('compra_antes', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('venta_antes', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('compra_ahora', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('venta_ahora', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('total', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('fecha', models.DateTimeField()),
                ('estado', models.IntegerField(blank=True, default=1, null=True)),
                ('id_prod', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Producto.producto')),
                ('traslado', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Traslado.traslado')),
                ('usuario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-traslado'],
            },
        ),
    ]
