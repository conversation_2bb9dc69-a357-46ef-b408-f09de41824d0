from django import forms
from .models import Sucursal,En<PERSON>s,DetalleEnvios,Vales,DetalleVales
from user.models import User

TIENDA = (
    ('Zacapa', 'Zacapa'),
    ('Estanzuela', 'Estanzuela'),
    ('<PERSON><PERSON><PERSON>an', '<PERSON><PERSON><PERSON>an'),
    ('Santa Cruz', 'Santa Cruz'),
    ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>')
)


class SucursalForm(forms.ModelForm):

    class Meta:
        model = Sucursal
        fields = ['nit', 'nombre', 'direccion',
                  'telefono', 'telefono2', 'correo', 'ubicacion']

        widgets = {
            'nit': forms.TextInput(attrs={'class': 'form-control', 'require': True, 'autofocus': True, 'placeholder': 'Nit Sucursal'}),
            'nombre': forms.TextInput(attrs={'class': 'form-control', 'require': True, 'autofocus': True, 'placeholder': 'Nombre de Sucursal'}),
            'direccion': forms.TextInput(attrs={'class': 'form-control', 'require': True, 'autofocus': True, 'placeholder': 'Direccion de Sucursal'}),
            'telefono': forms.TextInput(attrs={'class': 'form-control', 'require': True, 'autofocus': True, 'placeholder': 'Telefono Sucursal'}),
            'telefono2': forms.TextInput(attrs={'class': 'form-control', 'require': False, 'autofocus': True, 'placeholder': 'Telefono Sucursal'}),
            'correo': forms.TextInput(attrs={'class': 'form-control', 'require': False, 'autofocus': True, 'placeholder': '<EMAIL>'}),
            'ubicacion': forms.Select(attrs={'class': 'selectpicker form-control', 'data-style': 'btn-outline-info', 'require': True, 'style': 'border: 1px solid black;'}, choices=TIENDA),
        }


class UpdateSucursalForm(forms.ModelForm):

    class Meta:
        model = Sucursal
        fields = ['nit', 'nombre', 'direccion', 'telefono',
                  'telefono2', 'correo', 'ubicacion']

        widgets = {
            'nit': forms.TextInput(attrs={'class': 'form-control', 'require': True, 'autofocus': True, 'placeholder': 'Nit Sucursal'}),
            'nombre': forms.TextInput(attrs={'class': 'form-control', 'require': True, 'autofocus': True, 'placeholder': 'Nombre de Sucursal'}),
            'direccion': forms.TextInput(attrs={'class': 'form-control', 'require': True, 'autofocus': True, 'placeholder': 'Direccion de Sucursal'}),
            'telefono': forms.TextInput(attrs={'class': 'form-control', 'require': True, 'autofocus': True, 'placeholder': 'Telefono Sucursal'}),
            'telefono2': forms.TextInput(attrs={'class': 'form-control', 'require': False, 'autofocus': True, 'placeholder': 'Telefono Sucursal'}),
            'correo': forms.TextInput(attrs={'class': 'form-control', 'require': False, 'autofocus': True, 'placeholder': '<EMAIL>'}),
            'ubicacion': forms.Select(attrs={'class': 'selectpicker form-control', 'data-style': 'btn-outline-info', 'require': True, 'style': 'border: 1px solid black;'}, choices=TIENDA),
        }



####### FORM ENVIOS ########

class Envios_Tdas_Form(forms.ModelForm):

    class Meta:
        model = Envios
        fields = ['destino', 'observacion',]

        widgets = {
            'destino': forms.Select(attrs={'class': 'selectpicker form-control', 'data-style': 'btn-outline-info', 'require': True, 'style': 'border: 1px solid black;'}, choices=TIENDA),
            'observacion': forms.TextInput(attrs={'class': 'form-control', 'require': False, 'placeholder': 'Direccion de Sucursal','value':'S/O'}),
        }


class Detalle_Envios_Tdas_Form(forms.ModelForm):

    class Meta:
        model = DetalleEnvios
        fields = ['cantidad', 'observacion',]

        widgets = {
            'cantidad': forms.TextInput(attrs={'type':'number','class': 'form-control', 'require': False, 'placeholder': 'Direccion de Sucursal','value':'0'}),
            'observacion': forms.TextInput(attrs={'class': 'form-control', 'require': False, 'placeholder': 'Direccion de Sucursal','value':'S/O'}),
        }



####### FIN FORM ENVIOS #######



####### FORM VALES ########

class Vales_Tdas_Form(forms.ModelForm):

    class Meta:
        model = Vales
        fields = ['destino', 'observacion',]

        widgets = {
            'destino': forms.Select(attrs={'class': 'selectpicker form-control', 'data-style': 'btn-outline-info', 'require': True, 'style': 'border: 1px solid black;'}, choices=TIENDA),
            'observacion': forms.TextInput(attrs={'class': 'form-control', 'require': False, 'placeholder': 'Direccion de Sucursal','value':'S/O'}),
        }


class Detalle_Vales_Tdas_Form(forms.ModelForm):

    class Meta:
        model = DetalleVales
        fields = ['cantidad', 'observacion',]

        widgets = {
            'cantidad': forms.TextInput(attrs={'type':'number','class': 'form-control', 'require': False, 'placeholder': 'Direccion de Sucursal','value':'0'}),
            'observacion': forms.TextInput(attrs={'class': 'form-control', 'require': False, 'placeholder': 'Direccion de Sucursal','value':'S/O'}),
        }



####### FIN FORM VALES #######