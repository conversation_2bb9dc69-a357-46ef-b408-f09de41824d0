from django import forms
from .models import Cliente


class ClienteForm(forms.ModelForm):
   
    class Meta:
        model = Cliente
        fields = ['nit','nombre','direccion','tel']

        widgets = { 
            'nit': forms.TextInput(attrs={'class': 'form-control','require':True,'autofocus': True,'placeholder':'Nit'}),
            'nombre': forms.TextInput(attrs={'class': 'form-control','require':True,'autofocus': True,'placeholder':'Nombre de Cliente'}),
            'direccion': forms.TextInput(attrs={'class': 'form-control','require':True,'autofocus': True,'placeholder':'Direccion de Cliente'}),
            'tel': forms.TextInput(attrs={'class': 'form-control','require':True,'autofocus': True,'placeholder':'Telefono Cliente'}),
        }


class UpdateClienteForm(forms.ModelForm):
   
    class Meta:
        model = Cliente
        fields = ['nit','nombre','direccion','tel']

        widgets = { 
            'nit': forms.TextInput(attrs={'class': 'form-control','require':True,'autofocus': True,'placeholder':'Nit'}),
            'nombre': forms.TextInput(attrs={'class': 'form-control','require':True,'autofocus': True,'placeholder':'Nombre de Cliente'}),
            'direccion': forms.TextInput(attrs={'class': 'form-control','require':True,'autofocus': True,'placeholder':'Direccion de Cliente'}),
            'tel': forms.TextInput(attrs={'class': 'form-control','require':True,'autofocus': True,'placeholder':'Telefono Cliente'}),
        }



