from django import forms
from .models import Categoria


class CategoriaForm(forms.ModelForm):
   
    class Meta:
        model = Categoria
        fields = ['nombre']

        widgets = { 
            'nombre': forms.TextInput(attrs={'class': 'form-control','require':True,'autofocus': True,'placeholder':'Nombre de Categoria'}),
        }


class UpdateCategoriaForm(forms.ModelForm):
   
    class Meta:
        model = Categoria
        fields = ['nombre']

        widgets = { 
            'nombre': forms.TextInput(attrs={'class': 'form-control','autofocus': True,'require':True}),
        }



