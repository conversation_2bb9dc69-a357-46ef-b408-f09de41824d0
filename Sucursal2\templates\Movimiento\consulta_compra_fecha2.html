{% extends 'Base/base.html' %}
{% block title %}Consulta de Compras Por Fecha{% endblock %}

{% block content %}

{% if messages %}
{% for message in messages %}
<script>
    Swal.fire({
        "title": "Informacion Sistema",
        "text": "{{message}}",
        "icon": "{{message.tags}}"
    })
</script>
{% endfor %}
{% endif %}

<div class="content-wrapper">

    <div class="container-xxl flex-grow-1 container-p-y">
      <a href="{% url 'Consulta' %}"><button class="btn btn-info">Regresar</button></a><br></br>

        <!-- Basic Layout -->
        <div class="col-md-12">
            <div class="card md-6">
                <div class="card-header d-flex align-items-center justify-content-between">
                    <h5 class="mb-0">Consulta de Compras en Todas Las Sucursales</h5>
                    <small class="text-muted float-end">Consulta de Compras en Sucursales</small>
                </div>
                <div class="card-body">
                    <form action="#" method="POST" enctype="multipart/form-data">{% csrf_token %}
                        <div class="row">
                            <div class="col-md-3">
                                <label>Tienda</label>
                                {% if user.rol == 'admin' %}
                                <select name="tienda" class="form-control" required>
                                  <option value="" disabled selected>Selecciona Tienda</option>
                                    <option value="Todas">Todas</option>
                                    <option value="Estanzuela">Estanzuela</option>
                                    <option value="Teculutan">Teculutan</option>
                                    <option value="Zacapa">Zacapa</option>
                                    <option value="Santa Cruz">Santa Cruz</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                              <label>Tipo Movimiento</label>
                                <select name="tipo" class="form-control" required>
                                  <option value="" disabled selected>Selecciona Tipo</option>
                                  <option value="Todos">Todos</option>
                                  <option value="Compra">Compras</option>
                                  <option value="Consignacion">Consignaciones</option>
                                  <option value="Envio">Envios</option>
                                  <option value="Vale">Vales</option>
                                </select>
                                {% else %}
                                <label>Tipo Movimiento</label>
                                <select name="tipo" class="form-control" required>
                                  <option value="" disabled selected>Selecciona Tipo</option>
                                  <option value="Todos">Todos</option>
                                  <option value="Compra">Compras</option>
                                  <option value="Consignacion">Consignaciones</option>
                                  <option value="Envio">Envios</option>
                                  <option value="Vale">Vales</option>
                                </select>
                                <label>Tienda</label>
                                <select name="tienda" class="form-control" required>
                                  <option value="{{user.tienda}}">{{user.tienda}}</option>
                              </select>
                                {% endif %}
                            </div>
                            <div class="col-md-3">
                                <label>Fecha Inicio</label>
                                <input type="date" class="form-control" required name="inicio">
                            </div>
                            <div class="col-md-3">
                                <label>Fecha Fin</label>
                                <input type="date" class="form-control" required name="fin">
                            </div>
                            
                        </div><br>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary">Consultar</button>
                                <button type="reset" class="btn btn-danger">Cancelar</button>
                            </div>
                        </div>
                    
                    </form>
            </div>


        </div>

        {% if b %}
        
        <div class="content-wrapper">

            <div class="container-xxl flex-grow-1 container-p-y">
          
              <div class="row">
                <!-- Basic Layout -->
                <div class="col-md-12">
                  <div class="card md-6">
                    <div class="card-header d-flex align-items-center justify-content-between">
                      <h5 class="mb-0">Listado de Compras</h5>
                      <small class="text-muted float-end">Tipo de Busqueda <strong class="text-danger">{{tipo}}</strong></small>
                      <small class="text-muted float-end">Rango Fechas <strong class="text-danger">{{i}}</strong> al <strong class="text-danger">{{f}}</strong></small><br>
                      <small class="text-muted float-end">Compras Hechas <strong class="text-danger">{{c}}</strong></small>
                      <small class="text-muted float-end">Total Compras Solo Terminadas <strong class="text-danger">Q.{{t}}</strong></small>
                    </div>
                    <div class="card-body">
                      <div class="table-responsive-sm text-nowrap">
          
                        <input class="form-control col-md-3 light-table-filter" data-table="order-table" type="text"
                          placeholder="Buscar.." style="border: 1px solid black;" name="buscar" autofocus="buscar"><br>
          
                        <table class="table table-bordered table-sm order-table">

                          
                          <thead>
                            <tr>
                              <th>Tipo</th>
                              <th>Tienda</th>
                              <th>Factura</th>
                              <th>Proveedor</th>
                              <th>Antes</th>
                              <th>Ingreso</th>
                              <th>Salida</th>
                              <th>Actual</th>
                              <th>Fecha</th>
                              
                              
                            </tr>
                          </thead>
                          <tbody>
                            {% for c in compra %}
                            <tr>
                              <td>{{c.tipo}}</td>
                              <td>{{c.tienda}}</td>
                              {% if c.tienda == "Estanzuela" %}
                              <td>{{c.doc}}.1</td>
                              {% elif c.tienda == "Teculutan" %}
                              <td>{{c.doc}}.2</td>
                              {% elif c.tienda == "Zacapa" %}
                              <td>{{c.doc}}.3</td>
                              {% else %}
                              <td>{{c.doc}}.4</td>
                              {% endif %}
                              <td>{{c.prod}}</td>
                              <td>{{c.ultimo}}</td>
                              <td>{{c.entrada}}</td>
                              <td>{{c.salida}}</td>
                              <td>{{c.actual}}</td>
                              <td>{{c.fecha|date:'d-m-Y'}}</td>

                              
                              
                            </tr>
                            {% empty %}
                            <caption>SIN COMPRAS</caption>
                            {% endfor %}
          
                          </tbody>
                          
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
          
              </div>
          
            </div>
          
          </div>
        

          <script type="text/javascript">
            function popUp(URL) {
              window.open(URL, 'Ver Detalle', 'toolbar=0,scrollbars=0,location=0,statusbar=0,menubar=0,resizable=1,width=850,height=500,left = 590,top = 150');
            }
          </script>
          
          <script type="text/javascript">
              (function (document) {
                'use strict';
          
                var LightTableFilter = (function (Arr) {
          
                  var _input;
          
                  function _onInputEvent(e) {
                    _input = e.target;
                    var tables = document.getElementsByClassName(_input.getAttribute('data-table'));
                    Arr.forEach.call(tables, function (table) {
                      Arr.forEach.call(table.tBodies, function (tbody) {
                        Arr.forEach.call(tbody.rows, _filter);
                      });
                    });
                  }
          
                  function _filter(row) {
                    var text = row.textContent.toLowerCase(), val = _input.value.toLowerCase();
                    row.style.display = text.indexOf(val) === -1 ? 'none' : 'table-row';
                  }
          
                  return {
                    init: function () {
                      var inputs = document.getElementsByClassName('light-table-filter');
                      Arr.forEach.call(inputs, function (input) {
                        input.oninput = _onInputEvent;
                      });
                    }
                  };
                })(Array.prototype);
          
                document.addEventListener('readystatechange', function () {
                  if (document.readyState === 'complete') {
                    LightTableFilter.init();
                  }
                });
          
              })(document);
          </script>
        
        {% else %}

        {% endif %}  

    </div>

</div>



{% endblock %}