{% extends 'Base/base.html' %}
{% block title %}Consulta de Consginacion{% endblock %}

{% block content %}

<div class="content-wrapper">

    <div class="container-xxl flex-grow-1 container-p-y">
      <a href="{% url 'Consulta' %}"><button class="btn btn-info">Regresar</button></a><br></br>

        <!-- Basic Layout -->
        <div class="col-md-12">
            <div class="card md-6">
                <div class="card-header d-flex align-items-center justify-content-between">
                    <h5 class="mb-0">Consulta de Consignacion en Todas Las Sucursales</h5>
                    <small class="text-muted float-end">Consulta de Consignacion en Sucursales</small>
                </div>
                <div class="card-body">
                    <form action="#" method="POST" enctype="multipart/form-data">{% csrf_token %}
                        <div class="row">
                            <div class="col-md-4">
                                <label>Tienda</label>
                                {% if user.rol == 'admin' %}
                                <select name="tienda" class="form-control" required>
                                    <option value="Todas">Todas</option>
                                    <option value="Estanzuela">Estanzuela</option>
                                    <option value="Teculutan">Teculutan</option>
                                    <option value="Zacapa">Zacapa</option>
                                    <option value="Santa Cruz">Santa Cruz</option>
                                    <option value="Gualan">Gualan</option>
                                </select>
                                {% else %}
                                <select name="tienda" class="form-control" required>
                                  <option value="{{user.tienda}}">{{user.tienda}}</option>
                              </select>
                                {% endif %}
                            </div>
                            <div class="col-md-4">
                                <label>Fecha Inicio</label>
                                <input type="date" class="form-control" required name="inicio">
                            </div>
                            <div class="col-md-4">
                                <label>Fecha Fin</label>
                                <input type="date" class="form-control" required name="fin">
                            </div>
                            
                        </div><br>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary">Consultar</button>
                                <button type="reset" class="btn btn-danger">Cancelar</button>
                            </div>
                        </div>
                    
                    </form>
            </div>


        </div>

        {% if b %}

        <div class="content-wrapper">

            <div class="container-xxl flex-grow-1 container-p-y">
          
              <div class="row">
                <!-- Basic Layout -->
                <div class="col-md-12">
                  <div class="card md-6">
                    <div class="card-header d-flex align-items-center justify-content-between">
                      <h5 class="mb-0">Listado de Consignaciones</h5>
                      <small class="text-muted float-end">Rango Fechas <strong class="text-danger">{{i}}</strong> al <strong class="text-danger">{{f}}</strong></small><br>
                      <small class="text-muted float-end">Consignaciones Hechas <strong class="text-danger">{{c}}</strong></small>
                    </div>
                    <div class="card-body">
                             <div class="table-responsive-sm"
          style="width: 100%; height: 500px; overflow: auto; border-collapse: separate; border-spacing: 0;">



          <table class="table table-bordered table-sm order-table">
            <thead>
              <tr>
                <th>Prod</th>
                <th>Fecha</th>
                <th># Doc</th>
                <th>Tipo</th>
                <th>Anterior</th>
                <th>Ingreso</th>
                <th>Salida</th>
                <th>Saldo</th>
                <th>Tienda</th>
                <th>Usuario</th>
              </tr>
            </thead>
                <tbody style="font-size: 12px;">

  {% for p in bitacora %}
  <tr>
    <td>{{ p.prod }}</td>
    <td>{{ p.fecha }}</td>
    <td>{{ p.doc }}</td>
    <td>{{ p.tipo }}</td>
    <td>{{ p.habia }}</td>
    <td>{{ p.ingreso }}</td>
    <td>{{ p.salio }}</td>
    <td>{{ p.hay }}</td>
    <td>{{ p.tienda }}</td>
    <td>{{ p.usuario }}</td>
  </tr>
  {% endfor %}
 
  {% for m in movimientos %}
  <tr>
    <td>{{ m.prod }}</td>
    <td>{{ m.fecha }}</td>
    <td>{{ m.doc }}</td>
    <td>{{ m.tipo }}</td>
    <td>{{ m.ultimo }}</td>    {# lo equivalente a "habia" #}
    <td>{{ m.entrada }}</td>   {# lo equivalente a "ingreso" #}
    <td>{{ m.salida }}</td>    {# lo equivalente a "salio" #}
    <td>{{ m.actual }}</td>    {# lo equivalente a "hay" #}
    <td>{{ m.tienda }}</td>
    <td>{{ m.usuario }}</td>
  </tr>
  {% endfor %}

  {% if bitacora|length == 0 and movimientos|length == 0 %}
  <caption>SIN MOVIMIENTOS</caption>
  {% endif %}

</tbody>



          </table>
        </div>
                    </div>
                  </div>
                </div>
          
              </div>
          
            </div>
          
          </div>

          <script type="text/javascript">
            function popUp(URL) {
              window.open(URL, 'Ver Detalle', 'toolbar=0,scrollbars=0,location=0,statusbar=0,menubar=0,resizable=1,width=850,height=500,left = 590,top = 150');
            }
          </script>
          
          <script type="text/javascript">
              (function (document) {
                'use strict';
          
                var LightTableFilter = (function (Arr) {
          
                  var _input;
          
                  function _onInputEvent(e) {
                    _input = e.target;
                    var tables = document.getElementsByClassName(_input.getAttribute('data-table'));
                    Arr.forEach.call(tables, function (table) {
                      Arr.forEach.call(table.tBodies, function (tbody) {
                        Arr.forEach.call(tbody.rows, _filter);
                      });
                    });
                  }
          
                  function _filter(row) {
                    var text = row.textContent.toLowerCase(), val = _input.value.toLowerCase();
                    row.style.display = text.indexOf(val) === -1 ? 'none' : 'table-row';
                  }
          
                  return {
                    init: function () {
                      var inputs = document.getElementsByClassName('light-table-filter');
                      Arr.forEach.call(inputs, function (input) {
                        input.oninput = _onInputEvent;
                      });
                    }
                  };
                })(Array.prototype);
          
                document.addEventListener('readystatechange', function () {
                  if (document.readyState === 'complete') {
                    LightTableFilter.init();
                  }
                });
          
              })(document);
          </script>
        
        {% else %}

        {% endif %}  

    </div>

</div>



{% endblock %}