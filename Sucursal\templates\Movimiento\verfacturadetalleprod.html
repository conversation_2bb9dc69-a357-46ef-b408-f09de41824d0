{% extends 'Base/basever.html' %}
{% block title %}Detalle Factura{% endblock %}

{% block content %}

<div class="content-wrapper">

    <div class="container-xxl flex-grow-1 container-p-y">

        <div class="row">
            <!-- Basic Layout -->
            <div class="col-md-12">
                <div class="card md-6">
                    <div class="card-header d-flex align-items-center justify-content-between">
                        <h5 class="mb-0">Detalles de Factura</h5>
                        <small class="text-muted float-end">Detalles de Factura</small>
                    </div>
                    <div class="card-body">

                        <label for="DatosCliente" class="form-label label-venta">
                            <h4>DATOS DE
                                PROVEEDOR</h4>
                        </label>
                        <form action="#" method="POST">{% csrf_token %}
                            <div class="row">
                                <div class="col-md-4">
                                    <label for="Nit" class="form-label">Nit:</label>
                                    <input type="text" name="nit" class="form-control" value="{{ver.id_prov.nit}}" readonly>
                                </div>
                                <div class="col-md-4">
                                    <label for="Nombre" class="form-label">Nombre:</label>
                                    <input type="text" name="nombre" class="form-control" value="{{ver.id_prov.nombre}}"
                                        readonly>
                                </div>
                                <div class="col-md-4">
                                    <label for="Direccion" class="form-label">Direccion:</label>
                                    <input type="text" name="direccion" class="form-control" value="{{ver.id_prov.direccion}}"
                                        readonly>
                                </div>

                               
                            </div>
                        </form>

                    </div>&nbsp;

                    <div class="row" style="border-bottom: 2px solid black;" align="center">



                        <div class="col-md-6" style="border-right: 2px solid black;">
                            <label for="">Datos de Venta</label><br>
                            <div class="table-responsive">
                                <table class="table table-bordered table-sm" align="center">
                                    <thead>
                                        <tr align="center">
                                            <th>Factura</th>
                                            <th>Total</th>
                                            <th>Fecha</th>
                                            <th>Estado</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr align="center">
                                            <td>{{ver.factura}}</td>
                                            <td>Q.{{ver.total}}</td>
                                            <td>{{ver.fecha|date:"d-m-Y"}}</td>
                                            {% if ver.estado == 1 %}
                                            <td class="table-success" align="center">TERMINADA</td>
                                            {% elif ver.estado == 2 %}
                                            <td class="table-success" align="center">ANULADA</td>
                                            {% else %}
                                            <td class="table-danger" align="center">NO TERMINADA</td>
                                            {% endif %}
                                        </tr>

                                    </tbody>
                                </table>

                            </div>

                        </div>

                        <div class="col-md-6">

                            <h4>BUSQUEDA BLOQUEADA VENTA EN MODO VISTA</h4>

                        </div>
                    </div>



                    <div class="row" align="center">

                        <div class="col-md-12">

                            <label for="">Productos Agregados</label>

                            <div style="height: 12rem; overflow-y: scroll;">
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Producto</th>
                                                <th>Compra</th>
                                                <th>Venta</th>
                                                <th>Ingreso</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for d in detalle %}
                                            <tr>
                                                <th scope="row">{{d.id_prod.nombre}}</th>
                                                <td>Q.{{d.compra_ahora}}</td>
                                                <td>Q.{{d.venta_ahora}}</td>
                                                <td>{{d.cantidad}}</td>
                                            </tr>
                                            {% empty %}
                                            <caption>SIN PRODUCTOS</caption>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                        </div>

                    </div>

                </div>




            </div>




        </div>
    </div>





    {% endblock %}