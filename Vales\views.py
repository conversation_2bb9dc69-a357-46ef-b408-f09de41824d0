from django.shortcuts import render, redirect
from datetime import datetime
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from Vales.models import Vale, DetalleVale
from Vales.forms import ValeForm, UpdateValeForm
from user.models import User
import uuid
from django.db.models import Q
from Producto.models import Producto, Bitacora
from django.http import HttpResponse
from .repote import Comprobante


@login_required
def nuevo(request):
    form = ValeForm()
    if request.method == "POST":
        form = ValeForm(request.POST)
        if form.is_valid():
            try:
                v = Vale()
                v.origen = str(request.POST['origen']).capitalize()
                v.destino = str(request.POST['destino']).capitalize()
                v.estado = 1
                v.obs = form.cleaned_data['obs']
                v.total = 0.00
                v.fecha = datetime.today()
                v.usuario = User.objects.get(id=request.user.id)
                v.token = uuid.uuid4()
                v.save()
                messages.success(request, f'Vale Iniciado!')
                return redirect('DetalleVale', v.token)
            except:
                messages.error(
                    request, f'No Se Pudo Iniciar Envio!')
                return redirect('NuevoVale')

    return render(request, 'Vales/nuevo.html', {'form': form})


@login_required
def detalle(request, t):

    venta = Vale.objects.get(token=t)
    det = DetalleVale.objects.filter(token=t)

    # verificacion de token para validar venta
    if str(t) == str(venta.token):
        tok = True
    else:
        tok = False

    if request.method == "POST":

        if tok:

            if 'buscar' in request.POST:

                if request.POST['buscar'] == "":
                    messages.error(
                        request, f'Campo Busqueda No Puede Estar Vacio')
                    return redirect('DetalleVale', t)
                else:
                    if request.user.rol == "admin":
                        querys = (Q(id__icontains=request.POST['buscar'].strip()) | Q(
                            nombre__icontains=request.POST['buscar'].strip()))
                        busqueda = Producto.objects.filter(querys)
                        return render(request, 'Vales/detalle.html', {'e': venta, 'tk': tok, 'b': busqueda, 'd': det})
                    else:
                        querys = (Q(id__icontains=request.POST['buscar'].strip()) | Q(
                            nombre__icontains=request.POST['buscar'].strip()))
                        busqueda = Producto.objects.filter(
                            querys, tienda=request.user.tienda)
                        return render(request, 'Vales/detalle.html', {'e': venta, 'tk': tok, 'b': busqueda, 'd': det})

            elif 'agregar' in request.POST:

                ver = Producto.objects.get(id=request.POST['id'])

                if ver.stock >= int(request.POST['cantidad']):

                    if DetalleVale.objects.filter(producto=ver.id, token=t).exists():

                        endetalle = DetalleVale.objects.get(
                            id_prod=ver.id, token=t)

                        DetalleVale.objects.filter(producto=ver.id, token=t).update(cantidad=endetalle.cantidad+int(
                            request.POST['cantidad']), total=endetalle.total+(ver.precio_venta*int(request.POST['cantidad'])))

                        Producto.objects.filter(id=ver.id).update(
                            stock=ver.stock-int(request.POST['cantidad']), salio=ver.salio+int(request.POST['cantidad']))

                        Vale.objects.filter(token=t).update(
                            total=venta.total+(int(request.POST['cantidad'])*ver.precio_venta))

                        messages.success(
                            request, f'Agregando {request.POST["cantidad"]} Unidad de {ver.nombre}')

                        if request.user.rol == "admin":
                            # id, prod, t, d, h, i, s, hy, td, u
                            bitacora(ver.id, ver.nombre, 'Vale', venta.id, ver.stock, 0, int(
                                request.POST['cantidad']), (ver.stock-int(request.POST['cantidad'])), ver.tienda, request.user.username)
                        else:
                            # id, prod, t, d, h, i, s, hy, td, u
                            bitacora(ver.id, ver.nombre, 'Vale', venta.id, ver.stock, 0, int(
                                request.POST['cantidad']), (ver.stock-int(request.POST['cantidad'])), request.user.tienda, request.user.username)

                        return redirect('DetalleVale', t)
                    else:

                        d = DetalleVale()
                        d.envio = Vale.objects.get(
                            id=venta.id)
                        d.producto = Producto.objects.get(id=ver.id)
                        d.cantidad = int(request.POST['cantidad'])
                        d.precio = ver.precio_venta
                        d.total = d.precio*d.cantidad
                        d.fecha = datetime.today()
                        d.usuario = User.objects.get(id=request.user.id)
                        d.token = t
                        d.save()
                        Producto.objects.filter(id=ver.id).update(
                            stock=ver.stock-int(request.POST['cantidad']), salio=ver.salio+int(request.POST['cantidad']))
                        Vale.objects.filter(token=t).update(
                            total=venta.total+(int(request.POST['cantidad'])*ver.precio_venta))

                        if request.user.rol == "admin":

                            # id, prod, t, d, h, i, s, hy, td, u
                            bitacora(ver.id, ver.nombre, 'Vale', venta.id, ver.stock, 0, int(
                                request.POST['cantidad']), (ver.stock-int(request.POST['cantidad'])), ver.tienda, request.user.username)
                        else:
                            # id, prod, t, d, h, i, s, hy, td, u
                            bitacora(ver.id, ver.nombre, 'Vale', venta.id, ver.stock, 0, int(
                                request.POST['cantidad']), (ver.stock-int(request.POST['cantidad'])), request.user.tienda, request.user.username)

                        messages.success(
                            request, f'Agregando {request.POST["cantidad"]} Unidad de {ver.nombre}')
                        return redirect('DetalleVale', t)

                else:
                    messages.error(
                        request, f'Producto {ver.nombre} No Tiene Existencia!')
                    return redirect('DetalleVale', t)

            elif 'quitar' in request.POST:
                idetalle = DetalleVale.objects.get(
                    id=request.POST['corr'], token=t)
                elprod = Producto.objects.get(id=idetalle.producto.id)
                Vale.objects.filter(token=t).update(
                    total=venta.total-(idetalle.cantidad*idetalle.precio))
                Producto.objects.filter(id=idetalle.producto.id).update(
                    stock=elprod.stock+idetalle.cantidad, salio=elprod.salio-idetalle.cantidad)

                if request.user.rol == "admin":
                    # id, prod, t, d, h, i, s, hy, td, u
                    bitacora(elprod.id, elprod.nombre, 'Quitado de Vale', venta.id, elprod.stock, idetalle.cantidad,
                             0, elprod.stock+idetalle.cantidad, elprod.tienda, request.user.username)
                else:
                    # id, prod, t, d, h, i, s, hy, td, u
                    bitacora(elprod.id, elprod.nombre, 'Quitado de Vale', venta.id, elprod.stock, idetalle.cantidad,
                             0, elprod.stock+idetalle.cantidad, request.user.tienda, request.user.username)

                idetalle.delete()
                messages.success(
                    request, f'Se Quitado {idetalle.cantidad} de {elprod.nombre}!')
                return redirect('DetalleVale', t)

            elif 'terminar' in request.POST:
                miventa = Vale.objects.get(token=t)
                Vale.objects.filter(token=t).update(
                    estado=2, obs=request.POST['obs'])
                messages.success(request, f'{miventa.id}')
                return redirect('NuevoVale')

            elif 'descartar' in request.POST:

                # obetenemos la venta
                miventa = Vale.objects.get(token=t)

                # obtener productos en detalle
                midetalle = DetalleVale.objects.filter(token=t)

                # obtenemos el cliente para restar lo comprado
                ver = Vale.objects.filter(token=t).exists()

                if ver:
                    # regresamos el inventario a los productos
                    for d in midetalle:
                        # lista productos
                        prod = Producto.objects.get(id=d.producto.id)
                        Producto.objects.filter(id=d.producto.id).update(
                            stock=prod.stock+d.cantidad, ingreso=prod.ingreso+d.cantidad, salio=prod.salio-d.cantidad)

                        if request.user.rol == "admin":
                            # id, prod, t, d, h, i, s, hy, td, u
                            bitacora(prod.id, prod.nombre, 'Descartado de Vale', miventa.id, prod.stock, d.cantidad,
                                     0, prod.stock+d.cantidad, prod.tienda, request.user.username)
                        else:
                            # id, prod, t, d, h, i, s, hy, td, u
                            bitacora(prod.id, prod.nombre, 'Descartado de Vale', miventa.id, prod.stock, d.cantidad,
                                     0, prod.stock+d.cantidad, request.user.tienda, request.user.username)

                            midetalle.delete()

                    miventa.delete()

                    messages.warning(
                        request, f'Vale Descartado Productos Vuelven a Inventario!')
                    return redirect('NuevoVale')

                else:

                    messages.warning(
                        request, f'Vale Descartado Productos Vuelven a Inventario!')
                    return redirect('NuevoVale')

        else:
            messages.error(
                request, f'Vale Numero {venta.id} Cancelada Por Falta de Verificacion!')
            Vale.objects.filter(token=t).update(
                estado=99, tipo="Vale Corrupto")
            return redirect('NuevoVale')

    return render(request, 'Vales/detalle.html', {'e': venta, 'tk': tok, 'd': det})


##############  listado de vales ##########

@login_required
def listado(request):
    if request.user.is_superuser:
        datos = Vale.objects.all().order_by('id')  # Admin ve todo
    else:
        tienda_usuario = request.user.tienda  # Asume que el usuario tiene una tienda asociada
        datos = Vale.objects.filter(origen=tienda_usuario).order_by('id')  # Filtrar por tienda de origen

    
    return render(request, 'Vales/lista.html', {'envios': datos})


@login_required
def ver(request, t):
    v = Vale.objects.get(token=t)
    d = DetalleVale.objects.filter(token=t)

    # validacion de token

    if str(t) == str(v.token):
        tok = True
    else:
        tok = False

    return render(request, 'Vales/ver.html', {'tk': tok, 'e': v, 'd': d})


@login_required
def actualizar(request, id):
    envio = Vale.objects.get(id=id)
    if request.method == 'GET':
        form = UpdateValeForm(instance=envio)
    else:
        form = UpdateValeForm(request.POST, instance=envio)

        if form.is_valid():
            try:
                envio.fecha = str(datetime.today().strftime('%Y-%m-%d'))
                envio.usuario = User.objects.get(id=request.user.id)
                form.save()
                messages.success(
                    request, f'Envio # {envio.id} Modificado Exitosamente!')
                return redirect('ListaVale')
            except:
                messages.error(
                    request, f'No Se Pudo Modificar Envio # {envio.id}!')
                return redirect('ListaVale')

    return render(request, 'Vales/actualizar.html', {'form': form})


@login_required
def eliminar(request, id):
    try:
        envio = Vale.objects.get(id=id)

        for detalle in DetalleVale.objects.filter(token=envio.token):
            prod = Producto.objects.get(id=detalle.producto.id)
            Producto.objects.filter(id=detalle.producto.id).update(
                stock=prod.stock+detalle.cantidad, salio=prod.salio-detalle.cantidad)

        DetalleVale.objects.filter(token=envio.token).update(estado=99)
        Vale.objects.filter(token=envio.token).update(estado=99)
        messages.success(
            request, f'Vale # {envio.id} Anulado/Cancelado Productos Vuelven a Su Stock!')
        return redirect('ListaVale')
    except:
        messages.error(
            request, f'No Se Puede Anular/Cancelar Vale # {envio.id}')
        return redirect('ListaVale')


def pdf(request, f):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        response = HttpResponse(content_type='application/pdf')
        response[
            'Content-Disposition'] = f'attachment; filename="comprobante-vale-#-{f}.pdf"'
        r = Comprobante(f)
        response.write(r.run())
        return response


def bitacora(id, prod, t, d, h, i, s, hy, td, u):

    b = Bitacora()
    b.id_prod = id
    b.prod = prod
    b.tipo = t
    b.doc = d
    b.habia = h
    b.ingreso = i
    b.salio = s
    b.hay = hy
    b.tienda = td
    b.fecha = str(datetime.today())
    b.usuario = u
    b.save()
