from django.shortcuts import render,redirect
from datetime import datetime
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from Proveedor.models import Proveedor
from Proveedor.forms import ProveedorForm,UpdateProveedorForm
from user.models import User


@login_required
def nuevo(request):
    form = ProveedorForm()
    if request.method == "POST":
        form = ProveedorForm(request.POST)
        if form.is_valid():
            try:
                p = Proveedor()
                p.nit = form.cleaned_data['nit']
                p.nombre = form.cleaned_data['nombre']
                p.direccion = form.cleaned_data['direccion']
                p.telefono = form.cleaned_data['telefono']
                p.compras = 0
                p.total_compra = 0.00
                p.fecha = str(datetime.today().strftime('%Y-%m-%d'))
                p.usuario = User.objects.get(id=request.user.id)
                p.save()
                messages.success(request,f'{p.nombre} Ingresado!')
                return redirect('NuevoProveedor')
            except:
                messages.error(request,f'No Se Pudo Ingresar a {p.nombre}!')
                return redirect('NuevoProveedor')
    
    return render(request,'Proveedor/nuevo.html',{'form':form})



@login_required
def listado(request):
    datos = Proveedor.objects.all().order_by('nit')
    return render(request,'Proveedor/lista.html',{'cli':datos})


@login_required
def actualizar(request,id):
    prov = Proveedor.objects.get(nit=id)
    if request.method == 'GET':
        form = UpdateProveedorForm(instance=prov)
    else:
        form = UpdateProveedorForm(request.POST,instance = prov)
     
        if form.is_valid():
            try:
                prov.fecha = str(datetime.today().strftime('%Y-%m-%d'))
                prov.usuario = User.objects.get(id=request.user.id)
                form.save()
                messages.success(request, f'Proveedor {prov.nombre} Modificada Exitosamente!')
                return redirect('ListaProveedor')
            except:
                messages.error(request, f'No Se Pudo Modificar {prov.nombre}!')
                return redirect('ListaProveedor')

    return render(request,'Proveedor/actualizar.html',{'form':form})




@login_required
def eliminar(request,id):
    try:
        prov = Proveedor.objects.get(nit=id)
        prov.delete()
        messages.success(request,f'{prov.nombre} Eliminado!')
        return redirect('ListaProveedor')
    except:
        messages.error(request,f'No Se Puede Eliminar {prov.nombre}')
        return redirect('ListaProveedor')    
