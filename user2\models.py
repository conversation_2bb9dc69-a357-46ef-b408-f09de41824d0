from django.db import models
from django.contrib.auth.models import AbstractUser

# Create your models here.
class User(AbstractUser):
    rol = models.CharField(max_length=250,null=True,blank=True)
    tienda = models.CharField(max_length=250,blank=True,null=True)
    estado = models.IntegerField(blank=False,null=False,default=0)
    vendido = models.DecimalField(max_digits=10,decimal_places=2,blank=True,null=True,default=0.00)
    