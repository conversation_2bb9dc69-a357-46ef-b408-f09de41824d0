from django.db import models
from user.models import User

class Cliente(models.Model):
    nit = models.CharField(primary_key=True,max_length=15,blank=False,null=False)
    nombre = models.CharField(max_length=550,blank=False,null=False)
    direccion = models.CharField(max_length=850,blank=False,null=False)
    tel = models.CharField(max_length=9,blank=True,null=True,default='0000-0000')
    compras_contado = models.IntegerField(blank=True,null=True,default=0)
    total_contado = models.DecimalField(max_digits=18,decimal_places=2,blank=True,null=True,default=0.00)
    compras_credito = models.IntegerField(blank=True,null=True,default=0)
    total_credito = models.DecimalField(max_digits=18,decimal_places=2,blank=True,null=True,default=0.00)
    total_credito_pagado = models.DecimalField(max_digits=18,decimal_places=2,blank=True,null=True,default=0.00)
    fecha = models.CharField(max_length=10,blank=True,null=True)
    usuario = models.ForeignKey(User,on_delete=models.CASCADE,blank=False,null=False)

    class Meta:
        ordering = ["nit"]

    def __str__(self):
        return self.nit
