# Generated by Django 4.2.6 on 2023-12-06 10:26

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('Proveedor', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('Producto', '0002_alter_producto_options'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductoFactura',
            fields=[
                ('factura', models.BigIntegerField(primary_key=True, serialize=False)),
                ('serie', models.CharField(default='S/S', max_length=50)),
                ('fecha_factura', models.DateField()),
                ('cantidad', models.IntegerField(default=0)),
                ('total', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('fecha', models.DateTimeField()),
                ('id_prov', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Proveedor.proveedor')),
                ('usuario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['factura'],
            },
        ),
        migrations.CreateModel(
            name='DetalleFactura',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cantidad', models.IntegerField(default=0)),
                ('compra_antes', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('venta_antes', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('compra_ahora', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('venta_ahora', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('fecha', models.DateTimeField()),
                ('factura', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Producto.productofactura')),
                ('id_prod', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Producto.producto')),
                ('usuario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['factura'],
            },
        ),
    ]
