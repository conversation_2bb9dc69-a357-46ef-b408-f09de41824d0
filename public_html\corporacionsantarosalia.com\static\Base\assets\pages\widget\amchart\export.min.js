AmCharts.translations.export||(AmCharts.translations.export={}),AmCharts.translations.export.en||(AmCharts.translations.export.en={"fallback.save.text":"CTRL + C to copy the data into the clipboard.","fallback.save.image":"Rightclick -> Save picture as... to save the image.","capturing.delayed.menu.label":"{{duration}}","capturing.delayed.menu.title":"Click to cancel","menu.label.print":"Print","menu.label.undo":"Undo","menu.label.redo":"Redo","menu.label.cancel":"Cancel","menu.label.save.image":"Download as ...","menu.label.save.data":"Save as ...","menu.label.draw":"Annotate ...","menu.label.draw.change":"Change ...","menu.label.draw.add":"Add ...","menu.label.draw.shapes":"Shape ...","menu.label.draw.colors":"Color ...","menu.label.draw.widths":"Size ...","menu.label.draw.opacities":"Opacity ...","menu.label.draw.text":"Text","menu.label.draw.modes":"Mode ...","menu.label.draw.modes.pencil":"Pencil","menu.label.draw.modes.line":"Line","menu.label.draw.modes.arrow":"Arrow","label.saved.from":"Saved from: "}),AmCharts.export=function(e,t){var a,i={name:"export",version:"1.4.74",libs:{async:!0,autoLoad:!0,reload:!1,resources:["fabric.js/fabric.min.js","FileSaver.js/FileSaver.min.js",{"jszip/jszip.min.js":["xlsx/xlsx.min.js"],"pdfmake/pdfmake.min.js":["pdfmake/vfs_fonts.js"]}],namespaces:{"pdfmake.min.js":"pdfMake","jszip.min.js":"JSZip","xlsx.min.js":"XLSX","fabric.min.js":"fabric","FileSaver.min.js":"saveAs"},loadTimeout:1e4,unsupportedIE9libs:["pdfmake.min.js","jszip.min.js","xlsx.min.js"]},config:{},setup:{chart:e,hasBlob:!1,wrapper:!1,isIE:!!window.document.documentMode,IEversion:window.document.documentMode,hasTouch:"object"==typeof window.Touch,focusedMenuItem:void 0,hasClasslist:"classList"in document.createElement("_")},drawing:{enabled:!1,undos:[],redos:[],buffer:{position:{x1:0,y1:0,x2:0,y2:0,xD:0,yD:0}},handler:{undo:function(e,t){var a=i.drawing.undos.pop();if(a){a.selectable=!0,i.drawing.redos.push(a),"added"==a.action&&i.setup.fabric.remove(a.target);var r=JSON.parse(a.state);a.target.set(r),a.target instanceof fabric.Group&&i.drawing.handler.change({color:r.cfg.color,width:r.cfg.width,opacity:r.cfg.opacity},!0,a.target),i.setup.fabric.renderAll()}},redo:function(e,t){var a=i.drawing.redos.pop();if(a){a.selectable=!0,i.drawing.undos.push(a),"added"==a.action&&i.setup.fabric.add(a.target);var r=JSON.parse(a.state);a.target.recentState=a.state,a.target.set(r),a.target instanceof fabric.Group&&i.drawing.handler.change({color:r.cfg.color,width:r.cfg.width,opacity:r.cfg.opacity},!0,a.target),i.setup.fabric.renderAll()}},done:function(e){i.drawing.enabled=!1,i.drawing.buffer.enabled=!1,i.drawing.undos=[],i.drawing.redos=[],i.createMenu(i.config.menu),i.setup.fabric.deactivateAll(),i.isElement(i.setup.wrapper)&&i.isElement(i.setup.wrapper.parentNode)&&i.setup.wrapper.parentNode.removeChild&&(i.setup.wrapper.parentNode.removeChild(i.setup.wrapper),i.setup.wrapper=!1)},add:function(e){var t=i.deepMerge({top:i.setup.fabric.height/2,left:i.setup.fabric.width/2},e||{});(-1!=t.url.indexOf(".svg")?fabric.loadSVGFromURL:fabric.Image.fromURL)(t.url,function(e,a){var r=void 0!==a?fabric.util.groupSVGElements(e,a):e,n=!1;(r.height>i.setup.fabric.height||r.width>i.setup.fabric.width)&&(n=i.setup.fabric.height/2/r.height),t.top>i.setup.fabric.height&&(t.top=i.setup.fabric.height/2),t.left>i.setup.fabric.width&&(t.left=i.setup.fabric.width/2),i.drawing.buffer.isDrawing=!0,r.set({originX:"center",originY:"center",top:t.top,left:t.left,width:n?r.width*n:r.width,height:n?r.height*n:r.height,fill:i.drawing.color}),i.setup.fabric.add(r)})},change:function(e,t,a){var r,n,o,s=i.deepMerge({},e||{}),l=a||i.drawing.buffer.target,d=l?l._objects?l._objects:[l]:null;if(s.mode&&(i.drawing.mode=s.mode),s.width&&(i.drawing.width=s.width,i.drawing.fontSize=s.fontSize=3*s.width,1==i.drawing.width&&(i.drawing.fontSize=s.fontSize=i.defaults.fabric.drawing.fontSize)),s.fontSize&&(i.drawing.fontSize=s.fontSize),s.color&&(i.drawing.color=s.color),s.opacity&&(i.drawing.opacity=s.opacity),(o=i.getRGBA(i.drawing.color)).pop(),o.push(i.drawing.opacity),i.drawing.color="rgba("+o.join()+")",i.setup.fabric.freeDrawingBrush.color=i.drawing.color,i.setup.fabric.freeDrawingBrush.width=i.drawing.width,l){for((r=JSON.parse(l.recentState).cfg)&&(s.color=s.color||r.color,s.width=s.width||r.width,s.opacity=s.opacity||r.opacity,s.fontSize=s.fontSize||r.fontSize,(o=i.getRGBA(s.color)).pop(),o.push(s.opacity),s.color="rgba("+o.join()+")"),n=0;n<d.length;n++)d[n]instanceof fabric.Text||d[n]instanceof fabric.PathGroup||d[n]instanceof fabric.Triangle?((s.color||s.opacity)&&d[n].set({fill:s.color}),s.fontSize&&d[n].set({fontSize:s.fontSize})):(d[n]instanceof fabric.Path||d[n]instanceof fabric.Line)&&(l instanceof fabric.Group?(s.color||s.opacity)&&d[n].set({stroke:s.color}):((s.color||s.opacity)&&d[n].set({stroke:s.color}),s.width&&d[n].set({strokeWidth:s.width})));t||(r=JSON.stringify(i.deepMerge(l.saveState()._stateProperties,{cfg:{color:s.color,width:s.width,opacity:s.opacity}})),l.recentState=r,i.drawing.redos=[],i.drawing.undos.push({action:"modified",target:l,state:r})),i.setup.fabric.renderAll()}},text:function(e){var t=i.deepMerge({text:i.i18l("menu.label.draw.text"),top:i.setup.fabric.height/2,left:i.setup.fabric.width/2,fontSize:i.drawing.fontSize,fontFamily:i.setup.chart.fontFamily||"Verdana",fill:i.drawing.color},e||{});t.click=function(){};var a=new fabric.IText(t.text,t);return i.drawing.buffer.isDrawing=!0,i.setup.fabric.add(a),i.setup.fabric.setActiveObject(a),a.selectAll(),a.enterEditing(),a},line:function(e){var t,a,r,n,o=i.deepMerge({x1:i.setup.fabric.width/2-i.setup.fabric.width/10,x2:i.setup.fabric.width/2+i.setup.fabric.width/10,y1:i.setup.fabric.height/2,y2:i.setup.fabric.height/2,angle:90,strokeLineCap:i.drawing.lineCap,arrow:i.drawing.arrow,color:i.drawing.color,width:i.drawing.width,group:[]},e||{}),s=new fabric.Line([o.x1,o.y1,o.x2,o.y2],{stroke:o.color,strokeWidth:o.width,strokeLineCap:o.strokeLineCap});if(o.group.push(s),o.arrow&&(o.angle=o.angle?o.angle:i.getAngle(o.x1,o.y1,o.x2,o.y2),"start"==o.arrow?(r=o.y1+o.width/2,n=o.x1+o.width/2):"middle"==o.arrow?(r=o.y2+o.width/2-(o.y2-o.y1)/2,n=o.x2+o.width/2-(o.x2-o.x1)/2):(r=o.y2+o.width/2,n=o.x2+o.width/2),a=new fabric.Triangle({top:r,left:n,fill:o.color,height:7*o.width,width:7*o.width,angle:o.angle,originX:"center",originY:"bottom"}),o.group.push(a)),i.drawing.buffer.isDrawing=!0,"config"!=o.action){if(o.arrow){var l=new fabric.Group(o.group);return l.set({cfg:o,fill:o.color,action:o.action,selectable:!0,known:"change"==o.action}),"change"==o.action&&i.setup.fabric.setActiveObject(l),i.setup.fabric.add(l),l}return i.setup.fabric.add(s),s}for(t=0;t<o.group.length;t++)o.group[t].ignoreUndo=!0,i.setup.fabric.add(o.group[t]);return o}}},defaults:{position:"top-right",fileName:"amCharts",action:"download",overflow:!0,path:(e.path||"")+"plugins/export/",formats:{JPG:{mimeType:"image/jpg",extension:"jpg",capture:!0},PNG:{mimeType:"image/png",extension:"png",capture:!0},SVG:{mimeType:"text/xml",extension:"svg",capture:!0},PDF:{mimeType:"application/pdf",extension:"pdf",capture:!0},CSV:{mimeType:"text/plain",extension:"csv"},JSON:{mimeType:"text/plain",extension:"json"},XLSX:{mimeType:"application/octet-stream",extension:"xlsx"}},fabric:{backgroundColor:"#FFFFFF",removeImages:!0,forceRemoveImages:!1,selection:!1,loadTimeout:5e3,drawing:{enabled:!0,arrow:"end",lineCap:"butt",mode:"pencil",modes:["pencil","line","arrow"],color:"#000000",colors:["#000000","#FFFFFF","#FF0000","#00FF00","#0000FF"],shapes:["11.svg","14.svg","16.svg","17.svg","20.svg","27.svg"],width:1,fontSize:11,widths:[1,5,10,15],opacity:1,opacities:[1,.8,.6,.4,.2],menu:void 0,autoClose:!0},border:{fill:"",fillOpacity:0,stroke:"#000000",strokeWidth:1,strokeOpacity:1}},pdfMake:{images:{},pageOrientation:"portrait",pageMargins:40,pageOrigin:!0,pageSize:"A4",pageSizes:{"4A0":[4767.87,6740.79],"2A0":[3370.39,4767.87],A0:[2383.94,3370.39],A1:[1683.78,2383.94],A2:[1190.55,1683.78],A3:[841.89,1190.55],A4:[595.28,841.89],A5:[419.53,595.28],A6:[297.64,419.53],A7:[209.76,297.64],A8:[147.4,209.76],A9:[104.88,147.4],A10:[73.7,104.88],B0:[2834.65,4008.19],B1:[2004.09,2834.65],B2:[1417.32,2004.09],B3:[1000.63,1417.32],B4:[708.66,1000.63],B5:[498.9,708.66],B6:[354.33,498.9],B7:[249.45,354.33],B8:[175.75,249.45],B9:[124.72,175.75],B10:[87.87,124.72],C0:[2599.37,3676.54],C1:[1836.85,2599.37],C2:[1298.27,1836.85],C3:[918.43,1298.27],C4:[649.13,918.43],C5:[459.21,649.13],C6:[323.15,459.21],C7:[229.61,323.15],C8:[161.57,229.61],C9:[113.39,161.57],C10:[79.37,113.39],RA0:[2437.8,3458.27],RA1:[1729.13,2437.8],RA2:[1218.9,1729.13],RA3:[864.57,1218.9],RA4:[609.45,864.57],SRA0:[2551.18,3628.35],SRA1:[1814.17,2551.18],SRA2:[1275.59,1814.17],SRA3:[907.09,1275.59],SRA4:[637.8,907.09],EXECUTIVE:[521.86,756],FOLIO:[612,936],LEGAL:[612,1008],LETTER:[612,792],TABLOID:[792,1224]}},menu:void 0,divId:null,menuReviver:null,menuWalker:null,fallback:!0,keyListener:!0,fileListener:!0,compress:!0,debug:!1},listenersToRemove:[],i18l:function(e,t){var a=t||(i.setup.chart.language?i.setup.chart.language:"en");return(AmCharts.translations[i.name][a]||AmCharts.translations[i.name].en)[e]||e},download:function(e,t,a){if(window.saveAs&&i.setup.hasBlob)i.toBlob({data:e,type:t},function(e){saveAs(e,a)});else if(i.config.fallback&&"text/plain"==t){var r=document.createElement("div"),n=document.createElement("div"),o=document.createElement("textarea");n.innerHTML=i.i18l("fallback.save.text"),r.appendChild(n),r.appendChild(o),n.setAttribute("class","amcharts-export-fallback-message"),r.setAttribute("class","amcharts-export-fallback"),i.setup.chart.containerDiv.appendChild(r),o.setAttribute("readonly",""),o.value=e,o.focus(),o.select(),i.createMenu([{class:"export-main export-close",label:"Done",click:function(){i.createMenu(i.config.menu),i.isElement(i.setup.chart.containerDiv)&&i.setup.chart.containerDiv.removeChild(r)}}])}else{if(!i.config.fallback||"image"!=t.split("/")[0])throw new Error("Unable to create file. Ensure saveAs (FileSaver.js) is supported.");var r=document.createElement("div"),n=document.createElement("div"),s=i.toImage({data:e});n.innerHTML=i.i18l("fallback.save.image"),r.appendChild(n),r.appendChild(s),n.setAttribute("class","amcharts-export-fallback-message"),r.setAttribute("class","amcharts-export-fallback"),i.setup.chart.containerDiv.appendChild(r),i.createMenu([{class:"export-main export-close",label:"Done",click:function(){i.createMenu(i.config.menu),i.isElement(i.setup.chart.containerDiv)&&i.setup.chart.containerDiv.removeChild(r)}}])}return e},loadResource:function(e,t){function a(){i.handleLog(["amCharts[export]: Loading error on ",this.src||this.href].join(""))}function r(){if(t)for(n=0;n<t.length;n++)i.loadResource(t[n])}var n,o,s,l=-1!=e.indexOf("//")?e:[i.libs.path,e].join("");for(-1!=e.indexOf(".js")?((s=document.createElement("script")).setAttribute("type","text/javascript"),s.setAttribute("src",l),i.libs.async&&s.setAttribute("async","")):-1!=e.indexOf(".css")&&((s=document.createElement("link")).setAttribute("type","text/css"),s.setAttribute("rel","stylesheet"),s.setAttribute("href",l)),n=0;n<document.head.childNodes.length;n++)if(p=document.head.childNodes[n],c=!!p&&(p.src||p.href),!!p&&p.tagName,p&&c&&-1!=c.indexOf(e)){i.libs.reload&&document.head.removeChild(p),o=!0;break}for(n in i.libs.namespaces){var d=i.libs.namespaces[n],c=e.toLowerCase(),p=n.toLowerCase();if(-1!=c.indexOf(p)){if(i.setup.isIE&&i.setup.IEversion<=9&&i.libs.unsupportedIE9libs&&-1!=i.libs.unsupportedIE9libs.indexOf(p))return;if(void 0!==window[d]){o=!0;break}}}o&&!i.libs.reload||(s.addEventListener("load",r),i.addListenerToRemove("load",s,r),s.addEventListener("error",a),i.addListenerToRemove("error",s,a),document.head.appendChild(s))},addListenerToRemove:function(e,t,a){i.listenersToRemove.push({node:t,method:a,event:e})},loadDependencies:function(){var e,t;if(i.libs.autoLoad)for(e=0;e<i.libs.resources.length;e++)if(i.libs.resources[e]instanceof Object)for(t in i.libs.resources[e])i.loadResource(t,i.libs.resources[e][t]);else i.loadResource(i.libs.resources[e])},pxToNumber:function(e,t){if(e||!t)return Number(String(e).replace("px",""))||0},numberToPx:function(e){return String(e)+"px"},cloneObject:function(e){var t,a,r,n,o;t=Array.isArray(e)?[]:{};for(r in e)n="object"==typeof(a=e[r]),o=a instanceof Date,t[r]=n&&!o?i.cloneObject(a):a;return t},deepMerge:function(e,t,a){var r,n,o=t instanceof Array?"array":"object";if(!(e instanceof Object||e instanceof Array))return e;for(r in t)"array"==o&&isNaN(r)||(n=t[r],(e&&void 0==e[r]||a)&&(n instanceof Array?e[r]=new Array:n instanceof Function?e[r]=function(){}:n instanceof Date?e[r]=new Date:n instanceof Object?e[r]=new Object:n instanceof Number?e[r]=new Number:n instanceof String&&(e[r]=new String)),(n instanceof Object||n instanceof Array)&&!(n instanceof Function||n instanceof Date||i.isElement(n))&&"chart"!=r&&"scope"!=r?i.deepMerge(e[r],n,a):e instanceof Array&&!a?e.push(n):e&&(e[r]=n));return e},isElement:function(e){return e instanceof Object&&e&&1===e.nodeType},isHashbanged:function(e){var t=String(e).replace(/\"/g,"");return"url"==t.slice(0,3)&&t.slice(t.indexOf("#")+1,t.length-1)},isPressed:function(e){return"mousemove"==e.type&&1===e.which||("touchmove"==e.type||1===e.buttons||1===e.button||1===e.which?i.drawing.buffer.isPressed=!0:i.drawing.buffer.isPressed=!1),i.drawing.buffer.isPressed},removeImage:function(e){if(e){if(i.config.fabric.forceRemoveImages)return!0;if(i.config.fabric.removeImages&&i.isTainted(e))return!0;if(i.setup.isIE&&(10==i.setup.IEversion||11==i.setup.IEversion)&&-1!=e.toLowerCase().indexOf(".svg"))return!0}return!1},isTainted:function(e){var t=String(window.location.origin||window.location.protocol+"//"+window.location.hostname+(window.location.port?":"+window.location.port:""));if(e){if(-1!=t.indexOf(":\\")||-1!=e.indexOf(":\\")||-1!=t.indexOf("file://")||-1!=e.indexOf("file://"))return!0;if(-1!=e.indexOf("//")&&-1==e.indexOf(t.replace(/.*:/,"")))return!0}return!1},isSupported:function(){return!(!i.config.enabled||i.setup.isIE&&i.setup.IEversion<=9&&(!Array.prototype.indexOf||!document.head||!1===i.config.fallback))},getAngle:function(e,t,a,i){var r=a-e,n=i-t;return 180*(0==r?0==n?0:n>0?Math.PI/2:3*Math.PI/2:0==n?r>0?0:Math.PI:r<0?Math.atan(n/r)+Math.PI:n<0?Math.atan(n/r)+2*Math.PI:Math.atan(n/r))/Math.PI},gatherAttribute:function(e,t,a,r){var n,r=r||0,a=a||3;return e&&!(n=e.getAttribute(t))&&r<a?i.gatherAttribute(e.parentNode,t,a,r+1):n},gatherClassName:function(e,t,a,r){var n,r=r||0,a=a||3;if(i.isElement(e)){if(!(n=-1!=(e.getAttribute("class")||"").split(" ").indexOf(t))&&r<a)return i.gatherClassName(e.parentNode,t,a,r+1);n&&(n=e)}return n},gatherElements:function(e,t,a){var r,n;for(r=0;r<e.children.length;r++){var o=e.children[r];if("clipPath"==o.tagName){var s={},l=fabric.parseTransformAttribute(i.gatherAttribute(o,"transform"));for(n=0;n<o.childNodes.length;n++)o.childNodes[n].setAttribute("fill","transparent"),s={x:i.pxToNumber(o.childNodes[n].getAttribute("x")),y:i.pxToNumber(o.childNodes[n].getAttribute("y")),width:i.pxToNumber(o.childNodes[n].getAttribute("width")),height:i.pxToNumber(o.childNodes[n].getAttribute("height"))};e.clippings[o.id]={svg:o,bbox:s,transform:l}}else if("pattern"==o.tagName){var d={node:o,source:o.getAttribute("xlink:href"),width:Number(o.getAttribute("width")),height:Number(o.getAttribute("height")),repeat:"repeat",offsetX:0,offsetY:0};for(n=0;n<o.childNodes.length;n++)"rect"==o.childNodes[n].tagName?d.fill=o.childNodes[n].getAttribute("fill"):"image"==o.childNodes[n].tagName&&(c=fabric.parseAttributes(o.childNodes[n],fabric.SHARED_ATTRIBUTES)).transformMatrix&&(d.offsetX=c.transformMatrix[4],d.offsetY=c.transformMatrix[5]);i.removeImage(d.source)?e.patterns[o.id]=d.fill?d.fill:"transparent":e.patterns[d.node.id]=d}else if("image"==o.tagName)a.included++,fabric.Image.fromURL(o.getAttribute("xlink:href"),function(e){a.loaded++});else{var c=["fill","stroke"];for(n=0;n<c.length;n++){var p=c[n],f=o.getAttribute(p),u=i.getRGBA(f),g=i.isHashbanged(f);!f||u||g||(o.setAttribute(p,"none"),o.setAttribute(p+"-opacity","0"))}}}return e},getRGBA:function(e,t){return!("none"==e||"transparent"==e||i.isHashbanged(e)||!(e=new fabric.Color(e))._source)&&(t?e:e.getSource())},gatherPosition:function(e,t){var a,r=i.drawing.buffer.position,n=fabric.util.invertTransform(i.setup.fabric.viewportTransform);return"touchmove"==e.type&&("touches"in e?e=e.touches[0]:"changedTouches"in e&&(e=e.changedTouches[0])),a=fabric.util.transformPoint(i.setup.fabric.getPointer(e,!0),n),1==t&&(r.x1=a.x,r.y1=a.y),r.x2=a.x,r.y2=a.y,r.xD=r.x1-r.x2<0?-1*(r.x1-r.x2):r.x1-r.x2,r.yD=r.y1-r.y2<0?-1*(r.y1-r.y2):r.y1-r.y2,r},modifyFabric:function(){fabric.ElementsParser.prototype.resolveMega=function(e,t){var a=e.get(t);if(/^url\(/.test(a)){var r=a.slice(a.indexOf("#")+1,a.length-1);if(fabric.gradientDefs[this.svgUid][r]){var n=fabric.Mega.fromElement(fabric.gradientDefs[this.svgUid][r],e);n.coords.y1&&"pie"!=i.setup.chart.type&&(n.coords.y2=-1*n.coords.y1,n.coords.y1=0),e.set(t,n)}}},fabric.Text.fromElement=function(e,t){if(!e)return null;var a=fabric.parseAttributes(e,fabric.Text.ATTRIBUTE_NAMES);(t=fabric.util.object.extend(t?fabric.util.object.clone(t):{},a)).top=t.top||0,t.left=t.left||0,"dx"in a&&(t.left+=a.dx),"dy"in a&&(t.top+=a.dy),"fontSize"in t||(t.fontSize=fabric.Text.DEFAULT_SVG_FONT_SIZE),t.originX||(t.originX="left");var i="",r=[];if("textContent"in e)if(e.childNodes)for(var n=0;n<e.childNodes.length;n++)r.push(e.childNodes[n].textContent);else r.push(e.textContent);else"firstChild"in e&&null!==e.firstChild&&"data"in e.firstChild&&null!==e.firstChild.data&&r.push(e.firstChild.data);i=r.join("\n");var o=new fabric.Text(i,t),s=0;return"left"===o.originX&&(s=o.getWidth()/2),"right"===o.originX&&(s=-o.getWidth()/2),r.length>1?o.set({left:o.getLeft()+s,top:o.getTop()+o.fontSize*(r.length-1)*(.18+o._fontSizeFraction),textAlign:t.originX,lineHeight:r.length>1?.965:1.16}):o.set({left:o.getLeft()+s,top:o.getTop()-o.getHeight()/2+o.fontSize*(.18+o._fontSizeFraction)}),o}},capture:function(e,t){var a,r=i.deepMerge(i.deepMerge({},i.config.fabric),e||{}),n=[],o={x:0,y:0,pX:0,pY:0,lX:0,lY:0,width:i.setup.chart.divRealWidth,height:i.setup.chart.divRealHeight},s={loaded:0,included:0},l={items:[],width:0,height:0,maxWidth:0,maxHeight:0};if(!i.handleNamespace("fabric",{scope:this,cb:i.capture,args:arguments}))return!1;i.modifyFabric(),i.handleCallback(r.beforeCapture,r);var d=i.setup.chart.containerDiv.getElementsByTagName("svg");for(a=0;a<d.length;a++)(p={svg:d[a],parent:d[a].parentNode,children:d[a].getElementsByTagName("*"),offset:{x:0,y:0},patterns:{},clippings:{},has:{legend:!1,panel:!1,scrollbar:!1}}).has.legend=i.gatherClassName(p.parent,i.setup.chart.classNamePrefix+"-legend-div",1),p.has.panel=i.gatherClassName(p.parent,i.setup.chart.classNamePrefix+"-stock-panel-div"),p.has.scrollbar=i.gatherClassName(p.parent,i.setup.chart.classNamePrefix+"-scrollbar-chart-div"),p=i.gatherElements(p,r,s),n.push(p);if(i.config.legend){if("stock"==i.setup.chart.type)for(a=0;a<i.setup.chart.panels.length;a++)i.setup.chart.panels[a].stockLegend&&i.setup.chart.panels[a].stockLegend.divId&&l.items.push(i.setup.chart.panels[a].stockLegend);else i.setup.chart.legend&&i.setup.chart.legend.divId&&l.items.push(i.setup.chart.legend);for(a=0;a<l.items.length;a++){var c=l.items[a],p={svg:c.container.container,parent:c.container.container.parentNode,children:c.container.container.getElementsByTagName("*"),offset:{x:0,y:0},legend:{id:a,type:-1!=["top","left"].indexOf(i.config.legend.position)?"unshift":"push",position:i.config.legend.position,width:i.config.legend.width?i.config.legend.width:c.container.div.offsetWidth,height:i.config.legend.height?i.config.legend.height:c.container.div.offsetHeight},patterns:{},clippings:{},has:{legend:!1,panel:!1,scrollbar:!1}};l.width+=p.legend.width,l.height+=p.legend.height,l.maxWidth=p.legend.width>l.maxWidth?p.legend.width:l.maxWidth,l.maxHeight=p.legend.height>l.maxHeight?p.legend.height:l.maxHeight,p=i.gatherElements(p,r,s),n[p.legend.type](p)}-1!=["top","bottom"].indexOf(i.config.legend.position)?(o.width=l.maxWidth>o.width?l.maxWidth:o.width,o.height+=l.height):-1!=["left","right"].indexOf(i.config.legend.position)?(o.width+=l.maxWidth,o.height=l.height>o.height?l.height:o.height):(o.height+=l.height,o.width+=l.maxWidth)}if(i.drawing.enabled=r.drawing.enabled="draw"==r.action,i.drawing.buffer.enabled=i.drawing.enabled,i.setup.wrapper=document.createElement("div"),i.setup.wrapper.setAttribute("class",i.setup.chart.classNamePrefix+"-export-canvas"),i.setup.chart.containerDiv.appendChild(i.setup.wrapper),"stock"==i.setup.chart.type){var f={top:0,right:0,bottom:0,left:0};i.setup.chart.leftContainer&&(o.width-=i.setup.chart.leftContainer.offsetWidth,f.left=i.setup.chart.leftContainer.offsetWidth+2*i.setup.chart.panelsSettings.panelSpacing),i.setup.chart.rightContainer&&(o.width-=i.setup.chart.rightContainer.offsetWidth,f.right=i.setup.chart.rightContainer.offsetWidth+2*i.setup.chart.panelsSettings.panelSpacing),i.setup.chart.periodSelector&&-1!=["top","bottom"].indexOf(i.setup.chart.periodSelector.position)&&(o.height-=i.setup.chart.periodSelector.offsetHeight+i.setup.chart.panelsSettings.panelSpacing,f[i.setup.chart.periodSelector.position]+=i.setup.chart.periodSelector.offsetHeight+i.setup.chart.panelsSettings.panelSpacing),i.setup.chart.dataSetSelector&&-1!=["top","bottom"].indexOf(i.setup.chart.dataSetSelector.position)&&(o.height-=i.setup.chart.dataSetSelector.offsetHeight,f[i.setup.chart.dataSetSelector.position]+=i.setup.chart.dataSetSelector.offsetHeight),i.setup.wrapper.style.paddingTop=i.numberToPx(f.top),i.setup.wrapper.style.paddingRight=i.numberToPx(f.right),i.setup.wrapper.style.paddingBottom=i.numberToPx(f.bottom),i.setup.wrapper.style.paddingLeft=i.numberToPx(f.left)}i.setup.canvas=document.createElement("canvas"),i.setup.wrapper.appendChild(i.setup.canvas);var u=i.removeFunctionsFromObject(i.deepMerge({width:o.width,height:o.height,isDrawingMode:!0},r));for(i.setup.fabric=new fabric.Canvas(i.setup.canvas,u),i.deepMerge(i.setup.fabric,r),i.deepMerge(i.setup.fabric.freeDrawingBrush,r.drawing),i.deepMerge(i.drawing,r.drawing),i.drawing.handler.change(r.drawing),i.setup.fabric.on("mouse:down",function(e){i.gatherPosition(e.e,1),i.drawing.buffer.pressedTS=Number(new Date),i.isPressed(e.e),i.drawing.buffer.isDrawing=!1,i.drawing.buffer.isDrawingTimer=setTimeout(function(){i.drawing.buffer.isSelected||(i.drawing.buffer.isDrawing=!0)},200)}),i.setup.fabric.on("mouse:move",function(e){var t=i.gatherPosition(e.e,2);if(i.isPressed(e.e),i.drawing.buffer.isPressed&&!i.drawing.buffer.isSelected&&(i.drawing.buffer.isDrawing=!0,!i.drawing.buffer.line&&"pencil"!=i.drawing.mode&&(t.xD>5||t.yD>5)&&(i.setup.fabric.isDrawingMode=!1,i.setup.fabric._isCurrentlyDrawing=!1,i.drawing.buffer.ignoreUndoOnMouseUp=!0,i.setup.fabric.freeDrawingBrush.onMouseUp(),i.setup.fabric.remove(i.setup.fabric._objects.pop()),i.drawing.buffer.line=i.drawing.handler.line({x1:t.x1,y1:t.y1,x2:t.x2,y2:t.y2,arrow:"line"!=i.drawing.mode&&i.drawing.arrow,action:"config"}))),i.drawing.buffer.isSelected&&(i.setup.fabric.isDrawingMode=!1),i.drawing.buffer.line){var r,n,o,s=i.drawing.buffer.line;for(s.x2=t.x2,s.y2=t.y2,a=0;a<s.group.length;a++)(r=s.group[a])instanceof fabric.Line?r.set({x2:s.x2,y2:s.y2}):r instanceof fabric.Triangle&&(s.angle=i.getAngle(s.x1,s.y1,s.x2,s.y2)+90,"start"==s.arrow?(n=s.y1+s.width/2,o=s.x1+s.width/2):"middle"==s.arrow?(n=s.y2+s.width/2-(s.y2-s.y1)/2,o=s.x2+s.width/2-(s.x2-s.x1)/2):(n=s.y2+s.width/2,o=s.x2+s.width/2),r.set({top:n,left:o,angle:s.angle}));i.setup.fabric.renderAll()}}),i.setup.fabric.on("mouse:up",function(e){if(!i.drawing.buffer.isDrawing){var t=i.setup.fabric.findTarget(e.e);t&&t.selectable&&i.setup.fabric.setActiveObject(t)}if(i.drawing.buffer.line){for(a=0;a<i.drawing.buffer.line.group.length;a++)i.drawing.buffer.line.group[a].remove();delete i.drawing.buffer.line.action,delete i.drawing.buffer.line.group,i.drawing.handler.line(i.drawing.buffer.line)}i.drawing.buffer.line=!1,i.drawing.buffer.hasLine=!1,i.drawing.buffer.isPressed=!1,clearTimeout(i.drawing.buffer.isDrawingTimer),i.drawing.buffer.isDrawing=!1}),i.setup.fabric.on("object:selected",function(e){i.drawing.buffer.isSelected=!0,i.drawing.buffer.target=e.target,i.setup.fabric.isDrawingMode=!1}),i.setup.fabric.on("selection:cleared",function(e){i.drawing.buffer.target=!1,i.drawing.buffer.isSelected&&(i.setup.fabric._isCurrentlyDrawing=!1),i.drawing.buffer.isSelected=!1,i.setup.fabric.isDrawingMode=!0}),i.setup.fabric.on("path:created",function(e){var t=e.path;if(!i.drawing.buffer.isDrawing||i.drawing.buffer.hasLine)return i.setup.fabric.remove(t),void i.setup.fabric.renderAll()}),i.setup.fabric.on("object:added",function(e){var t=e.target,a=i.deepMerge(t.saveState()._stateProperties,{cfg:{color:i.drawing.color,width:i.drawing.width,opacity:i.drawing.opacity,fontSize:i.drawing.fontSize}});a=JSON.stringify(a),t.recentState=a,!i.drawing.buffer.ignoreUndoOnMouseUp&&i.drawing.buffer.isDrawing?(!t.selectable||t.known||t.ignoreUndo||(t.isAnnotation=!0,i.drawing.undos.push({action:"added",target:t,state:a}),i.drawing.redos=[]),t.known=!0,i.setup.fabric.isDrawingMode=!0):i.drawing.buffer.ignoreUndoOnMouseUp=!1}),i.setup.fabric.on("object:modified",function(e){var t=e.target,a=JSON.parse(t.recentState),r=i.deepMerge(t.saveState()._stateProperties,{cfg:a.cfg});r=JSON.stringify(r),t.recentState=r,i.drawing.undos.push({action:"modified",target:t,state:r}),i.drawing.redos=[]}),i.setup.fabric.on("text:changed",function(e){var t=e.target;clearTimeout(t.timer),t.timer=setTimeout(function(){var e=JSON.stringify(t.saveState()._stateProperties);t.recentState=e,i.drawing.redos=[],i.drawing.undos.push({action:"modified",target:t,state:e})},250)}),i.drawing.enabled?(i.setup.wrapper.setAttribute("class",i.setup.chart.classNamePrefix+"-export-canvas active"),i.setup.wrapper.style.backgroundColor=r.backgroundColor,i.setup.wrapper.style.display="block"):(i.setup.wrapper.setAttribute("class",i.setup.chart.classNamePrefix+"-export-canvas"),i.setup.wrapper.style.display="none"),a=0;a<n.length;a++)p=n[a],"stock"==i.setup.chart.type&&i.setup.chart.legendSettings.position?-1!=["top","bottom"].indexOf(i.setup.chart.legendSettings.position)?p.parent.style.top&&p.parent.style.left?(p.offset.y=i.pxToNumber(p.parent.style.top),p.offset.x=i.pxToNumber(p.parent.style.left)):(p.offset.x=o.x,p.offset.y=o.y,o.y+=i.pxToNumber(p.parent.style.height),p.has.panel?(o.pY=i.pxToNumber(p.has.panel.style.marginTop),p.offset.y+=o.pY):p.has.scrollbar&&(p.offset.y+=o.pY)):-1!=["left","right"].indexOf(i.setup.chart.legendSettings.position)&&(p.offset.y=i.pxToNumber(p.parent.style.top)+o.pY,p.offset.x=i.pxToNumber(p.parent.style.left)+o.pX,p.has.legend?o.pY+=i.pxToNumber(p.has.panel.style.height)+i.setup.chart.panelsSettings.panelSpacing:p.has.scrollbar&&(p.offset.y-=i.setup.chart.panelsSettings.panelSpacing)):("absolute"==p.parent.style.position?(p.offset.absolute=!0,p.offset.top=i.pxToNumber(p.parent.style.top),p.offset.right=i.pxToNumber(p.parent.style.right,!0),p.offset.bottom=i.pxToNumber(p.parent.style.bottom,!0),p.offset.left=i.pxToNumber(p.parent.style.left),p.offset.width=i.pxToNumber(p.parent.style.width),p.offset.height=i.pxToNumber(p.parent.style.height)):p.parent.style.top&&p.parent.style.left?(p.offset.y=i.pxToNumber(p.parent.style.top),p.offset.x=i.pxToNumber(p.parent.style.left)):p.legend?("left"==p.legend.position?o.x=l.maxWidth:"right"==p.legend.position?p.offset.x=o.width-l.maxWidth:"top"==p.legend.position?o.y+=p.legend.height:"bottom"==p.legend.position&&(p.offset.y=o.height-l.height),p.offset.y+=o.lY,o.lY+=p.legend.height):(p.offset.x=o.x,p.offset.y=o.y+o.pY,o.y+=i.pxToNumber(p.parent.style.height)),p.has.legend&&p.has.panel&&p.has.panel.style.marginTop?(o.y+=i.pxToNumber(p.has.panel.style.marginTop),p.offset.y+=i.pxToNumber(p.has.panel.style.marginTop)):i.setup.chart.legend&&-1!=["left","right"].indexOf(i.setup.chart.legend.position)&&(p.offset.y=i.pxToNumber(p.parent.style.top),p.offset.x=i.pxToNumber(p.parent.style.left))),fabric.parseSVGDocument(p.svg,function(e){return function(a,l){var d,c=fabric.util.groupSVGElements(a,l),p=[],f={selectable:!1,isCoreElement:!0};for(e.offset.absolute?(void 0!==e.offset.bottom?f.top=o.height-e.offset.height-e.offset.bottom:f.top=e.offset.top,void 0!==e.offset.right?f.left=o.width-e.offset.width-e.offset.right:f.left=e.offset.left):(f.top=e.offset.y,f.left=e.offset.x),d=0;d<c.paths.length;d++){var u=null;if(c.paths[d]){if(i.removeImage(c.paths[d]["xlink:href"]))continue;if(c.paths[d].fill instanceof Object)"radial"==c.paths[d].fill.type&&-1==["pie","gauge"].indexOf(i.setup.chart.type)&&(c.paths[d].fill.coords.r2=-1*c.paths[d].fill.coords.r1,c.paths[d].fill.coords.r1=0,c.paths[d].set({opacity:c.paths[d].fillOpacity}));else if((u=i.isHashbanged(c.paths[d].fill))&&e.patterns&&e.patterns[u]){var g=e.patterns[u];s.included++,fabric.Image.fromURL(g.source,function(e,t){return function(a){s.loaded++,a.set({top:e.offsetY,left:e.offsetX,width:e.width,height:e.height}),i.setup.fabric._isRetinaScaling()&&a.set({top:e.offsetY/2,left:e.offsetX/2,scaleX:.5,scaleY:.5});var r=new fabric.StaticCanvas(void 0,{backgroundColor:e.fill,width:a.getWidth(),height:a.getHeight()});r.add(a);var n=new fabric.Pattern({source:r.getElement(),offsetX:c.paths[t].width/2,offsetY:c.paths[t].height/2,repeat:"repeat"});c.paths[t].set({fill:n,opacity:c.paths[t].fillOpacity})}}(g,d))}(u=i.isHashbanged(c.paths[d].clipPath))&&e.clippings&&e.clippings[u]&&(function(t,a){var i=c.paths[t].toSVG;c.paths[t].toSVG=function(t){return i.apply(this,[function(i){return t(i,e.clippings[a])}])}}(d,u),c.paths[d].set({clipTo:function(t,a){return function(r){var n=e.clippings[a],o=this.transformMatrix||[1,0,0,1,0,0],s={top:n.bbox.y,left:n.bbox.x,width:n.bbox.width,height:n.bbox.height};"map"==i.setup.chart.type&&(s.top+=n.transform[5],s.left+=n.transform[4]),n.bbox.x&&o[4]&&n.bbox.y&&o[5]&&(s.top-=o[5],s.left-=o[4]),void 0!==i.setup.chart.smoothCustomBullets&&this.className==i.setup.chart.classNamePrefix+"-graph-bullet"&&"image"==c.paths[t].svg.tagName?(radius=n.svg.firstChild.rx.baseVal.value/2+2,r.beginPath(),r.moveTo(s.left+radius,s.top),r.lineTo(s.left+s.width-radius,s.top),r.quadraticCurveTo(s.left+s.width,s.top,s.left+s.width,s.top+radius),r.lineTo(s.left+s.width,s.top+s.height-radius),r.quadraticCurveTo(s.left+s.width,s.top+s.height,s.left+s.width-radius,s.top+s.height),r.lineTo(s.left+radius,s.top+s.height),r.quadraticCurveTo(s.left,s.top+s.height,s.left,s.top+s.height-radius),r.lineTo(s.left,s.top+radius),r.quadraticCurveTo(s.left,s.top,s.left+radius,s.top),r.closePath()):r.rect(s.left,s.top,s.width,s.height)}}(d,u)}))}p.push(c.paths[d])}if(c.paths=p,c.set(f),i.setup.fabric.add(c),e.svg.parentNode&&e.svg.parentNode.getElementsByTagName){var h=e.svg.parentNode.getElementsByClassName(i.setup.chart.classNamePrefix+"-balloon-div");for(d=0;d<h.length;d++)if(r.balloonFunction instanceof Function)r.balloonFunction.apply(i,[h[d],e]);else{var m=h[d],b=fabric.parseStyleAttribute(m),v=fabric.parseStyleAttribute(m.childNodes[0]),w=new fabric.Text(m.innerText||m.textContent||m.innerHTML,{selectable:!1,top:i.pxToNumber(b.top)+e.offset.y,left:i.pxToNumber(b.left)+e.offset.x,fill:v.color,fontSize:i.pxToNumber(v.fontSize||v["font-size"]),fontFamily:v.fontFamily||v["font-family"],textAlign:v["text-align"],isCoreElement:!0});i.setup.fabric.add(w)}}if(e.svg.nextSibling&&"A"==e.svg.nextSibling.tagName){var m=e.svg.nextSibling,b=fabric.parseStyleAttribute(m),w=new fabric.Text(m.innerText||m.textContent||m.innerHTML,{selectable:!1,top:i.pxToNumber(b.top)+e.offset.y,left:i.pxToNumber(b.left)+e.offset.x,fill:b.color,fontSize:i.pxToNumber(b.fontSize||b["font-size"]),fontFamily:b.fontFamily||b["font-family"],opacity:b.opacity,isCoreElement:!0});e.has.scrollbar||i.setup.fabric.add(w)}if(n.pop(),!n.length)var y=Number(new Date),x=setInterval(function(){var e=Number(new Date);(s.loaded==s.included||e-y>i.config.fabric.loadTimeout)&&(clearTimeout(x),i.handleBorder(r),i.handleCallback(r.afterCapture,r),i.setup.fabric.renderAll(),i.handleCallback(t,r))},AmCharts.updateRate)}}(p),function(e,t){var a,n=i.gatherAttribute(e,"class"),o=i.gatherAttribute(e,"visibility"),s=i.gatherAttribute(e,"clip-path");t.className=String(n),t.classList=String(n).split(" "),t.clipPath=s,t.svg=e;var l=["fill","stroke"];for(a=0;a<l.length;a++){var d=l[a],c=String(e.getAttribute(d)||"none"),p=Number(e.getAttribute(d+"-opacity")||"1"),f=i.getRGBA(c);"hidden"==o&&(t.opacity=0,p=0),f&&(f.pop(),f.push(p),t[d]="rgba("+f.join()+")",t[d+i.capitalize("opacity")]=p)}i.handleCallback(r.reviver,t,e)})},toCanvas:function(e,t){var a=i.deepMerge({},e||{}),r=i.setup.canvas;return i.handleCallback(t,r,a),r},toImage:function(e,t){var a=i.deepMerge({format:"png",quality:1,multiplier:i.config.multiplier},e||{}),r=a.data,n=document.createElement("img");return!!i.handleNamespace("fabric",{scope:this,cb:i.toImage,args:arguments})&&(a.data||(r=a.lossless||"svg"==a.format?i.toSVG(i.deepMerge(a,{getBase64:!0})):i.setup.fabric.toDataURL(a)),n.setAttribute("src",r),i.handleCallback(t,n,a),n)},toBlob:function(e,t){var a,r=i.deepMerge({data:"empty",type:"text/plain"},e||{}),n=/^data:.+;base64,(.*)$/.exec(r.data);return n&&(r.data=n[0],r.type=r.data.slice(5,r.data.indexOf(",")-7),r.data=i.toByteArray({data:r.data.slice(r.data.indexOf(",")+1,r.data.length)})),a=r.getByteArray?r.data:new Blob([r.data],{type:r.type}),i.handleCallback(t,a,r),a},toJPG:function(e,t){var a=i.deepMerge({format:"jpeg",quality:1,multiplier:i.config.multiplier},e||{});a.format=a.format.toLowerCase();var r;return/iP(hone|od|ad)/.test(navigator.platform)&&(a.multiplier=1),!!i.handleNamespace("fabric",{scope:this,cb:i.toJPG,args:arguments})&&(r=i.setup.fabric.toDataURL(a),i.handleCallback(t,r,a),r)},toPNG:function(e,t){var a,r=i.deepMerge({format:"png",quality:1,multiplier:i.config.multiplier},e||{});return/iP(hone|od|ad)/.test(navigator.platform)&&(r.multiplier=1),!!i.handleNamespace("fabric",{scope:this,cb:i.toPNG,args:arguments})&&(a=i.setup.fabric.toDataURL(r),i.handleCallback(t,a,r),a)},toSVG:function(e,t){var a,r=[],n=[],o=i.deepMerge({compress:i.config.compress,reviver:function(e,t){var a=new RegExp(/\bstyle=(['"])(.*?)\1/).exec(e)[0].slice(7,-1),o=a.split(";"),s=[];for(i1=0;i1<o.length;i1++)if(o[i1]){var l=o[i1].replace(/\s/g,"").split(":"),d=l[0],c=l[1];if(-1!=["fill","stroke"].indexOf(d))if(c=i.getRGBA(c,!0)){var p="#"+c.toHex(),f=c._source[3];s.push([d,p].join(":")),s.push([d+"-opacity",f].join(":"))}else s.push(o[i1]);else"opactiy"!=d&&s.push(o[i1])}if(e=e.replace(a,s.join(";")),t&&t.svg){var u=t.svg.id,g=2,h=e.slice(-g);"/>"!=h&&(g=3,h=e.slice(-g));var m=e.slice(0,e.length-g),b=' clip-path="url(#'+u+')" ',v=i.gatherAttribute(t.svg,"class");if(v=v?v.split(" "):[],e=-1!=v.indexOf(i.setup.chart.classNamePrefix+"-graph-line")?m+b+h:"<g "+b+">"+e+"</g>",-1==n.indexOf(u)){var w=(new XMLSerializer).serializeToString(t.svg);r.push(w),n.push(u)}}return e}},e||{});if(!i.handleNamespace("fabric",{scope:this,cb:i.toSVG,args:arguments}))return!1;if(a=i.setup.fabric.toSVG(o,o.reviver),r.length){var s=a.slice(0,a.length-6),l=a.slice(-6);a=s+r.join("")+l}return o.compress&&(a=a.replace(/[\t\r\n]+/g,"")),o.getBase64&&(a="data:image/svg+xml;base64,"+btoa(a)),i.handleCallback(t,a,o),a},toPDF:function(e,t){var a,r=i.deepMerge(i.deepMerge({multiplier:i.config.multiplier||2,pageOrigin:void 0===i.config.pageOrigin},i.config.pdfMake),e||{},!0);if(/iP(hone|od|ad)/.test(navigator.platform)&&(r.multiplier=1),!i.handleNamespace("pdfMake",{scope:this,cb:i.toPDF,args:arguments}))return!1;if(r.images.reference=i.toPNG(r),!r.content){var n=[],o=function(e,t){var a=i.defaults.pdfMake.pageSizes[String(e).toUpperCase()].slice();if(!a)throw new Error('The given pageSize "'+e+'" does not exist!');return"landscape"==t&&a.reverse(),a}(r.pageSize,r.pageOrientation),s=function(e){if("number"==typeof e||e instanceof Number)e={left:e,right:e,top:e,bottom:e};else if(e instanceof Array)if(2===e.length)e={left:e[0],top:e[1],right:e[0],bottom:e[1]};else{if(4!==e.length)throw"Invalid pageMargins definition";e={left:e[0],top:e[1],right:e[2],bottom:e[3]}}else e={left:i.defaults.pdfMake.pageMargins,top:i.defaults.pdfMake.pageMargins,right:i.defaults.pdfMake.pageMargins,bottom:i.defaults.pdfMake.pageMargins};return e}(r.pageMargins);o[0]-=s.left+s.right,o[1]-=s.top+s.bottom,r.pageOrigin&&(n.push(i.i18l("label.saved.from")),n.push(window.location.href),o[1]-=28.128),n.push({image:"reference",fit:o}),r.content=n}return a=new pdfMake.createPdf(r),t&&a.getDataUrl(function(e){return function(t){e.apply(i,arguments)}}(t)),a},toPRINT:function(e,t){var a,r=i.deepMerge({delay:1,lossless:!1},e||{}),n=i.toImage(r),o=[],s=document.body.childNodes,l=document.documentElement.scrollTop||document.body.scrollTop;for(n.setAttribute("style","width: 100%; max-height: 100%;"),a=0;a<s.length;a++)i.isElement(s[a])&&(o[a]=s[a].style.display,s[a].style.display="none");return document.body.appendChild(n),r.delay*=1e3,/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream&&r.delay<1e3&&(r.delay=1e3),setTimeout(function(){window.print(),setTimeout(function(){for(a=0;a<s.length;a++)i.isElement(s[a])&&(s[a].style.display=o[a]);document.body.removeChild(n),document.documentElement.scrollTop=document.body.scrollTop=l,i.handleCallback(t,n,r)},r.delay)},r.delay),n},toJSON:function(e,t){var a=i.deepMerge({dateFormat:i.config.dateFormat||"dateObject"},e||{},!0),r={};return!!i.handleNamespace("JSON",{scope:this,cb:i.toJSON,args:arguments})&&(a.data=void 0!==a.data?a.data:i.getChartData(a),r=JSON.stringify(a.data,void 0,"\t"),i.handleCallback(t,r,a),r)},toCSV:function(e,t){var a,r=i.deepMerge({delimiter:",",quotes:!0,escape:!0,withHeader:!0},e||{},!0),n=[],o="";n=i.toArray(r);for(a in n)isNaN(a)||(o+=n[a].join(r.delimiter)+"\n");return i.handleCallback(t,o,r),o},toXLSX:function(e,t){function a(e,t){return t&&(e+=1462),(Date.parse(e)-60*e.getTimezoneOffset()*1e3-new Date(Date.UTC(1899,11,30)))/864e5}var r=i.deepMerge({name:"amCharts",dateFormat:i.config.dateFormat||"dateObject",withHeader:!0,stringify:!1},e||{},!0),n=[],o="",s={SheetNames:[],Sheets:{}};return!!i.handleNamespace("XLSX",{scope:this,cb:i.toXLSX,args:arguments})&&(n=i.toArray(r),s.SheetNames.push(r.name),s.Sheets[r.name]=function(e,t){for(var i={},r={s:{c:1e7,r:1e7},e:{c:0,r:0}},n=0;n!=e.length;++n)for(var o=0;o!=e[n].length;++o){r.s.r>n&&(r.s.r=n),r.s.c>o&&(r.s.c=o),r.e.r<n&&(r.e.r=n),r.e.c<o&&(r.e.c=o);var s={v:e[n][o]};if(null!=s.v){var l=XLSX.utils.encode_cell({c:o,r:n});"number"==typeof s.v?s.t="n":"boolean"==typeof s.v?s.t="b":s.v instanceof Date?(s.t="n",s.z=XLSX.SSF._table[14],s.v=a(s.v)):s.v instanceof Object?(s.t="s",s.v=JSON.stringify(s.v)):s.t="s",i[l]=s}}return r.s.c<1e7&&(i["!ref"]=XLSX.utils.encode_range(r)),i}(n),o=XLSX.write(s,{bookType:"xlsx",bookSST:!0,type:"base64"}),o="data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,"+o,i.handleCallback(t,o,r),o)},toArray:function(e,t){function a(e){return"string"==typeof e&&(n.escape&&(e=e.replace('"','""')),n.quotes&&(e=['"',e,'"'].join(""))),e}var r,n=i.deepMerge({withHeader:!1,stringify:!0,escape:!1,quotes:!1},e||{},!0),o=[],s=[],l=[],d=i.config.processData;if(n.processData=function(e,t){var a=t.exportFields||Object.keys(t.dataFieldsMap);for(c=0;c<a.length;c++){var r=a[c],n=t.dataFieldsTitlesMap[r];s.push(n)}return d?i.handleCallback(d,e,t):e},n.data=void 0!==n.data?i.processData(n):i.getChartData(n),n.withHeader){l=[];for(c in s)isNaN(c)||l.push(a(s[c]));o.push(l)}for(r in n.data)if(l=[],!isNaN(r)){for(c in s)if(!isNaN(c)){var c=s[c],p=n.data[r][c];p=null==p?"":n.stringify?String(p):p,l.push(a(p))}o.push(l)}return i.handleCallback(t,o,n),o},toByteArray:function(e,t){function a(e){var t=e.charCodeAt(0);return t===o?62:t===s?63:t<l?-1:t<l+10?t-l+26+26:t<c+26?t-c:t<d+26?t-d+26:void 0}var r=i.deepMerge({},e||{}),n="undefined"!=typeof Uint8Array?Uint8Array:Array,o="+".charCodeAt(0),s="/".charCodeAt(0),l="0".charCodeAt(0),d="a".charCodeAt(0),c="A".charCodeAt(0),p=function(e){function t(e){d[p++]=e}var i,r,o,s,l,d;if(e.length%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var c=e.length;l="="===e.charAt(c-2)?2:"="===e.charAt(c-1)?1:0,d=new n(3*e.length/4-l),o=l>0?e.length-4:e.length;var p=0;for(i=0,r=0;i<o;i+=4,r+=3)t((16711680&(s=a(e.charAt(i))<<18|a(e.charAt(i+1))<<12|a(e.charAt(i+2))<<6|a(e.charAt(i+3))))>>16),t((65280&s)>>8),t(255&s);return 2===l?t(255&(s=a(e.charAt(i))<<2|a(e.charAt(i+1))>>4)):1===l&&(t((s=a(e.charAt(i))<<10|a(e.charAt(i+1))<<4|a(e.charAt(i+2))>>2)>>8&255),t(255&s)),d}(r.data);return i.handleCallback(t,p,r),p},removeFunctionsFromObject:function(e){for(var t in e)"function"==typeof e[t]&&delete e[t];return e},handleCallback:function(e){var t,a=Array();if(e&&e instanceof Function){for(t=0;t<arguments.length;t++)t>0&&a.push(arguments[t]);return e.apply(i,a)}},handleLog:function(e){!0===i.config.debug&&console.log(e)},handleNamespace:function(e,t){function a(){var l=Number(new Date);o=!!(e in n),"pdfMake"==e&&o&&(o=n.pdfMake.vfs),o?(clearTimeout(r),t.cb.apply(t.scope,t.args),i.handleLog(['AmCharts [export]: Namespace "',e,'" showed up in: ',String(n)].join(""))):l-s<i.libs.loadTimeout?r=setTimeout(a,250):i.handleLog(['AmCharts [export]: Gave up waiting for "',e,'" in: ',String(n)].join(""))}var r,n=i.config.scope||window,o=!1,s=Number(new Date);return(o=!!(e in n))||(i.handleLog(['AmCharts [export]: Could not find "',e,'" in: ',String(n)].join("")),a()),o},handleBorder:function(e){if(i.config.border instanceof Object){var t=i.deepMerge(i.defaults.fabric.border,e.border||{},!0),a=new fabric.Rect;t.width=i.setup.fabric.width-t.strokeWidth,t.height=i.setup.fabric.height-t.strokeWidth,a.set(t),i.setup.fabric.add(a)}},handleDropbox:function(e){if(i.drawing.enabled)if(e.preventDefault(),e.stopPropagation(),"dragover"==e.type)i.setup.wrapper.setAttribute("class",i.setup.chart.classNamePrefix+"-export-canvas active dropbox");else if(i.setup.wrapper.setAttribute("class",i.setup.chart.classNamePrefix+"-export-canvas active"),"drop"==e.type&&e.dataTransfer.files.length)for(var t=0;t<e.dataTransfer.files.length;t++){var a=new FileReader;a.onloadend=function(t){return function(){i.drawing.handler.add({url:a.result,top:e.layerY-10*t,left:e.layerX-10*t})}}(t),a.readAsDataURL(e.dataTransfer.files[t])}},handleReady:function(e){var t=this,a=Number(new Date);t.handleCallback(e,"data",!1);for(filename in t.libs.namespaces)!function(i){var r=setInterval(function(){var n=Number(new Date);(n-a>t.libs.loadTimeout||i in window)&&(clearTimeout(r),t.handleCallback(e,i,n-a>t.libs.loadTimeout))},AmCharts.updateRate)}(t.libs.namespaces[filename])},getChartData:function(e){function t(e,t,a){function r(e,t){return-1!=s.dataFields.indexOf(e)?r([e,".",t].join("")):e}e&&s.exportTitles&&"gantt"!=i.setup.chart.type&&(g=r(e,a),s.dataFieldsMap[g]=e,s.dataFields.push(g),s.titles[g]=t||g)}var a,r,n,o,s=i.deepMerge({data:[],titles:{},dateFields:[],dataFields:[],dataFieldsMap:{},exportTitles:i.config.exportTitles,exportFields:i.config.exportFields,exportSelection:i.config.exportSelection,columnNames:i.config.columnNames},e||{},!0),l=["valueField","openField","closeField","highField","lowField","xField","yField"];if(0==s.data.length)if("stock"==i.setup.chart.type){for(s.data=i.cloneObject(i.setup.chart.mainDataSet.dataProvider),t(i.setup.chart.mainDataSet.categoryField),s.dateFields.push(i.setup.chart.mainDataSet.categoryField),a=0;a<i.setup.chart.mainDataSet.fieldMappings.length;a++)for(u=i.setup.chart.mainDataSet.fieldMappings[a],r=0;r<i.setup.chart.panels.length;r++){var d=i.setup.chart.panels[r];for(n=0;n<d.stockGraphs.length;n++)for(v=d.stockGraphs[n],i4=0;i4<l.length;i4++)v[l[i4]]==u.toField&&t(u.fromField,v.title,l[i4])}if(i.setup.chart.comparedGraphs.length){for(o=[],a=0;a<s.data.length;a++)o.push(s.data[a][i.setup.chart.mainDataSet.categoryField]);for(a=0;a<i.setup.chart.comparedGraphs.length;a++)for(v=i.setup.chart.comparedGraphs[a],r=0;r<v.dataSet.dataProvider.length;r++){var c=v.dataSet.categoryField,p=v.dataSet.dataProvider[r][c],f=o.indexOf(p);if(-1!=f)for(n=0;n<v.dataSet.fieldMappings.length;n++){var u=v.dataSet.fieldMappings[n],g=v.dataSet.id+"_"+u.toField;s.data[f][g]=v.dataSet.dataProvider[r][u.fromField],s.titles[g]||t(g,v.dataSet.title)}}}}else if("gantt"==i.setup.chart.type){t(i.setup.chart.categoryField);var h=i.setup.chart.segmentsField;for(a=0;a<i.setup.chart.dataProvider.length;a++){var m=i.setup.chart.dataProvider[a];if(m[h])for(r=0;r<m[h].length;r++)m[h][r][i.setup.chart.categoryField]=m[i.setup.chart.categoryField],s.data.push(m[h][r])}for(a=0;a<i.setup.chart.graphs.length;a++)for(v=i.setup.chart.graphs[a],r=0;r<l.length;r++){var b=v[w=l[r]];v.title,t(b,v.title,w)}}else if(-1!=["pie","funnel"].indexOf(i.setup.chart.type))s.data=i.setup.chart.dataProvider,t(i.setup.chart.titleField),s.dateFields.push(i.setup.chart.titleField),t(i.setup.chart.valueField);else if("map"!=i.setup.chart.type)for(s.data=i.setup.chart.dataProvider,i.setup.chart.categoryAxis&&(t(i.setup.chart.categoryField,i.setup.chart.categoryAxis.title),!1!==i.setup.chart.categoryAxis.parseDates&&s.dateFields.push(i.setup.chart.categoryField)),a=0;a<i.setup.chart.graphs.length;a++){var v=i.setup.chart.graphs[a];for(r=0;r<l.length;r++){var w=l[r];t(b=v[w],v.title,w)}}return i.processData(s)},getAnnotations:function(e,t){var a,r=i.deepMerge({},e||{},!0),n=[];for(a=0;a<i.setup.fabric._objects.length;a++)if(!i.setup.fabric._objects[a].isCoreElement){var o=i.setup.fabric._objects[a].toJSON();i.handleCallback(r.reviver,o,a),n.push(o)}return i.handleCallback(t,n),n},setAnnotations:function(e,t){var a=i.deepMerge({data:[]},e||{},!0);return fabric.util.enlivenObjects(a.data,function(e){e.forEach(function(e,t){i.handleCallback(a.reviver,e,t),i.setup.fabric.add(e)}),i.handleCallback(t,a)}),a.data},processData:function(t){var a,r,n=i.deepMerge({data:[],titles:{},dateFields:[],dataFields:[],dataFieldsMap:{},dataFieldsTitlesMap:{},dataDateFormat:i.setup.chart.dataDateFormat,dateFormat:i.config.dateFormat||i.setup.chart.dataDateFormat||"YYYY-MM-DD",exportTitles:i.config.exportTitles,exportFields:i.config.exportFields,exportSelection:i.config.exportSelection,columnNames:i.config.columnNames,processData:i.config.processData},t||{},!0);if(n.data.length){for(a=0;a<n.data.length;a++)for(r in n.data[a])-1==n.dataFields.indexOf(r)&&(n.dataFields.push(r),n.dataFieldsMap[r]=r);void 0!==n.exportFields&&(n.dataFields=n.exportFields.filter(function(e){return-1!=n.dataFields.indexOf(e)}));var o=[];for(a=0;a<n.data.length;a++){var s={},l=!1;for(r=0;r<n.dataFields.length;r++){var d=n.dataFields[r],c=n.dataFieldsMap[d],p=n.columnNames&&n.columnNames[d]||n.titles[d]||d,f=n.data[a][c];null==f&&(f=void 0),n.exportTitles&&"gantt"!=i.setup.chart.type&&p in s&&(p+=["( ",d," )"].join("")),-1!=n.dateFields.indexOf(c)&&(n.dataDateFormat&&(f instanceof String||"string"==typeof f)?f=AmCharts.stringToDate(f,n.dataDateFormat):n.dateFormat&&(f instanceof Number||"number"==typeof f)&&(f=new Date(f)),n.exportSelection&&(f instanceof Date?(f<e.startDate||f>e.endDate)&&(l=!0):(a<e.startIndex||a>e.endIndex)&&(l=!0)),n.dateFormat&&"dateObject"!=n.dateFormat&&f instanceof Date&&(f=AmCharts.formatDate(f,n.dateFormat))),n.dataFieldsTitlesMap[c]=p,s[p]=f}l||o.push(s)}n.data=o}return void 0!==n.processData&&(n.data=i.handleCallback(n.processData,n.data,n)),n.data},capitalize:function(e){return e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()},createMenu:function(t,a){function r(t,a){var o,s,l=document.createElement("ul");for(o=0;o<t.length;o++){var d="string"==typeof t[o]?{format:t[o]}:t[o],c=document.createElement("li"),p=document.createElement("a"),f=document.createElement("img"),u=document.createElement("span"),g=String(d.action?d.action:d.format).toLowerCase();if(d.format=String(d.format).toUpperCase(),c.addEventListener("mouseleave",function(e){this.classList.remove("active")}),p.addEventListener("focus",function(e){if(!i.setup.hasTouch){i.setup.focusedMenuItem=this;var t=this.parentNode;"UL"!=t.tagName&&(t=t.parentNode);var a=t.getElementsByTagName("li");for(o=0;o<a.length;o++)a[o].classList.remove("active");this.parentNode.classList.add("active"),this.parentNode.parentNode.parentNode.classList.add("active")}}),i.config.formats[d.format]?d=i.deepMerge({label:d.icon?"":d.format,format:d.format,mimeType:i.config.formats[d.format].mimeType,extension:i.config.formats[d.format].extension,capture:i.config.formats[d.format].capture,action:i.config.action,fileName:i.config.fileName},d):d.label||(d.label=d.label?d.label:i.i18l("menu.label."+g)),(-1==["CSV","JSON","XLSX"].indexOf(d.format)||-1==["map","gauge"].indexOf(i.setup.chart.type))&&(i.setup.hasBlob||"UNDEFINED"==d.format||!d.mimeType||"image"==d.mimeType.split("/")[0]||"text/plain"==d.mimeType)){if("draw"==d.action)i.config.fabric.drawing.enabled?(d.menu=d.menu?d.menu:i.config.fabric.drawing.menu,d.click=function(e){return function(){this.capture(e,function(){this.createMenu(e.menu)})}}(d)):d.menu=[];else if(!d.populated&&d.action&&-1!=d.action.indexOf("draw.")){var h=d.action.split(".")[1],m=d[h]||i.config.fabric.drawing[h]||[];for(d.menu=[],d.populated=!0,s=0;s<m.length;s++){var b={label:m[s]};if("shapes"==h){var v=-1==m[s].indexOf("//"),w=(v?i.config.path+"shapes/":"")+m[s];b.action="add",b.url=w,b.icon=w,b.ignore=v,b.class="export-drawing-shape"}else"colors"==h?(b.style="background-color: "+m[s],b.action="change",b.color=m[s],b.class="export-drawing-color"):"widths"==h?(b.action="change",b.width=m[s],b.label=document.createElement("span"),b.label.style.width=i.numberToPx(m[s]),b.label.style.height=i.numberToPx(m[s]),b.class="export-drawing-width"):"opacities"==h?(b.style="opacity: "+m[s],b.action="change",b.opacity=m[s],b.label=100*m[s]+"%",b.class="export-drawing-opacity"):"modes"==h&&(b.label=i.i18l("menu.label.draw.modes."+m[s]),b.click=function(e){return function(){i.drawing.mode=e}}(m[s]),b.class="export-drawing-mode");d.menu.push(b)}}else d.click||d.menu||d.items||(i.drawing.handler[g]instanceof Function?(d.action=g,d.click=function(e){return function(){this.drawing.handler[e.action](e),"cancel"!=e.action&&this.createMenu(this.config.fabric.drawing.menu)}}(d)):i.drawing.enabled?d.click=function(e){return function(){this.config.drawing.autoClose&&this.drawing.handler.done(),this["to"+e.format](e,function(t){"download"==e.action&&this.download(t,e.mimeType,[e.fileName,e.extension].join("."))})}}(d):"UNDEFINED"!=d.format&&(d.click=function(e){return function(){if(e.capture||"print"==e.action||"PRINT"==e.format)this.capture(e,function(){this.drawing.handler.done(),this["to"+e.format](e,function(t){"download"==e.action&&this.download(t,e.mimeType,[e.fileName,e.extension].join("."))})});else{if(!this["to"+e.format])throw new Error("Invalid format. Could not determine output type.");this["to"+e.format](e,function(t){this.download(t,e.mimeType,[e.fileName,e.extension].join("."))})}}}(d)));(void 0===d.menu||d.menu.length)&&(p.setAttribute("href","#"),i.setup.hasTouch&&c.classList?(p.addEventListener("touchend",function(e,t){return function(a){a.preventDefault();var r=[a,t];if(("draw"==t.action||"PRINT"==t.format||"UNDEFINED"!=t.format&&t.capture)&&!i.drawing.enabled&&(!isNaN(t.delay)||!isNaN(i.config.delay)))return t.delay=isNaN(t.delay)?i.config.delay:t.delay,void i.delay(t,e);e.apply(i,r)}}(d.click||function(e){e.preventDefault()},d)),p.addEventListener("touchend",function(e){return function(t){function a(e){return e.classList.contains("export-main")||e.classList.contains("export-drawing")}t.preventDefault();var r=e.elements.li,s=function(e){var t=e.parentNode.parentNode,a=t.classList;return!("LI"!=t.tagName||!a.contains("active"))}(r),l=(function(e){var t=e.parentNode.children;for(o=0;o<t.length;o++){var a=t[o],i=a.classList;if(a!==e&&i.contains("active"))return i.remove("active"),!0}}(r),r.getElementsByTagName("ul").length>0);if(!a(r)&&l||i.setup.menu.classList.toggle("active"),!s||!l)for(;n.length;){var d=n.pop(),c=d!==r;a(d)?l||d.classList.remove("active"):c&&d.classList.remove("active")}n.push(r),l&&r.classList.toggle("active")}}(d))):p.addEventListener("click",function(e,t){return function(a){a.preventDefault();var r=[a,t];if(("draw"==t.action||"PRINT"==t.format||"UNDEFINED"!=t.format&&t.capture)&&!i.drawing.enabled&&(!isNaN(t.delay)||!isNaN(i.config.delay)))return t.delay=isNaN(t.delay)?i.config.delay:t.delay,void i.delay(t,e);e.apply(i,r)}}(d.click||function(e){e.preventDefault()},d)),c.appendChild(p),i.isElement(d.label)?u.appendChild(d.label):u.innerHTML=d.label,d.class&&(c.className=d.class),d.style&&c.setAttribute("style",d.style),d.icon&&(f.setAttribute("src",(d.ignore||-1!=d.icon.slice(0,10).indexOf("//")?"":e.pathToImages)+d.icon),p.appendChild(f)),d.label&&p.appendChild(u),d.title&&p.setAttribute("title",d.title),i.config.menuReviver&&(c=i.config.menuReviver.apply(i,[d,c])),d.elements={li:c,a:p,img:f,span:u},(d.menu||d.items)&&"draw"!=d.action?r(d.menu||d.items,c).childNodes.length&&l.appendChild(c):l.appendChild(c))}}return l.childNodes.length&&a.appendChild(l),l}var n=[];return a||("string"==typeof i.config.divId?i.config.divId=a=document.getElementById(i.config.divId):a=i.isElement(i.config.divId)?i.config.divId:i.setup.chart.containerDiv),i.isElement(i.setup.menu)?i.setup.menu.innerHTML="":i.setup.menu=document.createElement("div"),i.setup.menu.setAttribute("class",i.setup.chart.classNamePrefix+"-export-menu "+i.setup.chart.classNamePrefix+"-export-menu-"+i.config.position+" amExportButton"),i.config.menuWalker&&(r=i.config.menuWalker),r.apply(this,[t,i.setup.menu]),i.setup.menu.childNodes.length&&a.appendChild(i.setup.menu),i.setup.menu},delay:function(e,t){var a,r,n=i.deepMerge({delay:3,precision:2},e||{}),o=Number(new Date),s=i.createMenu([{label:i.i18l("capturing.delayed.menu.label").replace("{{duration}}",AmCharts.toFixed(n.delay,n.precision)),title:i.i18l("capturing.delayed.menu.title"),class:"export-delayed-capturing",click:function(){clearTimeout(a),clearTimeout(r),i.createMenu(i.config.menu)}}]).getElementsByTagName("a")[0];a=setInterval(function(){var e=n.delay-(Number(new Date)-o)/1e3;e<=0?(clearTimeout(a),"draw"!=n.action&&i.createMenu(i.config.menu)):s&&(s.innerHTML=i.i18l("capturing.delayed.menu.label").replace("{{duration}}",AmCharts.toFixed(e,2)))},AmCharts.updateRate),r=setTimeout(function(){t.apply(i,arguments)},1e3*n.delay)},migrateSetup:function(e){function t(e){var i;for(i in e){var r=e[i];"export"==i.slice(0,6)&&r?a.menu.push(i.slice(6)):"userCFG"==i?t(r):"menuItems"==i?a.menu=r:"libs"==i?a.libs=r:"string"==typeof i&&(a[i]=r)}}var a={enabled:!0,migrated:!0,libs:{autoLoad:!0},menu:[]};return t(e),a},clear:function(){var e,t;for(void 0!==i.setup.fabric&&i.setup.fabric.removeListeners(),e=0;e<i.listenersToRemove.length;e++)(t=i.listenersToRemove[e]).node.removeEventListener(t.event,t.method);i.isElement(i.setup.wrapper)&&i.isElement(i.setup.wrapper.parentNode)&&i.setup.wrapper.parentNode.removeChild&&i.setup.wrapper.parentNode.removeChild(i.setup.wrapper),i.isElement(i.setup.menu)&&i.isElement(i.setup.wrapper.parentNode)&&i.setup.wrapper.parentNode.removeChild&&i.setup.menu.parentNode.removeChild(i.setup.menu),i.listenersToRemove=[],i.setup.chart.AmExport=void 0,i.setup.chart.export=void 0,i.setup=void 0},loadListeners:function(){function e(e){e&&(e.set({top:e.top+10,left:e.left+10}),i.setup.fabric.add(e))}i.config.keyListener&&"attached"!=i.config.keyListener&&(i.docListener=function(t){function a(e,t){for(i1=0;i1<e.length;i1++){var a=e[i1];a.parentNode.classList.remove("active"),0!=i1||t||a.focus()}}function r(e){i.setup.focusedMenuItem&&i.setup.focusedMenuItem.nextSibling&&(i.setup.focusedMenuItem.parentNode.classList.add("active"),a(i.setup.focusedMenuItem.nextSibling.getElementsByTagName("a"),e))}function n(e){i.setup.focusedMenuItem&&i.setup.focusedMenuItem.parentNode.parentNode.parentNode&&(i.setup.focusedMenuItem.parentNode.classList.add("active"),a(i.setup.focusedMenuItem.parentNode.parentNode.parentNode.getElementsByTagName("a"),e))}var o=i.drawing.buffer.target,s=[37,38,39,40,13,9,27],l=(["top-left","bottom-left"].indexOf(i.config.position),-1!=["top-right","bottom-right"].indexOf(i.config.position));if(i.setup.focusedMenuItem&&-1!=s.indexOf(t.keyCode)){if(9==t.keyCode)return void(i.setup.focusedMenuItem.nextSibling?t.shiftKey&&i.setup.focusedMenuItem.parentNode.classList.remove("active"):(i.setup.focusedMenuItem.parentNode.classList.remove("active"),i.setup.focusedMenuItem.parentNode.nextSibling||(i.setup.focusedMenuItem.parentNode.classList.remove("active"),i.setup.focusedMenuItem.parentNode.parentNode.parentNode.classList.remove("active"))));13==t.keyCode&&i.setup.focusedMenuItem.nextSibling&&r(),37==t.keyCode&&(l?r():n()),39==t.keyCode&&(l?n():r()),40==t.keyCode&&function(e){i.setup.focusedMenuItem&&i.setup.focusedMenuItem.parentNode.nextSibling&&(i.setup.focusedMenuItem.parentNode.classList.remove("active"),a(i.setup.focusedMenuItem.parentNode.nextSibling.getElementsByTagName("a"),e))}(),38==t.keyCode&&function(e){i.setup.focusedMenuItem&&i.setup.focusedMenuItem.parentNode.previousSibling&&(i.setup.focusedMenuItem.parentNode.classList.remove("active"),a(i.setup.focusedMenuItem.parentNode.previousSibling.getElementsByTagName("a"),e))}(),27==t.keyCode&&function(){function e(t){if(i.isElement(t)){try{t.blur()}catch(e){}t.parentNode&&t.parentNode.classList.remove("active"),t.classList.contains("amExportButton")||e(t.parentNode)}}i.setup.focusedMenuItem&&(e(i.setup.focusedMenuItem),i.setup.focusedMenuItem=void 0)}()}8!=t.keyCode&&46!=t.keyCode||!o?27==t.keyCode&&i.drawing.enabled?(t.preventDefault(),i.drawing.buffer.isSelected?i.setup.fabric.discardActiveObject():i.drawing.handler.done()):67==t.keyCode&&(t.metaKey||t.ctrlKey)&&o?i.drawing.buffer.copy=o:88==t.keyCode&&(t.metaKey||t.ctrlKey)&&o?(i.drawing.buffer.copy=o,i.setup.fabric.remove(o)):86==t.keyCode&&(t.metaKey||t.ctrlKey)?i.drawing.buffer.copy&&e(i.drawing.buffer.copy.clone(e)):90==t.keyCode&&(t.metaKey||t.ctrlKey)&&(t.preventDefault(),t.shiftKey?i.drawing.handler.redo():i.drawing.handler.undo()):(t.preventDefault(),i.setup.fabric.remove(o))},i.config.keyListener="attached",document.addEventListener("keydown",i.docListener),i.addListenerToRemove("keydown",document,i.docListener)),i.config.fileListener&&(i.setup.chart.containerDiv.addEventListener("dragover",i.handleDropbox),i.addListenerToRemove("dragover",i.setup.chart.containerDiv,i.handleDropbox),i.setup.chart.containerDiv.addEventListener("dragleave",i.handleDropbox),i.addListenerToRemove("dragleave",i.setup.chart.containerDiv,i.handleDropbox),i.setup.chart.containerDiv.addEventListener("drop",i.handleDropbox),i.addListenerToRemove("drop",i.setup.chart.containerDiv,i.handleDropbox))},init:function(){clearTimeout(a),a=setInterval(function(){i.setup&&i.setup.chart.containerDiv&&(clearTimeout(a),i.config.enabled&&(i.setup.chart.AmExport=i,i.config.overflow&&(i.setup.chart.div.style.overflow="visible"),i.loadListeners(),i.createMenu(i.config.menu),i.handleReady(i.config.onReady)))},AmCharts.updateRate)},construct:function(){i.drawing.handler.cancel=i.drawing.handler.done;try{i.setup.hasBlob=!!new Blob}catch(e){}window.safari=window.safari?window.safari:{},i.defaults.fabric.drawing.fontSize=i.setup.chart.fontSize||11,i.config.drawing=i.deepMerge(i.defaults.fabric.drawing,i.config.drawing||{},!0),i.config.border&&(i.config.border=i.deepMerge(i.defaults.fabric.border,i.config.border||{},!0)),i.deepMerge(i.defaults.fabric,i.config,!0),i.deepMerge(i.defaults.fabric,i.config.fabric||{},!0),i.deepMerge(i.defaults.pdfMake,i.config,!0),i.deepMerge(i.defaults.pdfMake,i.config.pdfMake||{},!0),i.deepMerge(i.libs,i.config.libs||{},!0),i.config.drawing=i.defaults.fabric.drawing,i.config.fabric=i.defaults.fabric,i.config.pdfMake=i.defaults.pdfMake,i.config=i.deepMerge(i.defaults,i.config,!0),i.config.fabric.drawing.enabled&&void 0===i.config.fabric.drawing.menu&&(i.config.fabric.drawing.menu=[],i.deepMerge(i.config.fabric.drawing.menu,[{class:"export-drawing",menu:[{label:i.i18l("menu.label.draw.add"),menu:[{label:i.i18l("menu.label.draw.shapes"),action:"draw.shapes"},{label:i.i18l("menu.label.draw.text"),action:"text"}]},{label:i.i18l("menu.label.draw.change"),menu:[{label:i.i18l("menu.label.draw.modes"),action:"draw.modes"},{label:i.i18l("menu.label.draw.colors"),action:"draw.colors"},{label:i.i18l("menu.label.draw.widths"),action:"draw.widths"},{label:i.i18l("menu.label.draw.opacities"),action:"draw.opacities"},"UNDO","REDO"]},{label:i.i18l("menu.label.save.image"),menu:["PNG","JPG","SVG","PDF"]},"PRINT","CANCEL"]}])),void 0===i.config.menu&&(i.config.menu=[],i.deepMerge(i.config,{menu:[{class:"export-main",menu:[{label:i.i18l("menu.label.save.image"),menu:["PNG","JPG","SVG","PDF"]},{label:i.i18l("menu.label.save.data"),menu:["CSV","XLSX","JSON"]},{label:i.i18l("menu.label.draw"),action:"draw",menu:i.config.fabric.drawing.menu},{format:"PRINT",label:i.i18l("menu.label.print")}]}]})),i.libs.path||(i.libs.path=i.config.path+"libs/"),i.setup.hasClasslist||i.libs.resources.push("classList.js/classList.min.js"),i.isSupported()&&(i.loadDependencies(i.libs.resources,i.libs.reload),i.setup.chart.addClassNames=!0,i.setup.chart[i.name]=i,i.init())}};if(t)i.config=t;else if(i.setup.chart[i.name])i.config=i.setup.chart[i.name];else{if(!i.setup.chart.amExport&&!i.setup.chart.exportConfig)return;i.config=i.migrateSetup(i.setup.chart.amExport||i.setup.chart.exportConfig)}return i.construct(),i.deepMerge(this,i)},AmCharts.addInitHandler(function(e){new AmCharts.export(e)},["pie","serial","xy","funnel","radar","gauge","stock","map","gantt"]);
