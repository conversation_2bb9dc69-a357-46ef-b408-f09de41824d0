from django.shortcuts import render, redirect
from datetime import datetime
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from Sucursal.models import Sucursal,Envios,DetalleEnvios,Vales,DetalleVales,Movimientos
from Producto.models import ProductoFactura,DetalleFactura
from Sucursal.forms import SucursalForm, UpdateSucursalForm,Envios_Tdas_Form,Detalle_Envios_Tdas_Form,Vales_Tdas_Form,Detalle_Vales_Tdas_Form
from Producto.models import Producto
from Categoria.models import Categoria
from user.models import User
from django.db.models import Q,Sum
from django.http import HttpResponse
from .repote import Comprobante,Comprobante2
import uuid

@login_required
def nueva(request):
    encar = User.objects.all()
    form = SucursalForm()
    if request.method == "POST":
        form = SucursalForm(request.POST)
        if form.is_valid():
            try:
                s = Sucursal()
                s.nit = form.cleaned_data['nit']
                s.nombre = form.cleaned_data['nombre']
                s.direccion = form.cleaned_data['direccion']
                s.telefono = form.cleaned_data['telefono']
                s.telefono2 = form.cleaned_data['telefono2']
                s.correo = form.cleaned_data['correo']
                s.ubicacion = form.cleaned_data['ubicacion']
                s.fecha = str(datetime.today().strftime('%Y-%m-%d'))
                s.usuario = User.objects.get(id=request.user.id)
                s.save()
                messages.success(request, f'Sucursal {s.nombre} Ingresada!')
                return redirect('NuevaSucursal')
            except:
                messages.error(
                    request, f'No Se Pudo Ingresar Sucursal {s.nombre}!')
                return redirect('NuevaSucursal')

    return render(request, 'Sucursal/nueva.html', {'form': form, 'encar': encar})


@login_required
def listado(request):
    datos = Sucursal.objects.all().order_by('id')
    return render(request, 'Sucursal/lista.html', {'sucursal': datos})


@login_required
def actualizar(request, id):
    sucursal = Sucursal.objects.get(id=id)
    if request.method == 'GET':
        form = UpdateSucursalForm(instance=sucursal)
    else:
        form = UpdateSucursalForm(request.POST, instance=sucursal)

        if form.is_valid():
            try:
                sucursal.fecha = str(datetime.today().strftime('%Y-%m-%d'))
                sucursal.usuario = User.objects.get(id=request.user.id)
                form.save()
                messages.success(
                    request, f'Categoria {sucursal.nombre} Modificada Exitosamente!')
                return redirect('ListaSucursal')
            except:
                messages.error(
                    request, f'No Se Pudo Modificar {sucursal.nombre}!')
                return redirect('ListaSucursal')

    return render(request, 'Sucursal/actualizar.html', {'form': form})


@login_required
def eliminar(request, id):
    try:
        sucursal = Sucursal.objects.get(id=id)
        sucursal.delete()
        messages.success(request, f'Sucursal {sucursal.nombre} Eliminado!')
        return redirect('ListaSucursal')
    except:
        messages.error(
            request, f'No Se Puede Eliminar Sucursal {sucursal.nombre}')
        return redirect('ListaSucursal')
    



@login_required
def ver(request, f):
    ver = ProductoFactura.objects.get(factura=f)
    datos = DetalleFactura.objects.filter(factura=f)
    return render(request, 'Movimiento/ver.html', {'ver': ver, 'detalle': datos})







# envios

@login_required
def nuevo_envio(request):
    form = Envios_Tdas_Form()
    ul = Envios.objects.filter(origen=request.user.tienda).last()
    
    if request.method == "POST":
          
        form = Envios_Tdas_Form(request.POST)
        if form.is_valid():
            e = Envios()
            if request.POST['origen'] == "Estanzuela":
                en = Envios.objects.filter(origen=request.POST['origen']).last()
                if en == None:
                   e.correlativo = 1
                else:
                    e.correlativo = en.correlativo+1   

            elif request.POST['origen'] == "Teculutan":
                en = Envios.objects.filter(origen=request.POST['origen']).last()
                if en == None:
                   e.correlativo = 1
                else:
                    e.correlativo = en.correlativo+1   

            elif request.POST['origen'] == "Zacapa":
                en = Envios.objects.filter(origen=request.POST['origen']).last()
                if en == None:
                   e.correlativo = 1
                else:
                    e.correlativo = en.correlativo+1   

            else:
                en = Envios.objects.filter(origen=request.POST['origen']).last()
                if en == None:
                   e.correlativo = 1
                else:
                    e.correlativo = en.correlativo+1

            e.origen = request.POST['origen']
            e.destino = form.cleaned_data['destino']
            e.observacion = form.cleaned_data['observacion']
            e.total = 0.00
            if request.user.rol == "admin":
                e.tienda = request.POST['tienda']
            else:
                e.tienda = request.user.tienda
            e.token = str(uuid.uuid4())
            e.usuario = User.objects.get(id=request.user.id)
            e.save()
            messages.success(request,f'Ingreso de Detalles Para Envio # {e.correlativo}')
            return redirect('DetalleEnvioTda',e.token)

    return render(request,'Envios/nuevo.html',{'form':form,'c':ul})



@login_required
def detalle_envio(request,t):
    e = Envios.objects.get(token=t)
    total = DetalleEnvios.objects.filter(token=t).aggregate(t=Sum('total'))
    detalle = DetalleEnvios.objects.filter(token=t)
    
    if e:
        tk = True
    else:
        tk = False

    if request.method == "POST":

        if 'buscar' in request.POST:

                if request.POST['buscar'] == "":
                    messages.error(request, f'Campo Busqueda No Puede Estar Vacio')
                    return redirect('DetalleEnvioTda', t)

                else:
                    if request.user.rol == "admin":
                        querys = (Q(id__icontains=request.POST['buscar'].strip()) | Q(nombre__icontains=request.POST['buscar'].strip()))
                        busqueda = Producto.objects.filter(querys)
                        if busqueda:
                            b = True
                        else:
                            b = False
                        return render(request,'Envios/detalle.html',{'e':e,'t':total['t'],'tk':tk,'d':detalle,'b':b,'bus':busqueda})    
                    else:
                        querys = (Q(id__icontains=request.POST['buscar'].strip()) | Q(nombre__icontains=request.POST['buscar'].strip()))
                        busqueda = Producto.objects.filter(querys, tienda=request.user.tienda)
                        if busqueda:
                            b = True
                        else:
                            b = False            
                        return render(request,'Envios/detalle.html',{'e':e,'t':total['t'],'tk':tk,'d':detalle,'b':b,'bus':busqueda})
        
        elif 'agregar' in request.POST:
                prod = Producto.objects.get(id=request.POST['id'],tienda=e.tienda)
                # existencia de producto
                if Producto.objects.filter(id=request.POST['id'],tienda=e.tienda).exists():

                    # si existe en detalle sumar la cantidad

                    if DetalleEnvios.objects.filter(id_prod=request.POST['id'],token=t).exists():
                        inc = DetalleEnvios.objects.get(id_prod=request.POST['id'],token=t)
                        DetalleEnvios.objects.filter(id_prod=request.POST['id'],token=t).update(cantidad=inc.cantidad+int(request.POST['cantidad']),total=inc.total+(int(request.POST['cantidad'])*prod.precio_venta))
                        tot = DetalleEnvios.objects.filter(token=t).aggregate(tt=Sum('total'))
                        Envios.objects.filter(token=t).update(total=tot['tt'])
                        Producto.objects.filter(id=request.POST['id']).update(stock=prod.stock-int(request.POST['cantidad']),salio=prod.salio+int(request.POST['cantidad']))
                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                        movi(e.correlativo,'Envio',e.origen,inc.id_prod.pk,inc.id_prod.nombre,prod.stock,0,int(request.POST['cantidad']),\
                             (prod.stock-int(request.POST['cantidad'])),e.observacion,e.usuario)
                        messages.success(request,f"Se Agrego {request.POST['cantidad']} Unidades de {prod.nombre}")
                        return redirect('DetalleEnvioTda', t)

                    else:    
                        # verificacion de stock
                        if prod.stock >= int(request.POST['cantidad']):
                            i = DetalleEnvios()
                            i.correlativo = Envios.objects.get(id=e.id)
                            i.id_prod = Producto.objects.get(id=request.POST['id'])
                            i.tienda = prod.tienda
                            i.cantidad = int(request.POST['cantidad'])
                            i.precio_compra = prod.precio_compra
                            i.precio_venta = prod.precio_venta
                            i.total = i.precio_venta*i.cantidad
                            i.observacion = e.observacion
                            i.token = t
                            i.usuario = User.objects.get(id=request.user.id)
                            i.estado = 0
                            i.save()
                            Producto.objects.filter(id=request.POST['id']).update(stock=prod.stock-i.cantidad,salio=prod.salio+i.cantidad)
                            tot = DetalleEnvios.objects.filter(token=t).aggregate(tt=Sum('total'))
                            Envios.objects.filter(token=t).update(total=tot['tt'])
                            # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                            movi(e.correlativo,'Envio',e.origen,i.id_prod.pk,i.id_prod.nombre,prod.stock,0,int(request.POST['cantidad']),\
                            (prod.stock-int(request.POST['cantidad'])),e.observacion,i.usuario)
                            messages.success(request,f'Se Agrego {i.cantidad} Unidades de {i.id_prod.nombre}')
                            return redirect('DetalleEnvioTda', t)
                        else:
                            messages.error(request,f'Paso del limite')
                            return redirect('DetalleEnvioTda', t)
                        
                else:
                    messages.error(request,f'Producto No Existe Verifique')

        elif 'quitar' in request.POST:

            if DetalleEnvios.objects.filter(id=request.POST['corr']).exists():

                q = DetalleEnvios.objects.get(id=request.POST['corr'])
                p = Producto.objects.get(id=q.id_prod.id,tienda=q.tienda)
                tot = DetalleEnvios.objects.filter(token=t).aggregate(tt=Sum('total'))
                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                movi(e.correlativo,'Envio',e.origen,q.id_prod.pk,q.id_prod.nombre,p.stock,q.cantidad,0,\
                            (p.stock+q.cantidad),'Se Quito de Envio',q.usuario)
                Envios.objects.filter(token=t).update(total=tot['tt'])
                Producto.objects.filter(id=q.id_prod.id,tienda=q.tienda).update(stock=p.stock+q.cantidad,salio=p.salio-q.cantidad)
                
                q.delete()
                messages.success(request,f'Se Quito {q.cantidad} Unidades de {q.id_prod.nombre}')
                return redirect('DetalleEnvioTda', t)

        elif 'descartar' in request.POST:

            for des in DetalleEnvios.objects.filter(token=t):
                pr = Producto.objects.get(id=des.id_prod.id,tienda=des.tienda)
                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                movi(e.correlativo,'Envio',e.origen,des.id_prod.pk,des.id_prod.nombre,pr.stock,des.cantidad,0,\
                            (pr.stock+des.cantidad),'Se Descarto Envio por lo cual el correlativo vuelve al anterior',des.usuario)
                Producto.objects.filter(id=des.id_prod.id,tienda=des.tienda).update(stock=pr.stock+des.cantidad,salio=pr.salio-des.cantidad)


            DetalleEnvios.objects.filter(token=t).delete()
            Envios.objects.filter(token=t).delete()    
            messages.success(request,f'Se Descartaron los Productos Regresan a Inventario')
            return redirect('NuevoEnvioTda')     

        else:

            Envios.objects.filter(token=t).update(estado=99)
            messages.success(request,f'Envio Finalizado Revisa en Listado de Envios')
            return redirect('NuevoEnvioTda')    


    return render(request,'Envios/detalle.html',{'e':e,'t':total['t'],'tk':tk,'d':detalle})




@login_required
def listado_envios(request):
    if request.user.rol == "admin":
        lista = Envios.objects.all()
    else:
        lista = Envios.objects.filter(origen=request.user.tienda)
    return render(request,'Envios/lista.html',{'l':lista})


@login_required
def listado_envios_recibir(request):

    if request.user.rol == "admin":
        lista = Envios.objects.all()
    else:
        lista = Envios.objects.filter(destino=request.user.tienda)

    return render(request,'Envios/recibir.html',{'l':lista})

@login_required
def recibir_envio(request,t):
        
        if DetalleEnvios.objects.filter(token=t).exists():
            for d in DetalleEnvios.objects.filter(token=t,estado=0):
                if Producto.objects.filter(nombre=d.id_prod.nombre,tienda=request.user.tienda):
                    pr = Producto.objects.get(nombre=d.id_prod.nombre,tienda=request.user.tienda)
                    print('Recibiendo Envio!!!')
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(d.correlativo.correlativo,'Recepcion de Envio',d.correlativo.destino,d.id_prod.pk,d.id_prod.nombre,pr.stock,d.cantidad,0,\
                             (pr.stock+d.cantidad),d.correlativo.observacion,d.usuario)
                    Producto.objects.filter(nombre=d.id_prod.nombre,tienda=request.user.tienda).update(stock=pr.stock+d.cantidad,ingreso=pr.ingreso+d.cantidad)
                    DetalleEnvios.objects.filter(token=t,estado=0).update(estado=1)
                    
                else:
                    # id nombre descr stock ingreso salio precio_compra precio_venta tienda fecha id_cate usuario
                    p = Producto()
                    p.nombre = d.id_prod.nombre
                    p.descripcion = 'S/D'
                    p.stock = d.cantidad
                    p.ingreso = d.cantidad
                    p.salio = 0
                    p.precio_compra = d.id_prod.precio_compra
                    p.precio_venta = d.id_prod.precio_venta
                    p.tienda = request.user.tienda
                    p.fecha = str(datetime.today().strftime('%Y-%m-%d'))
                    p.id_cate = Categoria.objects.get(id=d.id_prod.id_cate.id)
                    p.usuario = User.objects.get(id=request.user.id)
                    p.save()
                    pp = Producto.objects.get(nombre=d.id_prod.nombre,tienda=p.tienda)
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(d.correlativo.correlativo,'Recepcion de Envio',d.correlativo.destino,p.id,d.id_prod.nombre,0,d.cantidad,0,\
                             d.cantidad,f'Producto No Existia en Tienda {d.correlativo.destino}',d.usuario)

        Envios.objects.filter(token=t).update(estado=1)
        messages.success(request,'Recibido!')
        return redirect('RecibiendoEnvioTda')


@login_required
def ver_envio(request,t):

    e = Envios.objects.get(token=t)
    d = DetalleEnvios.objects.filter(token=t)

    if Envios.objects.get(token=t):
        tk = True
    else:
        tk = False    

    return render(request,'Envios/ver.html',{'e':e,'d':d,'tk':tk})



def pdf(request, f):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        response = HttpResponse(content_type='application/pdf')
        response[
            'Content-Disposition'] = f'attachment; filename="comprobante-envio-#-{f}.pdf"'
        r = Comprobante(f)
        response.write(r.run())
        return response


# fin envios







# vales

@login_required
def nuevo_vale(request):
    form = Vales_Tdas_Form()
    ul = Vales.objects.filter(origen=request.user.tienda).last()
    
    if request.method == "POST":
          
        form = Vales_Tdas_Form(request.POST)
        if form.is_valid():
            e = Vales()
            if request.POST['origen'] == "Estanzuela":
                en = Vales.objects.filter(origen=request.POST['origen']).last()
                if en == None:
                   e.correlativo = 1
                else:
                    e.correlativo = en.correlativo+1   

            elif request.POST['origen'] == "Teculutan":
                en = Vales.objects.filter(origen=request.POST['origen']).last()
                if en == None:
                   e.correlativo = 1
                else:
                    e.correlativo = en.correlativo+1   

            elif request.POST['origen'] == "Zacapa":
                en = Vales.objects.filter(origen=request.POST['origen']).last()
                if en == None:
                   e.correlativo = 1
                else:
                    e.correlativo = en.correlativo+1   

            else:
                en = Vales.objects.filter(origen=request.POST['origen']).last()
                if en == None:
                   e.correlativo = 1
                else:
                    e.correlativo = en.correlativo+1

            e.origen = request.POST['origen']
            e.destino = form.cleaned_data['destino']
            e.observacion = form.cleaned_data['observacion']
            e.total = 0.00
            if request.user.rol == "admin":
                e.tienda = request.POST['tienda']
            else:
                e.tienda = request.user.tienda
            e.usuario = User.objects.get(id=request.user.id)
            e.save()
            messages.success(request,f'Ingreso de Detalles Para Envio # {e.correlativo}')
            return redirect('DetalleValesTda',e.token)

    return render(request,'Vales/nuevo.html',{'form':form,'c':ul})



@login_required
def detalle_vale(request,t):
    e = Vales.objects.get(token=t)
    total = DetalleVales.objects.filter(token=t).aggregate(t=Sum('total'))
    detalle = DetalleVales.objects.filter(token=t)
    
    if e:
        tk = True
    else:
        tk = False

    if request.method == "POST":

        if 'buscar' in request.POST:

                if request.POST['buscar'] == "":
                    messages.error(request, f'Campo Busqueda No Puede Estar Vacio')
                    return redirect('DetalleEnvioTda', t)

                else:
                    if request.user.rol == "admin":
                        querys = (Q(id__icontains=request.POST['buscar'].strip()) | Q(nombre__icontains=request.POST['buscar'].strip()))
                        busqueda = Producto.objects.filter(querys)
                        if busqueda:
                            b = True
                        else:
                            b = False
                        return render(request,'Vales/detalle.html',{'e':e,'t':total['t'],'tk':tk,'d':detalle,'b':b,'bus':busqueda})    
                    else:
                        querys = (Q(id__icontains=request.POST['buscar'].strip()) | Q(nombre__icontains=request.POST['buscar'].strip()))
                        busqueda = Producto.objects.filter(querys, tienda=request.user.tienda)
                        if busqueda:
                            b = True
                        else:
                            b = False            
                        return render(request,'Vales/detalle.html',{'e':e,'t':total['t'],'tk':tk,'d':detalle,'b':b,'bus':busqueda})
        
        elif 'agregar' in request.POST:
                prod = Producto.objects.get(id=request.POST['id'],tienda=e.tienda)
                # existencia de producto
                if Producto.objects.filter(id=request.POST['id'],tienda=e.tienda).exists():

                    # si existe en detalle sumar la cantidad

                    if DetalleVales.objects.filter(id_prod=request.POST['id'],token=t).exists():
                        inc = DetalleVales.objects.get(id_prod=request.POST['id'],token=t)
                        DetalleVales.objects.filter(id_prod=request.POST['id'],token=t).update(cantidad=inc.cantidad+int(request.POST['cantidad']),total=inc.total+(int(request.POST['cantidad'])*prod.precio_venta))
                        tot = DetalleVales.objects.filter(token=t).aggregate(tt=Sum('total'))
                        Vales.objects.filter(token=t).update(total=tot['tt'])
                        Producto.objects.filter(id=request.POST['id']).update(stock=prod.stock-int(request.POST['cantidad']),salio=prod.salio+int(request.POST['cantidad']))
                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                        movi(e.correlativo,'Vale',e.origen,inc.id_prod.pk,inc.id_prod.nombre,prod.stock,0,int(request.POST['cantidad']),\
                             (prod.stock-int(request.POST['cantidad'])),e.observacion,e.usuario)
                        messages.success(request,f"Se Agrego {request.POST['cantidad']} Unidades de {prod.nombre}")
                        return redirect('DetalleValesTda', t)

                    else:    
                        # verificacion de stock
                        if prod.stock >= int(request.POST['cantidad']):
                            i = DetalleVales()
                            i.correlativo = Vales.objects.get(id=e.id)
                            i.id_prod = Producto.objects.get(id=request.POST['id'])
                            i.tienda = prod.tienda
                            i.cantidad = int(request.POST['cantidad'])
                            i.precio_compra = prod.precio_compra
                            i.precio_venta = prod.precio_venta
                            i.total = i.precio_venta*i.cantidad
                            i.observacion = e.observacion
                            i.token = t
                            i.usuario = User.objects.get(id=request.user.id)
                            i.estado = 0
                            i.save()
                            Producto.objects.filter(id=request.POST['id']).update(stock=prod.stock-i.cantidad,salio=prod.salio+i.cantidad)
                            tot = DetalleVales.objects.filter(token=t).aggregate(tt=Sum('total'))
                            # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                            movi(e.correlativo,'Vale',e.origen,i.id_prod.pk,i.id_prod.nombre,prod.stock,0,int(request.POST['cantidad']),\
                            (prod.stock-int(request.POST['cantidad'])),e.observacion,i.usuario)
                            Vales.objects.filter(token=t).update(total=tot['tt'])
                            messages.success(request,f'Se Agrego {i.cantidad} Unidades de {i.id_prod.nombre}')
                            return redirect('DetalleValesTda', t)
                        else:
                            messages.error(request,f'Paso del limite')
                            return redirect('DetalleValesTda', t)
                        
                else:
                    messages.error(request,f'Producto No Existe Verifique')

        elif 'quitar' in request.POST:

            if DetalleVales.objects.filter(id=request.POST['corr']).exists():

                q = DetalleVales.objects.get(id=request.POST['corr'])
                p = Producto.objects.get(id=q.id_prod.id,tienda=q.tienda)
                tot = DetalleVales.objects.filter(token=t).aggregate(tt=Sum('total'))
                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                movi(e.correlativo,'Vale',e.origen,q.id_prod.pk,q.id_prod.nombre,p.stock,q.cantidad,0,\
                            (p.stock+q.cantidad),'Se Quito de Vale',q.usuario)
                Vales.objects.filter(token=t).update(total=tot['tt'])
                Producto.objects.filter(id=q.id_prod.id,tienda=q.tienda).update(stock=p.stock+q.cantidad,salio=p.salio-q.cantidad)
                q.delete()
                messages.success(request,f'Se Quito {q.cantidad} Unidades de {q.id_prod.nombre}')
                return redirect('DetalleValesTda', t)

        elif 'descartar' in request.POST:

            for des in DetalleVales.objects.filter(token=t):
                pr = Producto.objects.get(id=des.id_prod.id,tienda=des.tienda)
                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                movi(e.correlativo,'Vale',e.origen,des.id_prod.pk,des.id_prod.nombre,pr.stock,des.cantidad,0,\
                            (pr.stock+des.cantidad),'Se Descarto Vale por lo cual el correlativo vuelve al anterior',des.usuario)
                Producto.objects.filter(id=des.id_prod.id,tienda=des.tienda).update(stock=pr.stock+des.cantidad,salio=pr.salio-des.cantidad)

            DetalleVales.objects.filter(token=t).delete()
            Vales.objects.filter(token=t).delete()    
            messages.success(request,f'Se Descartaron los Productos Regresan a Inventario')
            return redirect('NuevoEnvioTda')     

        else:

            Vales.objects.filter(token=t).update(estado=99)
            messages.success(request,f'Vale Finalizado Revisa en Listado de Vales')
            return redirect('NuevoValesTda')    


    return render(request,'Vales/detalle.html',{'e':e,'t':total['t'],'tk':tk,'d':detalle})




@login_required
def listado_vales(request):

    if request.user.rol == "admin":
        lista = Vales.objects.all()
    else:
        lista = Vales.objects.filter(origen=request.user.tienda)    

    return render(request,'Vales/lista.html',{'l':lista})


@login_required
def listado_vales_recibir(request):

    if request.user.rol == "admin":
        lista = Vales.objects.all()
    else:
        lista = Vales.objects.filter(destino=request.user.tienda)  

    return render(request,'Vales/recibir.html',{'l':lista})

@login_required
def recibir_vale(request,t):
        if DetalleVales.objects.filter(token=t).exists():
            for d in DetalleVales.objects.filter(token=t,estado=0):
                if Producto.objects.filter(nombre=d.id_prod.nombre,tienda=request.user.tienda):
                    pr = Producto.objects.get(nombre=d.id_prod.nombre,tienda=request.user.tienda)
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(d.correlativo,'Recepcion de Vale',d.correlativo.origen,d.id_prod.pk,d.id_prod.nombre,pr.stock,d.cantidad,0,\
                             (pr.stock+d.cantidad),d.correlativo.observacion,d.usuario)
                    Producto.objects.filter(nombre=d.id_prod.nombre,tienda=request.user.tienda).update(stock=pr.stock+d.cantidad,ingreso=pr.ingreso+d.cantidad)
                    DetalleVales.objects.filter(token=t,estado=0).update(estado=1)
                    Vales.objects.filter(token=t).update(estado=1)
                else:
                    # id nombre descr stock ingreso salio precio_compra precio_venta tienda fecha id_cate usuario
                    p = Producto()
                    p.nombre = d.id_prod.nombre
                    p.descripcion = 'S/D'
                    p.stock = d.cantidad
                    p.ingreso = d.cantidad
                    p.salio = 0
                    p.precio_compra = d.id_prod.precio_compra
                    p.precio_venta = d.id_prod.precio_venta
                    p.tienda = request.user.tienda
                    p.fecha = str(datetime.today().strftime('%Y-%m-%d'))
                    p.id_cate = Categoria.objects.get(id=d.id_prod.id_cate.id)
                    p.usuario = User.objects.get(id=request.user.id)
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(d.correlativo,'Recepcion de Vale',d.correlativo.origen,p.id,d.id_prod.nombre,pr.stock,d.cantidad,0,\
                             (pr.stock+d.cantidad),f'Producto No Existia en Tienda {d.correlativo.destino}',d.usuario)
                    p.save()


        messages.success(request,'Recibido!')
        return redirect('RecibiendoValeTda')


@login_required
def ver_vale(request,t):

    e = Vales.objects.get(token=t)
    d = DetalleVales.objects.filter(token=t)

    if Vales.objects.get(token=t):
        tk = True
    else:
        tk = False    

    return render(request,'Vales/ver.html',{'e':e,'d':d,'tk':tk})



def pdf_vale(request, f):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        response = HttpResponse(content_type='application/pdf')
        response[
            'Content-Disposition'] = f'attachment; filename="comprobante-vale-#-{f}.pdf"'
        r = Comprobante2(f)
        response.write(r.run())
        return response


# fin vales






# movimientos
# docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
def movi(d,tp,td,id,prod,u,e,s,ac,obs,us):
    print('Procesando en Movimeintos')
    m = Movimientos()
    m.doc = d
    m.tipo = tp
    m.tienda = td
    m.id_prod = id
    m.prod = prod
    m.ultimo = u
    m.entrada = e
    m.salida = s
    m.actual = ac
    m.obs = obs
    m.fecha = datetime.today()
    m.usuario = User.objects.get(id=us.id)
    m.save()


# fin movimientos
