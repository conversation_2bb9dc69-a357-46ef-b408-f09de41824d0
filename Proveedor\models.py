from django.db import models
from user.models import User

class Proveedor(models.Model):
    nit = models.CharField(primary_key=True,max_length=15,blank=False,null=False)
    nombre = models.CharField(max_length=550,blank=False,null=False)
    direccion = models.CharField(max_length=550,blank=False,null=False)
    telefono = models.CharField(max_length=9,blank=True,null=True,default='0000-0000')
    compras = models.IntegerField(blank=False,null=False,default=0)
    total_compra = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False,default="0.00")
    fecha = models.CharField(max_length=10,blank=False,null=False)
    usuario = models.ForeignKey(User,on_delete=models.CASCADE,blank=False,null=False)

    class Meta:
        ordering = ["nit"]

    def __str__(self):
        return self.nit