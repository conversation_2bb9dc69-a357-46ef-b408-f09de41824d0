from django.shortcuts import render,redirect
from datetime import datetime
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from Gasto.models import Gasto
from Gasto.forms import GastoForm,UpdateGastoForm
from user.models import User

@login_required
def nuevo(request):
    form = GastoForm()
    if request.method == "POST":
        form = GastoForm(request.POST)
        if form.is_valid():
            try:
                g = Gasto()
                g.nombre = form.cleaned_data['nombre']
                g.cantidad = form.cleaned_data['cantidad']
                g.precio = form.cleaned_data['precio']
                g.total = g.cantidad*g.precio
                g.tienda = request.user.tienda
                g.fecha = datetime.today()
                g.estado = 1
                g.usuario = User.objects.get(id=request.user.id)
                g.save()
                messages.success(request,f'Gasto {g.nombre} Ingresado!')
                return redirect('NuevoGasto')
            except:
                messages.error(request,f'No Se Pudo Ingresar Gasto {g.nombre}!')
                return redirect('NuevoGasto')
    
    return render(request,'Gasto/nuevo.html',{'form':form})



@login_required
def listado(request):
    gasto = Gasto.objects.all().order_by('id')
    return render(request,'Gasto/lista.html',{'gasto':gasto})



@login_required
def actualizar(request,id):
    gasto = Gasto.objects.get(id=id)
    if request.method == 'GET':
        form = UpdateGastoForm(instance=gasto)
    else:
        form = UpdateGastoForm(request.POST,instance = gasto)
     
        if form.is_valid():
            try:
                gasto.fecha = datetime.today()
                gasto.usuario = User.objects.get(id=request.user.id)
                gasto.tienda = request.user.tienda
                form.save()
                messages.success(request, f'Gasto {gasto.nombre} Modificada Exitosamente!')
                return redirect('ListaGasto')
            except:
                messages.error(request, f'No Se Pudo Modificar Gasto {gasto.nombre}!')
                return redirect('ListaGasto')

    return render(request,'Gasto/actualizar.html',{'form':form})




@login_required
def eliminar(request,id):
    try:
        gasto = Gasto.objects.get(id=id)
        gasto.delete()
        messages.success(request,f'Gasto {gasto.nombre} Eliminado!')
        return redirect('ListaGasto')
    except:
        messages.error(request,f'No Se Puede Eliminar Gasto {gasto.nombre}')
        return redirect('ListaGasto')    
