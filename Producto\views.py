from django.shortcuts import render, redirect
from datetime import datetime
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from Categoria.models import Categoria
from Producto.models import Producto, Bitacora
from Producto.forms import ProductoForm, UpdateProductoForm,UpdateNormalProductoForm
from user.models import User
import xlwt
from django.http import HttpResponse


@login_required
def nuevo(request):
    c = Categoria.objects.all()
    form = ProductoForm()
    if request.method == "POST":
        form = ProductoForm(request.POST)
        if form.is_valid():
            p = Producto()
            p.nombre = form.cleaned_data['nombre']
            p.descripcion = form.cleaned_data['descripcion']
            p.stock = form.cleaned_data['stock']
            p.precio_compra = form.cleaned_data['precio_compra']
            p.precio_venta = form.cleaned_data['precio_venta']
            p.tienda = form.cleaned_data['tienda']
            p.id_cate = Categoria.objects.get(id=request.POST['id_cate'])
            p.fecha = str(datetime.today().strftime('%Y-%m-%d'))
            p.usuario = User.objects.get(id=request.user.id)
            p.save()
            bitacora(0, p.nombre, 'Nuevo', 0, p.stock,
                     p.stock, 0, p.stock, p.tienda, request.user.username)
            messages.success(request, f'Producto {p.nombre} Ingresado!')
            return redirect('NuevoProducto')

    return render(request, 'Producto/nuevo.html', {'form': form, 'c': c})


@login_required
def listado(request):
    datos = Producto.objects.all()
    return render(request, 'Producto/lista.html', {'prod': datos})


@login_required
def listadozacapa(request):
    datos = Producto.objects.filter(tienda="Zacapa")
    return render(request, 'Producto/listazacapa.html', {'prod': datos})


@login_required
def listadostacruz(request):
    datos = Producto.objects.filter(tienda="Santa Cruz")
    return render(request, 'Producto/listastacruz.html', {'prod': datos})


@login_required
def listadogualan(request):
    datos = Producto.objects.filter(tienda="Gualan")
    return render(request, 'Producto/listagualan.html', {'prod': datos})



@login_required
def listadoestanzuela(request):
    datos = Producto.objects.filter(tienda="Estanzuela")
    return render(request, 'Producto/listaestanzuela.html', {'prod': datos})


@login_required
def listadoteculutan(request):
    datos = Producto.objects.filter(tienda="Teculutan")
    return render(request, 'Producto/listateculutan.html', {'prod': datos})


@login_required
def listadotienda(request):
    datos = Producto.objects.filter(tienda=request.user.tienda)
    return render(request, 'Producto/lista.html', {'prod': datos})


@login_required
def actualizar(request, id):
    prod = Producto.objects.get(id=id)
    cate = Categoria.objects.all().order_by('id')
    if request.method == 'GET':
        if request.user.rol == "admin":
            form = UpdateProductoForm(instance=prod)
        else:
            form = UpdateNormalProductoForm(instance=prod)    
    else:
        if request.user.rol == "admin":
            form = UpdateProductoForm(request.POST, instance=prod)
        else:
            form = UpdateNormalProductoForm(request.POST, instance=prod)
                

        if form.is_valid():
            try:
                if request.user.rol == "admin":
                    prod.fecha = str(datetime.today().strftime('%Y-%m-%d'))
                    prod.usuario = User.objects.get(id=request.user.id)
                    form.save()
                    messages.success(
                        request, f'Producto {prod.nombre} Modificado Exitosamente!')
                    if request.user.tienda == "Zacapa":
                        return redirect('ListaProductoZacapa')
                    elif request.user.tienda == "Estanzuela":
                        return redirect('ListaProductoEstanzuela')
                    elif request.user.tienda == "Teculutan":
                        return redirect('ListaProductoTeculutan')
                    elif request.user.tienda == "Santa Cruz":
                        return redirect('ListaProductoStaCruz')    
                    else:
                        return redirect('ListaProducto')
                else:
                    prod.fecha = str(datetime.today().strftime('%Y-%m-%d'))
                    prod.usuario = User.objects.get(id=request.user.id)
                    prod.tienda = request.user.tienda
                    form.save()
                    messages.success(
                        request, f'Producto {prod.nombre} Modificado Exitosamente!')
                    if request.user.tienda == "Zacapa":
                        return redirect('ListaProductoZacapa')
                    elif request.user.tienda == "Estanzuela":
                        return redirect('ListaProductoEstanzuela')
                    elif request.user.tienda == "Teculutan":
                        return redirect('ListaProductoTeculutan')
                    elif request.user.tienda == "Santa Cruz":
                        return redirect('ListaProductoStaCruz')      
                    else:
                        return redirect('ListaProducto')
                
            except:
                messages.error(request, f'No Se Pudo Modificar {prod.nombre}!')
                if request.user.tienda == "Zacapa":
                    return redirect('ListaProductoZacapa')
                elif request.user.tienda == "Estanzuela":
                    return redirect('ListaProductoEstanzuela')
                elif request.user.tienda == "Teculutan":
                    return redirect('ListaProductoTeculutan')
                elif request.user.tienda == "Santa Cruz":
                        return redirect('ListaProductoStaCruz')      
                else:
                    return redirect('ListaProducto')

    return render(request, 'Producto/actualizar.html', {'form': form, 'c': cate})


@login_required
def eliminar(request, id):
    try:
        prod = Producto.objects.get(id=id)
        prod.delete()
        messages.success(request, f'{prod.nombre} Eliminado!')
        return redirect('ListaProducto')
    except:
        messages.error(request, f'No Se Puede Elimnar {prod.nombre}')
        return redirect('ListaProducto')





# exportacion a excel
def excel(request, t):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        response = HttpResponse(content_type='application/ms-excel')
        response[
            'Content-Disposition'] = f'attachment; filename="todos_productos_general_{t}.xls"'

        wb = xlwt.Workbook(encoding='utf-8')
        ws = wb.add_sheet(f'Todos Los Productos')
        

        # Sheet header, first row
        row_num = 0
        print(t)
        font_style = xlwt.XFStyle()
        font_style.font.bold = True

        columns = ['id', 'nombre', 'descripcion', 'stock',
                   'precio_compra', 'precio_venta', 'id_cate', 'tienda']

        for col_num in range(len(columns)):
            ws.write(row_num, col_num, columns[col_num], font_style)

        # Sheet body, remaining rows
        font_style = xlwt.XFStyle()

        if t == "a":
            rows = Producto.objects.filter(tienda=t).values_list(
                'id', 'nombre', 'descripcion', 'stock', 'precio_compra', 'precio_venta', 'id_cate', 'tienda')
            for row in rows:
                row_num += 1
                for col_num in range(len(row)):
                    ws.write(row_num, col_num, row[col_num], font_style)
            rows = Producto.objects.all().values_list(
                'id', 'nombre', 'descripcion', 'stock', 'precio_compra', 'precio_venta', 'id_cate', 'tienda')
        else:
            rows = Producto.objects.filter(tienda=t).values_list(
                'id', 'nombre', 'descripcion', 'stock', 'precio_compra', 'precio_venta', 'id_cate', 'tienda')
            for row in rows:
                row_num += 1
                for col_num in range(len(row)):
                    ws.write(row_num, col_num, row[col_num], font_style)
            rows = Producto.objects.filter(tienda=t).values_list(
                'id', 'nombre', 'descripcion', 'stock', 'precio_compra', 'precio_venta', 'id_cate', 'tienda')

        wb.save(response)
        return response
        
        

def excelstacruz(request):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        response = HttpResponse(content_type='application/ms-excel')
        response[
            'Content-Disposition'] = f'attachment; filename="todos_productos_stacruz.xls"'

        wb = xlwt.Workbook(encoding='utf-8')
        ws = wb.add_sheet(f'Todos Los Productos Santa Cruz')

        # Sheet header, first row
        row_num = 0

        font_style = xlwt.XFStyle()
        font_style.font.bold = True

        columns = ['id', 'nombre', 'descripcion', 'stock',
                   'precio_compra', 'precio_venta', 'id_cate', 'tienda']

        for col_num in range(len(columns)):
            ws.write(row_num, col_num, columns[col_num], font_style)

        # Sheet body, remaining rows
        font_style = xlwt.XFStyle()

        rows = Producto.objects.filter(tienda="Santa Cruz").values_list(
            'id', 'nombre', 'descripcion', 'stock', 'precio_compra', 'precio_venta', 'id_cate', 'tienda')
        for row in rows:
            row_num += 1
            for col_num in range(len(row)):
                ws.write(row_num, col_num, row[col_num], font_style)
            rows = Producto.objects.filter(tienda="Santa Cruz").values_list(
                'id', 'nombre', 'descripcion', 'stock', 'precio_compra', 'precio_venta', 'id_cate', 'tienda')

        wb.save(response)
        return response        



def excelgualan(request):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        response = HttpResponse(content_type='application/ms-excel')
        response[
            'Content-Disposition'] = f'attachment; filename="todos_productos_gualan.xls"'

        wb = xlwt.Workbook(encoding='utf-8')
        ws = wb.add_sheet(f'Todos Los Productos Gualan')

        # Sheet header, first row
        row_num = 0

        font_style = xlwt.XFStyle()
        font_style.font.bold = True

        columns = ['id', 'nombre', 'descripcion', 'stock',
                   'precio_compra', 'precio_venta', 'id_cate', 'tienda']

        for col_num in range(len(columns)):
            ws.write(row_num, col_num, columns[col_num], font_style)

        # Sheet body, remaining rows
        font_style = xlwt.XFStyle()

        rows = Producto.objects.filter(tienda="Gualan").values_list(
            'id', 'nombre', 'descripcion', 'stock', 'precio_compra', 'precio_venta', 'id_cate', 'tienda')
        for row in rows:
            row_num += 1
            for col_num in range(len(row)):
                ws.write(row_num, col_num, row[col_num], font_style)
            rows = Producto.objects.filter(tienda="Gualan").values_list(
                'id', 'nombre', 'descripcion', 'stock', 'precio_compra', 'precio_venta', 'id_cate', 'tienda')

        wb.save(response)
        return response       
        
        
        
        
def bitacora(id, prod, t, d, h, i, s, hy, td, u):

    b = Bitacora()
    b.id_prod = 0
    b.prod = prod
    b.tipo = t
    b.doc = 0
    b.habia = h
    b.ingreso = i
    b.salio = s
    b.hay = hy
    b.tienda = td
    b.fecha = str(datetime.today())
    b.usuario = u
    b.save()        
    
######## BITACORA ##############
from django.shortcuts import render, get_object_or_404
from Producto.models import Producto, Bitacora, DetalleFactura
from Venta.models import Detalle
from datetime import datetime
from django.db.models import Q
from Proveedor.models import Proveedor

def bitacora_producto(request, producto_id):
    producto = get_object_or_404(Producto, id=producto_id)
    
    entradas = DetalleFactura.objects.filter(
        id_prod=producto_id
    ).select_related('factura', 'id_prod').order_by('fecha')  # <-- usuario quitado
    
    salidas = Detalle.objects.filter(
        id_prod=producto_id
    ).select_related('factura', 'id_prod').order_by('fecha')  # <-- usuario quitado

    # Obtener proveedor desde la última entrada
    proveedor = None
    if entradas.exists():
        proveedor = entradas.last().factura.id_prov  # asumiendo que factura tiene id_prov

    movimientos = []

    for entrada in entradas:
        movimientos.append({
            'fecha': entrada.fecha.strftime("%Y-%m-%d %H:%M:%S"),
            'tipo': 'ENTRADA',
            'documento': f"Factura {entrada.factura.factura}" if entrada.factura else 'N/A',
            'cantidad': entrada.cantidad,
            'salida': 0,
            'precio_unitario': getattr(entrada, 'compra_ahora', 0.00),
            'total': getattr(entrada, 'total', 0.00),
            'usuario': getattr(entrada, 'usuario', 'Sistema'),  # usuario directo sin relación
            'origen': 'compra'
        })

    for salida in salidas:
        cantidad_salida = getattr(salida, 'salio', getattr(salida, 'cantidad', 0))
        movimientos.append({
            'fecha': salida.fecha.strftime("%Y-%m-%d %H:%M:%S"),
            'tipo': 'SALIDA',
            'documento': f"Factura {salida.factura.factura}" if salida.factura else 'N/A',
            'cantidad': 0,
            'salida': cantidad_salida,
            'precio_unitario': getattr(salida, 'precio_unitario', 0.00),
            'total': getattr(salida, 'total', 0.00),
            'usuario': getattr(salida, 'usuario', 'Sistema'),  # usuario directo sin relación
            'origen': 'venta'
        })

    movimientos_ordenados = sorted(
        movimientos,
        key=lambda x: datetime.strptime(x['fecha'], "%Y-%m-%d %H:%M:%S")
    )

    stock_actual = producto.stock
    for mov in reversed(movimientos_ordenados):
        mov['stock_actual'] = stock_actual
        if mov['tipo'] == 'ENTRADA':
            stock_actual -= mov['cantidad']
        elif mov['tipo'] == 'SALIDA':
            stock_actual += mov['salida']
    movimientos_ordenados.reverse()

    total_comprado = sum(m['cantidad'] for m in movimientos_ordenados if m['tipo'] == 'ENTRADA')
    total_vendido = sum(m['salida'] for m in movimientos_ordenados if m['tipo'] == 'SALIDA')

    return render(request, 'Producto/movimiento_producto.html', {
        'producto': producto,
        'movimientos': movimientos_ordenados,
        'resumen': {
            'total_comprado': total_comprado,
            'total_vendido': total_vendido,
        },
        'proveedor': proveedor,
    })

