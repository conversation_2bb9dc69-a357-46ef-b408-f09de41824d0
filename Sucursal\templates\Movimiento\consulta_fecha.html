{% extends 'Base/base.html' %}
{% block title %}Consulta de Ventas Por Fecha{% endblock %}

{% block content %}

{% if messages %}
{% for message in messages %}
<script>
    Swal.fire({
        "title": "Informacion Sistema",
        "text": "{{message}}",
        "icon": "{{message.tags}}"
    })
</script>
{% endfor %}
{% endif %}

<div class="content-wrapper">

    <div class="container-xxl flex-grow-1 container-p-y">
      <a href="{% url 'Consulta' %}"><button class="btn btn-info">Regresar</button></a><br></br>

        <!-- Basic Layout -->
        <div class="col-md-12">
            <div class="card md-6">
                <div class="card-header d-flex align-items-center justify-content-between">
                    <h5 class="mb-0">Consulta de Venta en Todas Las Sucursales</h5>
                    <small class="text-muted float-end">Consulta de Ventas en Sucursales</small>
                </div>
                <div class="card-body">
                    <form action="#" method="POST" enctype="multipart/form-data">{% csrf_token %}
                        <div class="row">
                            <div class="col-md-4">
                                <label>Tienda</label>
                                {% if user.rol == 'admin' %}
                                <select name="tienda" class="form-control" required>
                                    <option value="Todas">Todas</option>
                                    <option value="Estanzuela">Estanzuela</option>
                                    <option value="Teculutan">Teculutan</option>
                                    <option value="Zacapa">Zacapa</option>
                                    <option value="Santa Cruz">Santa Cruz</option>
                                    <option value="Gualan">Gualan</option>
                                </select>
                                {% else %}
                                <select name="tienda" class="form-control" required>
                                  <option value="{{user.tienda}}">{{user.tienda}}</option>
                              </select>
                                {% endif %}
                            </div>
                            <div class="col-md-4">
                                <label>Fecha Inicio</label>
                                <input type="date" class="form-control" required name="inicio">
                            </div>
                            <div class="col-md-4">
                                <label>Fecha Fin</label>
                                <input type="date" class="form-control" required name="fin">
                            </div>
                            
                        </div><br>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary">Consultar</button>
                                <button type="reset" class="btn btn-danger">Cancelar</button>
                            </div>
                        </div>
                    
                    </form>
            </div>


        </div>

        {% if b %}

        <div class="content-wrapper">

            <div class="container-xxl flex-grow-1 container-p-y">
          
              <div class="row">
                <!-- Basic Layout -->
                <div class="col-md-12">
                  <div class="card md-6">
                    <div class="card-header d-flex align-items-center justify-content-between">
                      <h5 class="mb-0">Listado de Ventas</h5>
                      <small class="text-muted float-end">Rango Fechas <strong class="text-danger">{{i}}</strong> al <strong class="text-danger">{{f}}</strong></small><br>
                      <small class="text-muted float-end">Ventas Hechas <strong class="text-danger">{{c}}</strong></small>
                      <small class="text-muted float-end">Total Ventas Solo Terminadas <strong class="text-danger">Q.{{t}}</strong></small>
                    </div>
                    <div class="card-body">
                      <div class="table-responsive-sm text-nowrap">
          
                        <input class="form-control col-md-3 light-table-filter" data-table="order-table" type="text"
                          placeholder="Buscar.." style="border: 1px solid black;" name="buscar" autofocus="buscar"><br>
          
                        <table class="table table-bordered table-sm order-table">
                          <thead>
                            <tr>
                              <th>Correlativo</th>
                              <th>Tipo</th>
                              <th>Tienda</th>
                              <th>Nit</th>
                              <th>Nombre Cliente</th>
                              <th>Total</th>
                              <th>Fecha</th>
                              <th>Estado</th>
                              <th>Action</th>
                            </tr>
                          </thead>
                          <tbody>
                            {% for v in venta %}
                            <tr>
                              {% if v.tienda == "Estanzuela" %}
                              <td>{{v.interno_estanzuela}}.1</td>
                              {% elif v.tienda == "Teculutan" %}
                              <td>{{v.interno_tecu}}.2</td>
                              {% elif v.tienda == "Zacapa" %}
                              <td>{{v.interno_zacapa}}.3</td>
                              {% elif v.tienda == "Gualan" %}
                              <td>{{v.interno_gualan}}.5</td>
                              {% else %}
                              <td>{{v.interno_rio_hondo}}.4</td>
                              {% endif %}
                              <td>{{v.tipo}}</td>
                              <td>{{v.tienda}}</td>
                              <td>{{v.nit}}</td>
                              <td>{{v.nombre}}</td>
                              <td>Q.{{v.total}}</td>
                              <td>{{v.fecha|date:'d-m-Y'}}</td>
                              {% if v.estado == 1 %}
                              <td>Terminada</td>
                              {% elif v.estado == 2 %}
                              <td>Anulada</td>
                              {% elif v.estado == 3 %}
                              <td>Credito</td>
                              {% else %}
                              <td>En Proceso</td>
                              {% endif %}
                              <td>
                                <a href="{% url 'Ver2Consulta' v.token %}">
                                  <i style="color: blue;" class='bx bxs-navigation' title="Ver"></i>
                                </a>
                              </td>
                              
                            </tr>
                            {% empty %}
                            <caption>SIN VENTAS</caption>
                            {% endfor %}
          
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
          
              </div>
          
            </div>
          
          </div>
          
          <script type="text/javascript">
              (function (document) {
                'use strict';
          
                var LightTableFilter = (function (Arr) {
          
                  var _input;
          
                  function _onInputEvent(e) {
                    _input = e.target;
                    var tables = document.getElementsByClassName(_input.getAttribute('data-table'));
                    Arr.forEach.call(tables, function (table) {
                      Arr.forEach.call(table.tBodies, function (tbody) {
                        Arr.forEach.call(tbody.rows, _filter);
                      });
                    });
                  }
          
                  function _filter(row) {
                    var text = row.textContent.toLowerCase(), val = _input.value.toLowerCase();
                    row.style.display = text.indexOf(val) === -1 ? 'none' : 'table-row';
                  }
          
                  return {
                    init: function () {
                      var inputs = document.getElementsByClassName('light-table-filter');
                      Arr.forEach.call(inputs, function (input) {
                        input.oninput = _onInputEvent;
                      });
                    }
                  };
                })(Array.prototype);
          
                document.addEventListener('readystatechange', function () {
                  if (document.readyState === 'complete') {
                    LightTableFilter.init();
                  }
                });
          
              })(document);
          </script>
        
        {% else %}

        {% endif %}  

    </div>

</div>



{% endblock %}