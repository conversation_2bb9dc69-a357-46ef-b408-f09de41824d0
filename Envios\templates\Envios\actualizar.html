{% extends 'Base/base.html' %}
{% block title %}Actualizar Envio{% endblock %}

{% block content %}

{% if messages %}
{% for message in messages %}
<script>
    Swal.fire({
        "title": "Informacion Sistema",
        "text": "{{message}}",
        "icon": "{{message.tags}}"
    })
</script>
{% endfor %}
{% endif %}

{{form.errors}}
<div class="content-wrapper">

    <div class="container-xxl flex-grow-1 container-p-y">


        <!-- Basic Layout -->
        <div class="col-md-12">
            <div class="card md-6">
                <div class="card-header d-flex align-items-center justify-content-between">
                    <h5 class="mb-0">Formulario Actualizacion Envio</h5>
                    <small class="text-muted float-end">Envios</small>
                </div>
                <div class="card-body">
                    <form action="#" method="POST" enctype="multipart/form-data">{% csrf_token %}
                        <div class="row">
                            <div class="col-md-2">
                                <label>Nit Sucursal</label>
                                {{form.nit}}
                            </div>
                            <div class="col-md-4">
                                <label>Nombre
                                    Sucursal</label>
                                {{form.nombre}}
                            </div>
                            <div class="col-md-3">
                                <label>Telefono
                                    Sucursal</label>
                                {{form.telefono}}
                            </div>
                            <div class="col-md-3">
                                <label>Telefono Auxiliar
                                    Sucursal</label>
                                {{form.telefono2}}
                            </div>
                        </div><br>
                        <div class="row">
                            <div class="col-md-4">
                                <label>Correo Electronico</label>
                                {{form.correo}}
                            </div>
                            <div class="col-md-4">
                                <label>Direccion</label>
                                {{form.direccion}}
                            </div>
                            <div class="col-md-4">
                                <label>Ubicacion</label>
                                {{form.ubicacion}}
                            </div>
                        </div><br>
                        <div class="row">
                            <div class="col-md-4">
                                <label>Fecha</label>
                                <input type="text" class="form-control" readonly value="{% now 'd-m-Y' %}" />
                            </div>
                            <div class="col-md-4">
                                <label>Usuario</label>
                                <input type="text" class="form-control" readonly value="{{user.username}}" />
                            </div>
                        </div><br>
                        <div class="row">
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary">Actualizar</button>
                                <button type="reset" class="btn btn-danger">Cancelar</button>
                            </div>
                        </div>
                </div>
                </form>
            </div>
        </div>


    </div>

</div>



{% endblock %}