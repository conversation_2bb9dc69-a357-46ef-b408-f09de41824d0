from django.shortcuts import render, redirect
from datetime import datetime
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from Categoria.models import Categoria
from Producto.models import Producto
from Producto.forms import ProductoForm, UpdateProductoForm
from user.models import User
import xlwt
from django.http import HttpResponse

# exportacion a excel


def exceltodo(request):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        response = HttpResponse(content_type='application/ms-excel')
        response[
            'Content-Disposition'] = f'attachment; filename="todos_productos_general.xls"'

        wb = xlwt.Workbook(encoding='utf-8')
        ws = wb.add_sheet(f'Todos Los Productos')

        # Sheet header, first row
        row_num = 0

        font_style = xlwt.XFStyle()
        font_style.font.bold = True

        columns = ['id', 'nombre', 'descripcion', 'stock',
                   'precio_compra', 'precio_venta', 'id_cate', 'tienda']

        for col_num in range(len(columns)):
            ws.write(row_num, col_num, columns[col_num], font_style)

        # Sheet body, remaining rows
        font_style = xlwt.XFStyle()

        rows = Producto.objects.all().values_list(
            'id', 'nombre', 'descripcion', 'stock', 'precio_compra', 'precio_venta', 'id_cate', 'tienda')
        for row in rows:
            row_num += 1
            for col_num in range(len(row)):
                ws.write(row_num, col_num, row[col_num], font_style)
            rows = Producto.objects.all().values_list(
                'id', 'nombre', 'descripcion', 'stock', 'precio_compra', 'precio_venta', 'id_cate', 'tienda')

        wb.save(response)
        return response
