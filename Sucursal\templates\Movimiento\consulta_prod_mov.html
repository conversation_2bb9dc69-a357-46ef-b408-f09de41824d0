{% extends 'Base/base.html' %}
{% block title %}Consulta de Movimientos Por Producto{% endblock %}

{% block content %}

{% if messages %}
{% for message in messages %}
<script>
  Swal.fire({
    "title": "Informacion Sistema",
    "text": "{{message}}",
    "icon": "{{message.tags}}"
  })
</script>
{% endfor %}
{% endif %}

<div class="content-wrapper">

  <div class="container-xxl flex-grow-1 container-p-y">
    <a href="{% url 'Consulta' %}"><button class="btn btn-info">Regresar</button></a><br></br>

    <!-- Basic Layout -->
    <div class="col-md-12">
      <div class="card md-6">
        <div class="card-header d-flex align-items-center justify-content-between">
          <h5 class="mb-0">Consulta de Movimientos de Productos en Tiendas</h5>
          <small class="text-muted float-end">Consulta de Productos Tiendas</small>
        </div>
        <div class="card-body">
          <form action="#" method="POST" enctype="multipart/form-data">{% csrf_token %}
            <div class="row">
              <!--<div class="col-md-4">
                <label>Producto</label>
                <input type="text" class="form-control" name="buscar" required>
              </div>-->
              <div class="col-md-4">
                <label>Tienda</label>
                {% if user.rol == 'admin' %}
                <select name="tienda" class="form-control" required>
                  <option value="Todas">Todas</option>
                  <option value="Estanzuela">Estanzuela</option>
                  <option value="Teculutan">Teculutan</option>
                  <option value="Zacapa">Zacapa</option>
                  <option value="Santa Cruz">Santa Cruz</option>
                  <option value="Gualan">Gualan</option>
                </select>
                {% else %}
                <select name="tienda" class="form-control" required>
                  <option value="{{user.tienda}}">{{user.tienda}}</option>
                </select>
                {% endif %}
              </div>

            </div><br>

            <div class="row">
              <div class="col-md-4">
                <button type="submit" class="btn btn-primary">Consultar</button>
                <a href="{% url 'Inicio' %}" class="btn btn-danger">Terminar</a>
              </div>
            </div>

          </form>
        </div>


      </div>

      {% if b %}

      <div class="content-wrapper">

        <div class="container-xxl flex-grow-1 container-p-y">

          <div class="row">
            <!-- Basic Layout -->
            <div class="col-md-12">
              <div class="card md-6">
                <div class="card-header d-flex align-items-center justify-content-between">
                  <h5 class="mb-0">Listado de Movimientos</h5>
                </div>
                <div class="card-body">
                  <div class="table-responsive-sm text-nowrap">

                    <input class="form-control col-md-3 light-table-filter" data-table="order-table" type="text"
                      placeholder="Buscar.." style="border: 1px solid black;" name="buscar" autofocus="buscar"><br>

                    <table class="table table-bordered table-sm order-table">
                      <thead>
                        <tr>
                          <th>Producto</th>
                          <th>Tienda</th>
                          <th>Stock</th>
                          <th>Actions</th>

                        </tr>
                      </thead>
                      <tbody>
                        {% for p in prod %}
                        <tr>
                          <td>{{p.nombre}}</td>
                          <td>{{p.tienda}}</td>
                          <td>{{p.stock}}</td>
                          <td><a href="javascript:popUp('{% url 'ConsultaVerProd' p.id %}')"
                              class="btn btn-sm btn-info"><i class='bx bx-search-alt'></i></a>
                            <a href="javascript:historial('{% url 'Historial' p.id %}')">Historial</a></td>
                        </tr>
                        {% endfor %}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>

          </div>

        </div>

      </div>

      <script type="text/javascript">
        function popUp(URL) {
          window.open(URL, 'Ver Detalle', 'toolbar=0,scrollbars=0,location=0,statusbar=0,menubar=0,resizable=1,width=1000,height=500,left = 590,top = 150');
        }
      </script>

      
<script type="text/javascript">
  function historial(URL) {
    window.open(URL, 'Ver Historial', 'toolbar=0,scrollbars=0,location=0,statusbar=0,menubar=0,resizable=1,width=1000,height=500,left = 590,top = 150');
  }
</script>

      <script type="text/javascript">
        (function (document) {
          'use strict';

          var LightTableFilter = (function (Arr) {

            var _input;

            function _onInputEvent(e) {
              _input = e.target;
              var tables = document.getElementsByClassName(_input.getAttribute('data-table'));
              Arr.forEach.call(tables, function (table) {
                Arr.forEach.call(table.tBodies, function (tbody) {
                  Arr.forEach.call(tbody.rows, _filter);
                });
              });
            }

            function _filter(row) {
              var text = row.textContent.toLowerCase(), val = _input.value.toLowerCase();
              row.style.display = text.indexOf(val) === -1 ? 'none' : 'table-row';
            }

            return {
              init: function () {
                var inputs = document.getElementsByClassName('light-table-filter');
                Arr.forEach.call(inputs, function (input) {
                  input.oninput = _onInputEvent;
                });
              }
            };
          })(Array.prototype);

          document.addEventListener('readystatechange', function () {
            if (document.readyState === 'complete') {
              LightTableFilter.init();
            }
          });

        })(document);
      </script>

      {% else %}

      {% endif %}

    </div>

  </div>



  {% endblock %}