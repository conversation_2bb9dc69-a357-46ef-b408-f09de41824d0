# Generated by Django 4.1.7 on 2023-09-13 15:19

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Categoria', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Producto',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nombre', models.CharField(max_length=550)),
                ('descripcion', models.CharField(max_length=550)),
                ('stock', models.IntegerField(default=0)),
                ('ingreso', models.IntegerField(default=0)),
                ('salio', models.IntegerField(default=0)),
                ('precio_compra', models.DecimalField(decimal_places=2, default='0.00', max_digits=12)),
                ('precio_venta', models.DecimalField(decimal_places=2, default='0.00', max_digits=12)),
                ('tienda', models.CharField(max_length=550)),
                ('fecha', models.CharField(max_length=10)),
                ('id_cate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Categoria.categoria')),
                ('usuario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['nombre'],
            },
        ),
    ]
