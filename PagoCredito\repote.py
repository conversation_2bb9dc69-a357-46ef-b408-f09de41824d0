from decimal import Decimal
from io import BytesIO
from datetime import datetime
from reportlab.lib.pagesizes import A4,landscape
from reportlab.lib.styles import ParagraphStyle, TA_CENTER,TA_LEFT
from reportlab.lib.units import inch, mm,cm
from reportlab.lib import colors
from reportlab.platypus import (
        Paragraph, 
        Table, 
        SimpleDocTemplate, 
        Spacer, 
        TableStyle, 
        Paragraph)
from reportlab.lib.styles import getSampleStyleSheet
from django.db.models import Sum
from reportlab.platypus import Image

#from .models import Persona
from Venta.models import Venta,Detalle
from PagoCredito.models import Pago

class Comprobante():

    def __init__(self):
        self.buf = BytesIO()
        self.rastreo = Venta.objects.filter(estado=3)
        self.r2 = Venta.objects.filter(estado=3).aggregate(to=Sum('total'))
        
        

    def run(self):
        self.doc = SimpleDocTemplate(self.buf,title=f"Estado-de-Cuenta",pagesize=A4)
        self.story = []
        self.encabezado()
        self.crearTabla()
        self.doc.build(self.story, onFirstPage=self.numeroPagina, 
            onLaterPages=self.numeroPagina)
        pdf = self.buf.getvalue()
        self.buf.close()
        return pdf

    def encabezado(self):
        imagen_logo = Image('Venta/logo.jpg', width=95, height=85,hAlign='RIGHT')
        p = Paragraph("CORPORACION SANTA ROSALIA ZACAPA", self.estiloPC())
        p1 = Paragraph(f"TIENDA ..... ", self.estiloPC())
        a = Paragraph(f"ESTADO DE CUENTA", self.estiloPC())
        b = Paragraph(f"Total de Creditos Q.{self.r2['to']}", self.estiloPC2())
        a4 = Paragraph(f"Fecha de Impresion {datetime.today().strftime('%d/%m/%Y, %H:%M:%S')}", self.estiloPC3())
        self.story.append(imagen_logo)
        self.story.append(p)
        self.story.append(p1)
        self.story.append(a)
        self.story.append(a4)
        self.story.append(b)
        self.story.append(Spacer(1,0.2*inch))

              

    def crearTabla(self):
        r = Venta.objects.filter(estado=3).aggregate(total=Sum('total'))
        a = Pago.objects.all().aggregate(ab=Sum('abono'))
        print(r)
        data = [["ID","Cliente","Credito","Fecha Pago","Abono"]] \
            +[[x.id, x.factura.nombre,x.factura,x.fecha,"Q."+str(x.abono)] 
                for x in Pago.objects.all()]\
                +[["Total de Abonos","","","","Q."+str(a['ab'])]]
                
        table = Table(data,colWidths=[4.5*cm,4.5*cm,2.5*cm,2.5*cm,2.7*cm,2.2*cm])
        table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), "CENTER"),
        ('VALIGN',(-1,-1),(-1,-1),'MIDDLE'),
        ('FONTSIZE', (0, 0), (-1, -2), 7),
        ('GRID', (0,0), (-1,-1), 0.5, colors.gray)
    ]))    

        self.story.append(table)

    def estiloPC(self):
        return ParagraphStyle(name='centrado',
                           fontName="Helvetica-Bold",
                           fontSize=10,
                           alignment=1,
                           spaceAfter=7,)

    def estiloPC2(self):
        return ParagraphStyle(name='izquierda',
                           fontName="Helvetica",
                           fontSize=10,
                           alignment=0,
                           spaceAfter=7,
                           leftIndent = -30,
                           borderColor = '#FF5733' )

    def estiloPC3(self):
        return ParagraphStyle(name='derecha',
                           fontName="Helvetica",
                           fontSize=10,
                           alignment=2,
                           spaceAfter=2,
                           rightIndent = -30,)                       


    def numeroPagina(self,canvas,doc):
        num = canvas.getPageNumber()
        text = "Pagina %s" % num
        canvas.drawRightString(200*mm, 20*mm, text)







class Ticket():

    def __init__(self,t):
        self.buf = BytesIO()
        self.rastreo = Pago.objects.filter(factura=t).order_by('id').last()
        self.todo = Pago.objects.filter(factura=t).aggregate(total=Sum('abono'))

        

    def run(self):
        self.doc = SimpleDocTemplate(self.buf,title=f"Comprobante {self.rastreo.factura}",pagesize=A4)
        self.story = []
        self.encabezado()
        self.crearTabla()
        self.doc.build(self.story, onFirstPage=self.numeroPagina, 
            onLaterPages=self.numeroPagina)
        pdf = self.buf.getvalue()
        self.buf.close()
        return pdf

    def encabezado(self):
        
        imagen_logo = Image('Venta/logo.jpg', width=95, height=85,hAlign='RIGHT')
        p = Paragraph("CORPORACION SANTA ROSALIA ZACAPA", self.estiloPC())
        p1 = Paragraph(f"TIENDA ..... ", self.estiloPC())
        p11 = Paragraph("LUGAR,ZACAPA", self.estiloPC())
        t1 = Paragraph(" TEL: 0000-0000 ", self.estiloPC())
        a = Paragraph(f"COMPROBANTE PAGO # {self.rastreo.id}", self.estiloPC2())
        a1 = Paragraph(f"CLIENTE {self.rastreo.factura.nombre}", self.estiloPC2())
        a1_1 = Paragraph(f"TIPO {self.rastreo.factura.tipo}", self.estiloPC2())
        a1_1 = Paragraph(f"PENDIENTE {self.rastreo.factura.total-self.todo['total']}", self.estiloPC3())
        a3 = Paragraph(f"FECHA CREDITO {self.rastreo.fecha}", self.estiloPC2())
        a4 = Paragraph(f"Fecha de Impresion {datetime.today().strftime('%d/%m/%Y, %H:%M:%S')}", self.estiloPC3())
        self.story.append(imagen_logo)
        self.story.append(p)
        self.story.append(p1)
        self.story.append(p11)
        self.story.append(t1)
        self.story.append(a)
        self.story.append(a1)
        self.story.append(a1_1)
        self.story.append(a3)
        self.story.append(a4)
        self.story.append(Spacer(1,0.2*inch))

              

    def crearTabla(self):
        data = [["ID","Credito","Cantidad Abono","Fecha Pago"]] \
            +[[self.rastreo.id,self.rastreo.factura,"Q."+str(self.rastreo.abono),self.rastreo.fecha]]
                
        table = Table(data,colWidths=[4.5*cm,2.5*cm,2.5*cm,2.7*cm,2.2*cm])
        table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), "CENTER"),
        ('VALIGN',(-1,-1),(-1,-1),'MIDDLE'),
        ('FONTSIZE', (0, 0), (-1, -2), 7),
        ('GRID', (0,0), (-1,-1), 0.5, colors.gray)
    ]))    

        self.story.append(table)

    def estiloPC(self):
        return ParagraphStyle(name='centrado',
                           fontName="Helvetica-Bold",
                           fontSize=10,
                           alignment=1,
                           spaceAfter=7,)

    def estiloPC2(self):
        return ParagraphStyle(name='izquierda',
                           fontName="Helvetica",
                           fontSize=10,
                           alignment=0,
                           spaceAfter=7,
                           leftIndent = -30,
                           borderColor = '#FF5733' )

    def estiloPC3(self):
        return ParagraphStyle(name='derecha',
                           fontName="Helvetica",
                           fontSize=10,
                           alignment=2,
                           spaceAfter=2,
                           rightIndent = -30,)                       


    def numeroPagina(self,canvas,doc):
        num = canvas.getPageNumber()
        text = "Pagina %s" % num
        canvas.drawRightString(200*mm, 20*mm, text)