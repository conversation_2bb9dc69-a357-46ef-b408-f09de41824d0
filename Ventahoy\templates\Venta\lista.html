{% extends 'Base/base.html' %}
{% block title %}Listado Ventas{% endblock %}

{% block content %}

{% if messages %}
{% for message in messages %}
<script>
  Swal.fire({
    "title": "Informacion Sistema",
    "text": "{{message}}",
    "icon": "{{message.tags}}"
  })
</script>
{% endfor %}
{% endif %}

<div class="content-wrapper">

  <div class="container-xxl flex-grow-1 container-p-y">

    <div class="row">
      <!-- Basic Layout -->
      <div class="col-md-16">
        <div class="card md-6">
          <div class="card-header d-flex align-items-center justify-content-between">
            <h5 class="mb-0">Listado de Ventas</h5>
            <small class="text-muted float-end">Lista Venta</small>
          </div>
          <div class="card-body">
            <div class="table-responsive text-nowrap">

              <input class="form-control col-md-3 light-table-filter" data-table="order-table" type="text"
                placeholder="Buscar.." style="border: 1px solid black;" name="buscar" autofocus="buscar"><br>

              <table class="table table-bordered order-table">
                <thead>
                  <tr>
                    <th>Factrua</th>
                    <th>Nit</th>
                    <th>Nombre Cliente</th>
                    <th>Direccion</th>
                    <th>Tipo</th>
                    <th>Total</th>
                    <th>Fecha</th>
                    <th>Estado</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for v in venta %}
                  <tr>
                    <td>{{v.factura}}</td>
                    <td>{{v.nit}}</td>
                    <td>{{v.nombre}}</td>
                    <td>{{v.direccion}}</td>
                    <td>{{v.tipo}}</td>
                    <td>Q.{{v.total}}</td>
                    <td>{{v.fecha}}</td>
                    {% if v.estado == 1 %}
                    <td>Terminada</td>
                    {% elif v.estado == 2 %}
                    <td>Anulada</td>
                    {% elif v.estado == 3 %}
                    <td>Credito</td>
                    {% else %}
                    <td>En Proceso</td>
                    {% endif %}
                    <td>
                      {% if v.estado == 2 %}
                      {% else %}
                      <a href="{% url 'Detalle' v.token %}">
                        <i style="color: yellowgreen;" class='bx bxs-edit' title="Modificar"></i>
                      </a>
                      {% endif %}
                      <a href="{% url 'Ver' v.token %}">
                        <i style="color: blue;" class='bx bxs-navigation' title="Ver"></i>
                      </a>
                      {% if user.rol == "admin" %}
                      {% else %}
                      {% if v.tipo == "FEL" %}
                      <a href="{{ v.link }}" target="_blank">
                        <i style="color: black;" class='bx bxs-file' title="Ver"></i>
                      </a>
                      {% elif v.tipo == "FEL-Servicio" %}
                      <a href="{{ v.link }}" target="_blank">
                        <i style="color: black;" class='bx bxs-file' title="Ver"></i>
                      </a>
                      {% else %}
                      <a href="{% url 'PDF' v.factura %}">
                        <i style="color: black;" class='bx bxs-file' title="Ver"></i>
                      </a>
                      {% endif %}
                      {% endif %}
                      {% if v.estado == 2 %}
                      {% else %}
                      {% if v.tipo == "FEL" %}
                      <a href="{% url 'AnulaFEL' v.token %}">
                        <i style="color: red;" class='bx bx-window-close' title="Anular"></i>
                      </a>
                      {% elif v.tipo == "FEL-Servicio" %}
                      <a href="{% url 'AnulaFEL' v.token %}">
                        <i style="color: red;" class='bx bx-window-close' title="Anular"></i>
                      </a>
                      {% else %}
                      <a href="{% url 'AnulaDetalle' v.factura %}">
                        <i style="color: red;" class='bx bx-window-close' title="Anular"></i>
                      </a>
                      {% endif %}
                      {% endif %}
                    </td>
                  </tr>
                  {% empty %}
                  <caption>SIN VENTAS</caption>
                  {% endfor %}

                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

    </div>

  </div>

</div>

<script type="text/javascript">
    (function (document) {
      'use strict';

      var LightTableFilter = (function (Arr) {

        var _input;

        function _onInputEvent(e) {
          _input = e.target;
          var tables = document.getElementsByClassName(_input.getAttribute('data-table'));
          Arr.forEach.call(tables, function (table) {
            Arr.forEach.call(table.tBodies, function (tbody) {
              Arr.forEach.call(tbody.rows, _filter);
            });
          });
        }

        function _filter(row) {
          var text = row.textContent.toLowerCase(), val = _input.value.toLowerCase();
          row.style.display = text.indexOf(val) === -1 ? 'none' : 'table-row';
        }

        return {
          init: function () {
            var inputs = document.getElementsByClassName('light-table-filter');
            Arr.forEach.call(inputs, function (input) {
              input.oninput = _onInputEvent;
            });
          }
        };
      })(Array.prototype);

      document.addEventListener('readystatechange', function () {
        if (document.readyState === 'complete') {
          LightTableFilter.init();
        }
      });

    })(document);
</script>


{% endblock %}