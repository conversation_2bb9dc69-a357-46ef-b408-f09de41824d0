from Venta import views,viewsanalisis
from django.urls import path

urlpatterns = [
    path('',views.nueva,name="NuevaVenta"),
    path('proforma-servicio/',views.proformaser,name="ProformaServicio"),
    path('detalle/<str:t>/',views.detalle,name="Detalle"),
    path('verdetalle/<str:t>/',views.ver,name="Ver"),
    path('listadoventas/',views.listado,name="ListaVenta"),
    path('anular/<int:id>/',views.anular,name="AnulaDetalle"),
    path('anularfel/<str:t>/',views.anularfel,name="AnulaFEL"),
    path('comprobante/<int:f>',views.pdf,name='PDF'),
    path('proforma-servicio-pdf/<str:v>/<str:d>/<str:c>/<str:p>/<str:t>/<str:cli>',views.pdfproformaservicio,name='PDFProformaServicio'),

    path('analisisventas/',viewsanalisis.analisisgeneral,name='AnalisisVentas'),

]
