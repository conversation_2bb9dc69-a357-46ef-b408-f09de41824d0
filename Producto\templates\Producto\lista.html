{% extends 'Base/base.html' %}
{% block title %}Lista Productos{% endblock %}

{% block content %}

{% if messages %}
{% for message in messages %}
<script>
  Swal.fire({
    "title": "Informacion Sistema",
    "text": "{{message}}",
    "icon": "{{message.tags}}"
  })
</script>
{% endfor %}
{% endif %}

<div class="content-wrapper">

  <div class="container-xxl flex-grow-1 container-p-y">

    <div class="row">
      <!-- Basic Layout -->
      <div class="col-md-14">
        <div class="card md-8">
          <div class="card-header d-flex align-items-center justify-content-between">
            <h5 class="mb-0">Listado de Productos</h5>
            <small class="text-muted float-end">Lista Productos</small>
            {% if user.rol == "admin" %}
            <a href="{% url 'ExcelTodo' %}" target="_blank">Excel General</a>
            <a href="{% url 'Excel' 'Zacapa' %}" target="_blank">Excel Zacapa</a>
            <a href="{% url 'Excel' 'Estanzuela' %}" target="_blank">Excel Estanzuela</a>
            <a href="{% url 'Excel' 'Teculutan' %}" target="_blank">Excel Teculutan</a>
              <a href="{% url 'Excel' 'Gualan' %}" target="_blank">Excel Gualan</a>
            {% elif user.tienda == "Zacapa" %}
            <a href="{% url 'Excel' 'Zacapa' %}" target="_blank">Excel Zacapa</a>
            {% elif user.tienda == "Estanzuela" %}
            <a href="{% url 'Excel' 'Estanzuela' %}" target="_blank">Excel Estanzuela</a>
            {% elif user.tienda == "Teculutan" %}
            <a href="{% url 'Excel' 'Teculutan' %}" target="_blank">Excel Teculutan</a>
              {% elif user.tienda == "Gualan" %}
            <a href="{% url 'Excel' 'Gualan' %}" target="_blank">Excel Gualan</a>
            {% else %}
            {% endif %}
          </div>
          <div class="card-body">
            <div class="table-responsive-sm">

              <input class="form-control col-md-3 light-table-filter" data-table="order-table" type="text"
            placeholder="Buscar.." style="border: 1px solid black;" name="buscar" autofocus="buscar"><br>
              
            <table class="table table-bordered order-table">
                <thead>
                  <tr>
                    <th>Id</th>
                    <th>Producto</th>
                    <th>Stock</th>
                    <th>Precio Compra</th>
                    <th>Precio Venta</th>
                    <th>Categoria</th>
                    <th>Tienda</th>
                    <th>Actions</th>
                    <th>Bitacora</th>
                  </tr>
                </thead>
                <tbody>
                  {% for p in prod %}
                  <tr>
                    <td>{{p.id}}</td>
                    <td>{{p.nombre}}</td>
                    <td>{{p.stock}}</td>
                    <td>Q.{{p.precio_compra}}</td>
                    <td>Q.{{p.precio_venta}}</td>
                    <td>{{p.id_cate.nombre}}</td>
                    <td>{{p.tienda}}</td>
                    <td>
                      <a href="{% url 'UpdateProducto' p.id %}">
                        <i style="color: yellowgreen;" class='bx bxs-edit' title="Modificar"></i>
                      </a>
                      <a href="{% url  'DeleteProducto' p.id %}">
                        <i style="color: red;" class='bx bxs-trash' title="Eliminar"></i>
                      </a>
                    </td>
                    <td>
                      <a href="{% url 'bitacora_producto' p.id %}">
                     <i style="color: blue;" class='bx bx-history' title="Bitácora">Bitácora</i>
                    </a>

                    </td>
                  </tr>
                  {% empty %}
                  <caption>SIN PRODUCTOS</caption>
                  {% endfor %}

                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
  (function (document) {
      'use strict';

      var LightTableFilter = (function (Arr) {

          var _input;

          function _onInputEvent(e) {
              _input = e.target;
              var tables = document.getElementsByClassName(_input.getAttribute('data-table'));
              Arr.forEach.call(tables, function (table) {
                  Arr.forEach.call(table.tBodies, function (tbody) {
                      Arr.forEach.call(tbody.rows, _filter);
                  });
              });
          }

          function _filter(row) {
              var text = row.textContent.toLowerCase(), val = _input.value.toLowerCase();
              row.style.display = text.indexOf(val) === -1 ? 'none' : 'table-row';
          }

          return {
              init: function () {
                  var inputs = document.getElementsByClassName('light-table-filter');
                  Arr.forEach.call(inputs, function (input) {
                      input.oninput = _onInputEvent;
                  });
              }
          };
      })(Array.prototype);

      document.addEventListener('readystatechange', function () {
          if (document.readyState === 'complete') {
              LightTableFilter.init();
          }
      });

  })(document);
</script>

{% endblock %}