{% extends 'Base/base.html' %}
{% block title %}Listado Envios{% endblock %}

{% block content %}

{% if messages %}
{% for message in messages %}
<script>
  Swal.fire({
    title: "Información Sistema",
    text: "{{ message }}",
    icon: "{{ message.tags }}",
    confirmButtonText: "Aceptar"
  }).then((result) => {
    if (result.isConfirmed) {
      window.location.reload();
    }
  });
</script>
{% endfor %}
{% endif %}


<div class="content-wrapper">

  <div class="container-xxl flex-grow-1 container-p-y">

    <div class="row">
      <div class="col-md-12">
        <div class="card md-10">
          <div class="card-header d-flex align-items-center justify-content-between">
            <h5 class="mb-0">Listado de Envios</h5>
            <small class="text-muted float-end">Lista Envios</small>
          </div>
          <div class="card-body">
            <div class="table-responsive text-nowrap">

              <input class="form-control col-md-3 light-table-filter" data-table="order-table" type="text"
                placeholder="Buscar.." style="border: 1px solid black;" name="buscar" autofocus="buscar"><br>

              <table class="table table-bordered order-table table-sm">
                <thead>
                  <tr>
                    <th>Envio #</th>
                    <th>Origen</th>
                    <th>Destino</th>
                    <th>Total Envio</th>
                    <th>Fecha Envio</th>
                    <th>Estado Envio</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for s in envios %}
                  <tr>
                    {% if s.origen == "Estanzuela" %}
                    <td>{{s.envio_estan}}.1</td>
                    {% elif s.origen == "Teculutan" %}
                    <td>{{s.envio_tecu}}.2</td>
                    {% elif s.origen == "Zacapa" %}
                    <td>{{s.envio_zacapa}}.3</td>
                    {% elif s.origen == "Santa cruz" %}
                    <td>{{s.envio_stacruz}}.4</td>
                    {% else %}
                    <td>{{s.id}}</td>
                    {% endif %}
                    <td>{{s.origen}}</td>
                    <td>{{s.destino}}</td>
                    <td>Q.{{s.total}}</td>
                    <td>{{s.fecha|date:"d-m-Y H:m:s"}}</td>
                    {% if s.estado == 1 %}
                    <td>Recibido/Terminado</td>
                    {% elif s.estado == 2 %}
                    <td>En Proceso/Enviado</td>
                    {% elif s.estado == 99 %}
                    <td>Anulado/Cancelado</td>
                    {% else %}
                    <td>Realizando Envio</td>
                    {% endif %}
                    <td>
                      {% if s.estado != 1 and s.estado != 99 %}
                      <a href="{% url 'DetalleEnvio' s.token %}">
                        <i style="color: yellowgreen;" class='bx bxs-edit' title="Modificar"></i>
                      </a>
                      <a href="{% url 'RecibeEnvio' s.id %}">
                        <i style="color: purple; font-size: 23px;" class='bx bxs-truck' title="Recibir Envio"></i>
                      </a>
                      {% endif %}
                      
                      <a href="javascript:popUp('{% url 'VerEnvio' s.token %}')">
                        <i style="color: rgb(197, 141, 18); font-size: 23px;" class="menu-icon tf-icons bx bx-bullseye"
                          title="Ver Detalles"></i>
                      </a>
                      
                      <a href="{% url 'PDFEnvio' s.id %}">
                        <i style="color: purple; font-size: 23px;" class='bx bxs-file-pdf' title="PDF"></i>
                      </a>

                      {% if s.estado != 99 %}
                      <a href="#" onclick="confirmarEliminar('{% url 'DeleteEnvio' s.id %}')">
                        <i style="color: red;" class='bx bxs-trash' title="Eliminar"></i>
                      </a>
                      {% endif %}
                    </td>
                  </tr>
                  {% empty %}
                  <caption>SIN ENVIOS</caption>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

    </div>

  </div>

</div>

<script type="text/javascript">
  function popUp(URL) {
    window.open(URL, 'Ver Envio', 'toolbar=0,scrollbars=0,location=0,statusbar=0,menubar=0,resizable=1,width=1050,height=500,left = 590,top = 150');
  }

  function confirmarEliminar(url) {
    Swal.fire({
      title: '¿Está seguro?',
      text: "El envío se anulará y el stock regresará a la tienda de origen.",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'Sí, anular envío',
      cancelButtonText: 'Cancelar'
    }).then((result) => {
      if (result.isConfirmed) {
        window.location.href = url;
      }
    })
  }
</script>

{% endblock %}
