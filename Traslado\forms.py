from django import forms
from .models import Traslado
from user.models import User


class TrasladoForm(forms.ModelForm):

    class Meta:
        model = Traslado
        fields = ['traslado', 'fecha']

        widgets = {
            'traslado': forms.TextInput(attrs={'class': 'form-control', 'require': True, 'autofocus': True, 'placeholder': 'Numero Consignacion'}),
            'fecha': forms.TextInput(attrs={'type':'date','class': 'form-control', 'require': True, 'autofocus': True, 'placeholder': 'Fecha'}),
        }


class UpdateTrasladoForm(forms.ModelForm):

    class Meta:
        model = Traslado
        fields = ['traslado', 'fecha']

        widgets = {
            'traslado': forms.TextInput(attrs={'class': 'form-control', 'require': True, 'autofocus': True, 'placeholder': 'Numero Consignacion','readonly':True}),
            'fecha': forms.TextInput(attrs={'type':'date','class': 'form-control', 'require': True, 'autofocus': True, 'placeholder': 'Fecha'}),
        }
