{% extends 'Base/base.html' %}
{% block title %}Detalle Venta{% endblock %}

{% block content %}

{% if messages %}
{% for message in messages %}
<script>
    Swal.fire({
        "title": "Informacion Sistema",
        "text": "{{message}}",
        "icon": "{{message.tags}}"
    })
</script>
{% endfor %}
{% endif %}


{% if venta.tipo == "FEL-Servicio" %}

<div class="content-wrapper">

    <div class="container-xxl flex-grow-1 container-p-y">

        <div class="row">
            <!-- Basic Layout -->
            <div class="col-md-12">
                <div class="card md-6">
                    <div class="card-header d-flex align-items-center justify-content-between">
                        <h5 class="mb-0">Detalles de Venta</h5>
                        <small class="text-muted float-end">Detalles de Venta</small>
                    </div>
                    <div class="card-body">

                        <label for="DatosCliente" class="form-label label-venta">
                            <h4>DATOS DE
                                CLIENTE</h4>
                        </label>
                        <form action="#" method="POST">{% csrf_token %}
                            <div class="row">
                                <div class="col-md-4">
                                    <label for="Nit" class="form-label">Nit:</label>
                                    <input type="text" name="nit" class="form-control" value="{{venta.nit}}">
                                </div>
                                <div class="col-md-4">
                                    <label for="Nombre" class="form-label">Nombre:</label>
                                    <input type="text" name="nombre" class="form-control" value="{{venta.nombre}}">
                                </div>
                                <div class="col-md-4">
                                    <label for="Direccion" class="form-label">Direccion:</label>
                                    <input type="text" name="direccion" class="form-control"
                                        value="{{venta.direccion}}">
                                </div>

                                <div class="col-md-12"><br>

                                    <div class="col-md" style="text-align: center;">
                                        <button class="btn btn-info" name="terminar">Finalizar</button>
                                        <button name="descartar" class="btn btn-danger">Descartar</button>
                                    </div><br>

                                </div>
                            </div>
                        </form>

                    </div>&nbsp;

                    <div class="row">

                        <div class="card-body">

                            <label for="DatosCliente" class="form-label label-venta">
                                <h4>DATOS DE
                                    SERVICIO</h4>
                            </label>
                            <form action="#" method="POST">{% csrf_token %}
                                <div class="row">
                                    <div class="col-md-4">
                                        <label for="" class="form-label">Cantidad:</label>
                                        <input type="text" name="cantidadservicio" class="form-control" placeholder="Cantidad">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="" class="form-label">Nombre Servicio:</label>
                                        <input type="text" name="nombreservicio" class="form-control" placeholder="Nombre">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="" class="form-label">Precio:</label>
                                        <input type="text" name="precioservicio" class="form-control"
                                            placeholder="Precio Servicio">
                                    </div>
    
                                    <div class="col-md-12"><br>
    
                                        <div class="col-md" style="text-align: center;">
                                            <button class="btn btn-success" name="agregarservicio">Agregar</button>
                                        </div><br>
    
                                    </div>
                                </div>
                            </form>
    
                        </div>&nbsp;

                    </div>
                    
                    <div class="row" align="center">

                        <div class="col-md-12">

                            <label for="">Productos Agregados</label>

                            <div style="height: 12rem; overflow-y: scroll;">
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th scope="col">Cod</th>
                                                <th scope="col">Prod</th>
                                                <th scope="col">Cant</th>
                                                <th scope="col">Precio</th>
                                                <th scope="col">SubTotal</th>
                                                <th scope="col">Descu</th>
                                                <th scope="col">Total</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for d in d %}
                                            <tr>
                                                <th scope="row">{{d.id_prod.id}}</th>
                                                <td>{{d.id_prod.nombre}}</td>
                                                <td>{{d.cantidad}}</td>
                                                <td>Q{{d.precio}}</td>
                                                <td>Q{{d.subtotal}}</td>
                                                <td>Q{{d.descuento}}</td>
                                                <td>Q.{{d.total}}</td>
                                                <form action="#" method="POST">{% csrf_token %}
                                                    <input type="hidden" name="corr" value="{{d.id}}">
                                                    <input type="hidden" name="id" value="{{d.id_inventario}}">
                                                    <td><button name="quitarservicio" class="btn btn-sm btn-danger"><i
                                                                style="color: white;" class="bx bx-x"></i></button>
                                                    </td>
                                                </form>
                                            </tr>
                                            {% empty %}
                                            <caption>SIN PRODUCTOS</caption>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                        </div>

                    </div>

                </div>




            </div>




        </div>
    </div>

</div>  



{% else %}
<div class="content-wrapper">

    <div class="container-xxl flex-grow-1 container-p-y">

        <div class="row">
            <!-- Basic Layout -->
            <div class="col-md-12">
                <div class="card md-6">
                    <div class="card-header d-flex align-items-center justify-content-between">
                        <h5 class="mb-0">Detalles de Venta</h5>
                        <small class="text-muted float-end">Detalles de Venta</small>
                    </div>
                    <div class="card-body">

                        <label for="DatosCliente" class="form-label label-venta">
                            <h4>DATOS DE
                                CLIENTE</h4>
                        </label>
                        <form action="#" method="POST">{% csrf_token %}
                            <div class="row">
                                <div class="col-md-4">
                                    <label for="Nit" class="form-label">Nit:</label>
                                    <input type="text" name="nit" class="form-control" value="{{venta.nit}}">
                                </div>
                                <div class="col-md-4">
                                    <label for="Nombre" class="form-label">Nombre:</label>
                                    <input type="text" name="nombre" class="form-control" value="{{venta.nombre}}">
                                </div>
                                <div class="col-md-4">
                                    <label for="Direccion" class="form-label">Direccion:</label>
                                    <input type="text" name="direccion" class="form-control"
                                        value="{{venta.direccion}}">
                                </div>

                                <div class="col-md-12"><br>

                                    <div class="col-md" style="text-align: center;">
                                        <button class="btn btn-info" name="terminar">Finalizar</button>
                                        <button name="descartar" class="btn btn-danger">Descartar</button>
                                    </div><br>

                                </div>
                            </div>
                        </form>

                    </div>&nbsp;

                    <div class="row" style="border-bottom: 2px solid black;" align="center">



                        <div class="col-md-5" style="border-right: 2px solid black;">
                            <label for="">Datos de Venta</label><br>
                            <div class="table-responsive">
                                <table class="table table-bordered table-sm" align="center">
                                    <thead>
                                        <tr align="center">
                                            <th>Factura</th>
                                            <th>Total</th>
                                            <th>Fecha</th>
                                            <th>Verificacion</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr align="center">
                                            <td>{{venta.factura}}</td>
                                            <td>Q.{{venta.total}}</td>
                                            <td>{{venta.fecha}}</td>
                                            {% if tok %}
                                            <td class="table-success" align="center"><i style="font-size: 30px;"
                                                    class='bx bxs-check-shield'></i></td>
                                            {% else %}
                                            <td class="table-danger" align="center"><i style="font-size: 30px;"
                                                    class='bx bxs-bug'></i></td>
                                            {% endif %}
                                        </tr>

                                    </tbody>
                                </table>

                            </div>

                        </div>

                        <div class="col-md-7">
                            <label for="">Busqueda de Productos</label><br>
                            <label for="Buscar">Buscar</label>
                            <form action="#" method="POST">{% csrf_token %}
                                <input type="text" name="buscar" class="form-control" autofocus="buscar"
                                    placeholder="Agregar/Buscar">
                            </form><br>
                            {% if b %}
                            <div style="overflow-y: scroll; max-height: 11rem;">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm">
                                        <thead>
                                            <tr>
                                                <th scope="col">Producto</th>
                                                <th scope="col">Tienda</th>
                                                <th scope="col">Stock</th>
                                                <th scope="col">Precio</th>
                                                <th scope="col">Cant</th>
                                                <th scope="col">Descu</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for b in b %}
                                            <tr>
                                                <td>{{b.nombre}}</td>
                                                <td>{{b.tienda}}</td>
                                                <td>{{b.stock}}</td>
                                                <td>Q{{b.precio_venta}}</td>
                                                {% if b.stock == 0 %}
                                                
                                                <td></td>
                                                {% else %}
                                                <form action="#" method="POST">{% csrf_token %}
                                                    <input type="hidden" value="{{b.id}}" name="id">
                                                    <td><input type="text" name="cantidad" size="5" placeholder="Cantidad"></td>
                                                    {% if venta.tipo == "PROFORMA" or venta.tipo == "NOTA CREDITO" %}
                                                    <td><input type="text" name="descuento" size="5" placeholder="Descuento" ></td>
                                                    {% else %}
                                                    <input type="hidden" name="descuento" size="5" placeholder="Descuento" >
                                                    {% endif %}
                                                    <td><button name="agregar" class="btn btn-sm btn-success"><i
                                                                style="color: white;"
                                                                class="bx bx-plus-circle"></i></button>
                                                    </td>
                                                </form>
                                                {% endif %}
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            {% else %}
                            <caption>SIN RESULTADOS</caption>
                            {% endif %}




                        </div>
                    </div>



                    <div class="row" align="center">

                        <div class="col-md-12">

                            <label for="">Productos Agregados</label>

                            <div style="height: 12rem; overflow-y: scroll;">
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th scope="col">Cod</th>
                                                <th scope="col">Prod</th>
                                                <th scope="col">Cant</th>
                                                <th scope="col">Precio</th>
                                                <th scope="col">SubTotal</th>
                                                <th scope="col">Descu</th>
                                                <th scope="col">Total</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for d in d %}
                                            <tr>
                                                <th scope="row">{{d.id_prod.id}}</th>
                                                <td>{{d.id_prod.nombre}}</td>
                                                <td>{{d.cantidad}}</td>
                                                <td>Q{{d.precio}}</td>
                                                <td>Q{{d.subtotal}}</td>
                                                <td>Q{{d.descuento}}</td>
                                                <td>Q.{{d.total}}</td>
                                                <form action="#" method="POST">{% csrf_token %}
                                                    <input type="hidden" name="corr" value="{{d.id}}">
                                                    <input type="hidden" name="id" value="{{d.id_inventario}}">
                                                    <td><button name="quitar" class="btn btn-sm btn-danger"><i
                                                                style="color: white;" class="bx bx-x"></i></button>
                                                    </td>
                                                </form>
                                            </tr>
                                            {% empty %}
                                            <caption>SIN PRODUCTOS</caption>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                        </div>

                    </div>

                </div>




            </div>




        </div>
    </div>

</div>  

{% endif %}





    {% endblock %}