{% extends 'Base/basever.html' %}
{% block title %}Movimientos Producto en Compras{% endblock %}

{% block content %}

<div class="content-wrapper">

    <div class="container-xxl flex-grow-1 container-p-y">

        <div class="row">
            <!-- Basic Layout -->
            <div class="col-md-12">
                <div class="card md-6">
                    <div class="card-header d-flex align-items-center justify-content-between">
                        <h5 class="mb-0">Movimientos Historial del Producto {{prod.nombre}}</h5>
                        <small class="text-muted float-end">Movimientos Historial del Producto {{prod.nombre}}</small>
                    </div>
                    <div class="card-body">

                    </div>&nbsp;

                    <div class="row" align="center">

                        <div class="col-md-12">

                            <label for="">Historial del Producto</label>

                            <div style="height: 14rem; overflow-y: scroll;">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm" align="center">
                                        <thead>
                                            <tr align="center">
                                                <th>Correlativo</th>
                                                <th>Tipo</th>
                                                <th>Habia</th>
                                                <th>Salio</th>
                                                <th>Ingreso</th>
                                                <th>Stock</th>
                                                <th>Tienda</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for v in datos %}
                                            <tr align="center">
                                            {% if v.tienda == "Estanzuela" %}
                                            <td>{{v.factura}}.1</td>
                                            {% elif v.tienda == "Teculutan" %}
                                            <td>{{v.factura}}.2</td>
                                            {% else %}
                                            <td>{{v.factura}}.3</td>
                                            {% endif %}
                                                <td>{{v.tipo}}</td>
                                                <td>{{v.habia}}</td>
                                                <td>{{v.salio}}</td>
                                                <td>{{v.ingreso}}</td>
                                                <td>{{v.hay}}</td>
                                                <td>{{v.tienda}}</td>
                                            </tr>
                                            {% empty %}
                                            <caption>SIN MOVIMIENTOS</caption>
                                            {% endfor %}
                                        </tbody>
                                    </table>
    
                                </div>
                            </div>

                        </div>

                    </div>

                </div>




            </div>




        </div>
    </div>





    {% endblock %}