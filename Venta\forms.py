from django import forms
from .models import Venta

TIPO = (('FEL','FEL'),('FEL-Servicio','FEL-Servicio'),('PROFORMA','PROFORMA'),('PROFORMA-Servicio','PROFORMA-Servicio'),('NOTA CREDITO','NOTA CREDITO'),('COTIZACION','COTIZACION'))

class VentaForm(forms.ModelForm):
   
    class Meta:
        model = Venta
        fields = ['nit','nombre','direccion','tipo']

        widgets = { 
            'nit': forms.TextInput(attrs={'class': 'form-control','require':True,'autofocus': True,'placeholder':'Nit'}),
            'nombre': forms.TextInput(attrs={'class': 'form-control','autofocus': True,'require':True,'placeholder':'Nombre Cliente'}),
            'direccion': forms.TextInput(attrs={'class': 'form-control','autofocus': True,'require':True,'placeholder':'Direccion Cliente'}),
            'tipo':forms.Select(attrs={'class': 'selectpicker form-control','data-style':'btn-outline-info','placeholder':'Rol','require':True},choices=TIPO), 
        }


class UpdateVentaForm(forms.ModelForm):
   
    class Meta:
        model = Venta
        fields = ['nit','nombre','direccion']

        widgets = { 
            'nit': forms.TextInput(attrs={'class': 'form-control','require':True,'autofocus': True,'placeholder':'Nit'}),
            'nombre': forms.TextInput(attrs={'class': 'form-control','autofocus': True,'require':True,'placeholder':'Nombre Cliente'}),
            'direccion': forms.TextInput(attrs={'class': 'form-control','autofocus': True,'require':True,'placeholder':'Direccion Cliente'}),
            'tipo':forms.Select(attrs={'class': 'selectpicker form-control','data-style':'btn-outline-info','placeholder':'Rol','require':True},choices=TIPO), 
        }



