(function(e, a) { for(var i in a) e[i] = a[i]; }(window, /******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true;
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function(exports, name, getter) {
/******/ 		if(!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, { enumerable: true, get: getter });
/******/ 		}
/******/ 	};
/******/
/******/ 	// define __esModule on exports
/******/ 	__webpack_require__.r = function(exports) {
/******/ 		if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 			Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 		}
/******/ 		Object.defineProperty(exports, '__esModule', { value: true });
/******/ 	};
/******/
/******/ 	// create a fake namespace object
/******/ 	// mode & 1: value is a module id, require it
/******/ 	// mode & 2: merge all properties of value into the ns
/******/ 	// mode & 4: return value when already ns object
/******/ 	// mode & 8|1: behave like require
/******/ 	__webpack_require__.t = function(value, mode) {
/******/ 		if(mode & 1) value = __webpack_require__(value);
/******/ 		if(mode & 8) return value;
/******/ 		if((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;
/******/ 		var ns = Object.create(null);
/******/ 		__webpack_require__.r(ns);
/******/ 		Object.defineProperty(ns, 'default', { enumerable: true, value: value });
/******/ 		if(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));
/******/ 		return ns;
/******/ 	};
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function(module) {
/******/ 		var getter = module && module.__esModule ?
/******/ 			function getDefault() { return module['default']; } :
/******/ 			function getModuleExports() { return module; };
/******/ 		__webpack_require__.d(getter, 'a', getter);
/******/ 		return getter;
/******/ 	};
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "";
/******/
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = "./js/menu.js");
/******/ })
/************************************************************************/
/******/ ({

/***/ "./js/menu.js":
/*!********************!*\
  !*** ./js/menu.js ***!
  \********************/
/*! exports provided: Menu */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"Menu\", function() { return Menu; });\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\nvar TRANSITION_EVENTS = ['transitionend', 'webkitTransitionEnd', 'oTransitionEnd']; // const TRANSITION_PROPERTIES = ['transition', 'MozTransition', 'webkitTransition', 'WebkitTransition', 'OTransition']\n\nvar Menu = /*#__PURE__*/function () {\n  function Menu(el) {\n    var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n    var _PS = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n\n    _classCallCheck(this, Menu);\n\n    this._el = el;\n    this._animate = config.animate !== false;\n    this._accordion = config.accordion !== false;\n    this._closeChildren = Boolean(config.closeChildren);\n\n    this._onOpen = config.onOpen || function () {};\n\n    this._onOpened = config.onOpened || function () {};\n\n    this._onClose = config.onClose || function () {};\n\n    this._onClosed = config.onClosed || function () {};\n\n    this._psScroll = null;\n    this._topParent = null;\n    this._menuBgClass = null;\n    el.classList.add('menu');\n    el.classList[this._animate ? 'remove' : 'add']('menu-no-animation'); // check\n\n    el.classList.add('menu-vertical');\n    var PerfectScrollbarLib = _PS || window.PerfectScrollbar;\n\n    if (PerfectScrollbarLib) {\n      this._scrollbar = new PerfectScrollbarLib(el.querySelector('.menu-inner'), {\n        suppressScrollX: true,\n        wheelPropagation: !Menu._hasClass('layout-menu-fixed layout-menu-fixed-offcanvas')\n      });\n      window.Helpers.menuPsScroll = this._scrollbar;\n    } else {\n      el.querySelector('.menu-inner').classList.add('overflow-auto');\n    } // Add data attribute for bg color class of menu\n\n\n    var menuClassList = el.classList;\n\n    for (var i = 0; i < menuClassList.length; i++) {\n      if (menuClassList[i].startsWith('bg-')) {\n        this._menuBgClass = menuClassList[i];\n      }\n    }\n\n    el.setAttribute('data-bg-class', this._menuBgClass);\n\n    this._bindEvents(); // Link menu instance to element\n\n\n    el.menuInstance = this;\n  }\n\n  _createClass(Menu, [{\n    key: \"_bindEvents\",\n    value: function _bindEvents() {\n      var _this = this;\n\n      // Click Event\n      this._evntElClick = function (e) {\n        // Find top parent element\n        if (e.target.closest('ul') && e.target.closest('ul').classList.contains('menu-inner')) {\n          var menuItem = Menu._findParent(e.target, 'menu-item', false); // eslint-disable-next-line prefer-destructuring\n\n\n          if (menuItem) _this._topParent = menuItem.childNodes[0];\n        }\n\n        var toggleLink = e.target.classList.contains('menu-toggle') ? e.target : Menu._findParent(e.target, 'menu-toggle', false);\n\n        if (toggleLink) {\n          e.preventDefault();\n\n          if (toggleLink.getAttribute('data-hover') !== 'true') {\n            _this.toggle(toggleLink);\n          }\n        }\n      };\n\n      if (window.Helpers.isMobileDevice) this._el.addEventListener('click', this._evntElClick);\n\n      this._evntWindowResize = function () {\n        _this.update();\n\n        if (_this._lastWidth !== window.innerWidth) {\n          _this._lastWidth = window.innerWidth;\n\n          _this.update();\n        }\n\n        var horizontalMenuTemplate = document.querySelector(\"[data-template^='horizontal-menu']\");\n        if (!_this._horizontal && !horizontalMenuTemplate) _this.manageScroll();\n      };\n\n      window.addEventListener('resize', this._evntWindowResize);\n    }\n  }, {\n    key: \"_unbindEvents\",\n    value: function _unbindEvents() {\n      if (this._evntElClick) {\n        this._el.removeEventListener('click', this._evntElClick);\n\n        this._evntElClick = null;\n      }\n\n      if (this._evntElMouseOver) {\n        this._el.removeEventListener('mouseover', this._evntElMouseOver);\n\n        this._evntElMouseOver = null;\n      }\n\n      if (this._evntElMouseOut) {\n        this._el.removeEventListener('mouseout', this._evntElMouseOut);\n\n        this._evntElMouseOut = null;\n      }\n\n      if (this._evntWindowResize) {\n        window.removeEventListener('resize', this._evntWindowResize);\n        this._evntWindowResize = null;\n      }\n\n      if (this._evntBodyClick) {\n        document.body.removeEventListener('click', this._evntBodyClick);\n        this._evntBodyClick = null;\n      }\n\n      if (this._evntInnerMousemove) {\n        this._inner.removeEventListener('mousemove', this._evntInnerMousemove);\n\n        this._evntInnerMousemove = null;\n      }\n\n      if (this._evntInnerMouseleave) {\n        this._inner.removeEventListener('mouseleave', this._evntInnerMouseleave);\n\n        this._evntInnerMouseleave = null;\n      }\n    }\n  }, {\n    key: \"open\",\n    value: function open(el) {\n      var _this2 = this;\n\n      var closeChildren = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this._closeChildren;\n\n      var item = this._findUnopenedParent(Menu._getItem(el, true), closeChildren);\n\n      if (!item) return;\n\n      var toggleLink = Menu._getLink(item, true);\n\n      Menu._promisify(this._onOpen, this, item, toggleLink, Menu._findMenu(item)).then(function () {\n        if (!_this2._horizontal || !Menu._isRoot(item)) {\n          if (_this2._animate && !_this2._horizontal) {\n            window.requestAnimationFrame(function () {\n              return _this2._toggleAnimation(true, item, false);\n            });\n            if (_this2._accordion) _this2._closeOther(item, closeChildren);\n          } else if (_this2._animate) {\n            // eslint-disable-next-line no-unused-expressions\n            _this2._onOpened && _this2._onOpened(_this2, item, toggleLink, Menu._findMenu(item));\n          } else {\n            item.classList.add('open'); // eslint-disable-next-line no-unused-expressions\n\n            _this2._onOpened && _this2._onOpened(_this2, item, toggleLink, Menu._findMenu(item));\n            if (_this2._accordion) _this2._closeOther(item, closeChildren);\n          }\n        } else {\n          // eslint-disable-next-line no-unused-expressions\n          _this2._onOpened && _this2._onOpened(_this2, item, toggleLink, Menu._findMenu(item));\n        }\n      }).catch(function () {});\n    }\n  }, {\n    key: \"close\",\n    value: function close(el) {\n      var _this3 = this;\n\n      var closeChildren = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this._closeChildren;\n\n      var _autoClose = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n\n      var item = Menu._getItem(el, true);\n\n      var toggleLink = Menu._getLink(el, true);\n\n      if (!item.classList.contains('open') || item.classList.contains('disabled')) return;\n\n      Menu._promisify(this._onClose, this, item, toggleLink, Menu._findMenu(item), _autoClose).then(function () {\n        if (!_this3._horizontal || !Menu._isRoot(item)) {\n          if (_this3._animate && !_this3._horizontal) {\n            window.requestAnimationFrame(function () {\n              return _this3._toggleAnimation(false, item, closeChildren);\n            });\n          } else {\n            item.classList.remove('open');\n\n            if (closeChildren) {\n              var opened = item.querySelectorAll('.menu-item.open');\n\n              for (var i = 0, l = opened.length; i < l; i++) {\n                opened[i].classList.remove('open');\n              }\n            } // eslint-disable-next-line no-unused-expressions\n\n\n            _this3._onClosed && _this3._onClosed(_this3, item, toggleLink, Menu._findMenu(item));\n          }\n        } else {\n          // eslint-disable-next-line no-unused-expressions\n          _this3._onClosed && _this3._onClosed(_this3, item, toggleLink, Menu._findMenu(item));\n        }\n      }).catch(function () {});\n    }\n  }, {\n    key: \"_closeOther\",\n    value: function _closeOther(item, closeChildren) {\n      var opened = Menu._findChild(item.parentNode, ['menu-item', 'open']);\n\n      for (var i = 0, l = opened.length; i < l; i++) {\n        if (opened[i] !== item) this.close(opened[i], closeChildren);\n      }\n    }\n  }, {\n    key: \"toggle\",\n    value: function toggle(el) {\n      var closeChildren = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this._closeChildren;\n\n      var item = Menu._getItem(el, true); // const toggleLink = Menu._getLink(el, true)\n\n\n      if (item.classList.contains('open')) this.close(item, closeChildren);else this.open(item, closeChildren);\n    }\n  }, {\n    key: \"_findUnopenedParent\",\n    value: function _findUnopenedParent(item, closeChildren) {\n      var tree = [];\n      var parentItem = null;\n\n      while (item) {\n        if (item.classList.contains('disabled')) {\n          parentItem = null;\n          tree = [];\n        } else {\n          if (!item.classList.contains('open')) parentItem = item;\n          tree.push(item);\n        }\n\n        item = Menu._findParent(item, 'menu-item', false);\n      }\n\n      if (!parentItem) return null;\n      if (tree.length === 1) return parentItem;\n      tree = tree.slice(0, tree.indexOf(parentItem));\n\n      for (var i = 0, l = tree.length; i < l; i++) {\n        tree[i].classList.add('open');\n\n        if (this._accordion) {\n          var openedItems = Menu._findChild(tree[i].parentNode, ['menu-item', 'open']);\n\n          for (var j = 0, k = openedItems.length; j < k; j++) {\n            if (openedItems[j] !== tree[i]) {\n              openedItems[j].classList.remove('open');\n\n              if (closeChildren) {\n                var openedChildren = openedItems[j].querySelectorAll('.menu-item.open');\n\n                for (var x = 0, z = openedChildren.length; x < z; x++) {\n                  openedChildren[x].classList.remove('open');\n                }\n              }\n            }\n          }\n        }\n      }\n\n      return parentItem;\n    }\n  }, {\n    key: \"_toggleAnimation\",\n    value: function _toggleAnimation(open, item, closeChildren) {\n      var _this4 = this;\n\n      var toggleLink = Menu._getLink(item, true);\n\n      var menu = Menu._findMenu(item);\n\n      Menu._unbindAnimationEndEvent(item);\n\n      var linkHeight = Math.round(toggleLink.getBoundingClientRect().height);\n      item.style.overflow = 'hidden';\n\n      var clearItemStyle = function clearItemStyle() {\n        item.classList.remove('menu-item-animating');\n        item.classList.remove('menu-item-closing');\n        item.style.overflow = null;\n        item.style.height = null;\n\n        _this4.update();\n      };\n\n      if (open) {\n        item.style.height = \"\".concat(linkHeight, \"px\");\n        item.classList.add('menu-item-animating');\n        item.classList.add('open');\n\n        Menu._bindAnimationEndEvent(item, function () {\n          clearItemStyle();\n\n          _this4._onOpened(_this4, item, toggleLink, menu);\n        });\n\n        setTimeout(function () {\n          item.style.height = \"\".concat(linkHeight + Math.round(menu.getBoundingClientRect().height), \"px\");\n        }, 50);\n      } else {\n        item.style.height = \"\".concat(linkHeight + Math.round(menu.getBoundingClientRect().height), \"px\");\n        item.classList.add('menu-item-animating');\n        item.classList.add('menu-item-closing');\n\n        Menu._bindAnimationEndEvent(item, function () {\n          item.classList.remove('open');\n          clearItemStyle();\n\n          if (closeChildren) {\n            var opened = item.querySelectorAll('.menu-item.open');\n\n            for (var i = 0, l = opened.length; i < l; i++) {\n              opened[i].classList.remove('open');\n            }\n          }\n\n          _this4._onClosed(_this4, item, toggleLink, menu);\n        });\n\n        setTimeout(function () {\n          item.style.height = \"\".concat(linkHeight, \"px\");\n        }, 50);\n      }\n    }\n  }, {\n    key: \"_getItemOffset\",\n    value: function _getItemOffset(item) {\n      var curItem = this._inner.childNodes[0];\n      var left = 0;\n\n      while (curItem !== item) {\n        if (curItem.tagName) {\n          left += Math.round(curItem.getBoundingClientRect().width);\n        }\n\n        curItem = curItem.nextSibling;\n      }\n\n      return left;\n    }\n  }, {\n    key: \"_innerWidth\",\n    get: function get() {\n      var items = this._inner.childNodes;\n      var width = 0;\n\n      for (var i = 0, l = items.length; i < l; i++) {\n        if (items[i].tagName) {\n          width += Math.round(items[i].getBoundingClientRect().width);\n        }\n      }\n\n      return width;\n    }\n  }, {\n    key: \"_innerPosition\",\n    get: function get() {\n      return parseInt(this._inner.style[this._rtl ? 'marginRight' : 'marginLeft'] || '0px', 10);\n    },\n    set: function set(value) {\n      this._inner.style[this._rtl ? 'marginRight' : 'marginLeft'] = \"\".concat(value, \"px\");\n      return value;\n    }\n  }, {\n    key: \"closeAll\",\n    value: function closeAll() {\n      var closeChildren = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this._closeChildren;\n\n      var opened = this._el.querySelectorAll('.menu-inner > .menu-item.open');\n\n      for (var i = 0, l = opened.length; i < l; i++) {\n        this.close(opened[i], closeChildren);\n      }\n    }\n  }, {\n    key: \"update\",\n    value: function update() {\n      if (this._scrollbar) {\n        this._scrollbar.update();\n      }\n    }\n  }, {\n    key: \"manageScroll\",\n    value: function manageScroll() {\n      var _window = window,\n          PerfectScrollbar = _window.PerfectScrollbar;\n      var menuInner = document.querySelector('.menu-inner');\n\n      if (window.innerWidth < window.Helpers.LAYOUT_BREAKPOINT) {\n        if (this._scrollbar !== null) {\n          // window.Helpers.menuPsScroll.destroy()\n          this._scrollbar.destroy();\n\n          this._scrollbar = null;\n        }\n\n        menuInner.classList.add('overflow-auto');\n      } else {\n        if (this._scrollbar === null) {\n          var menuScroll = new PerfectScrollbar(document.querySelector('.menu-inner'), {\n            suppressScrollX: true,\n            wheelPropagation: false\n          });\n          this._scrollbar = menuScroll;\n        }\n\n        menuInner.classList.remove('overflow-auto');\n      }\n    }\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      if (!this._el) return;\n\n      this._unbindEvents();\n\n      var items = this._el.querySelectorAll('.menu-item');\n\n      for (var i = 0, l = items.length; i < l; i++) {\n        Menu._unbindAnimationEndEvent(items[i]);\n\n        items[i].classList.remove('menu-item-animating');\n        items[i].classList.remove('open');\n        items[i].style.overflow = null;\n        items[i].style.height = null;\n      }\n\n      var menus = this._el.querySelectorAll('.menu-menu');\n\n      for (var i2 = 0, l2 = menus.length; i2 < l2; i2++) {\n        menus[i2].style.marginRight = null;\n        menus[i2].style.marginLeft = null;\n      }\n\n      this._el.classList.remove('menu-no-animation');\n\n      if (this._wrapper) {\n        this._prevBtn.parentNode.removeChild(this._prevBtn);\n\n        this._nextBtn.parentNode.removeChild(this._nextBtn);\n\n        this._wrapper.parentNode.insertBefore(this._inner, this._wrapper);\n\n        this._wrapper.parentNode.removeChild(this._wrapper);\n\n        this._inner.style.marginLeft = null;\n        this._inner.style.marginRight = null;\n      }\n\n      this._el.menuInstance = null;\n      delete this._el.menuInstance;\n      this._el = null;\n      this._animate = null;\n      this._accordion = null;\n      this._closeChildren = null;\n      this._onOpen = null;\n      this._onOpened = null;\n      this._onClose = null;\n      this._onClosed = null;\n\n      if (this._scrollbar) {\n        this._scrollbar.destroy();\n\n        this._scrollbar = null;\n      }\n\n      this._inner = null;\n      this._prevBtn = null;\n      this._wrapper = null;\n      this._nextBtn = null;\n    }\n  }], [{\n    key: \"childOf\",\n    value: function childOf(\n    /* child node */\n    c,\n    /* parent node */\n    p) {\n      // returns boolean\n      if (c.parentNode) {\n        while ((c = c.parentNode) && c !== p) {\n          ;\n        }\n\n        return !!c;\n      }\n\n      return false;\n    }\n  }, {\n    key: \"_isRoot\",\n    value: function _isRoot(item) {\n      return !Menu._findParent(item, 'menu-item', false);\n    }\n  }, {\n    key: \"_findParent\",\n    value: function _findParent(el, cls) {\n      var throwError = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n      if (el.tagName.toUpperCase() === 'BODY') return null;\n      el = el.parentNode;\n\n      while (el.tagName.toUpperCase() !== 'BODY' && !el.classList.contains(cls)) {\n        el = el.parentNode;\n      }\n\n      el = el.tagName.toUpperCase() !== 'BODY' ? el : null;\n      if (!el && throwError) throw new Error(\"Cannot find `.\".concat(cls, \"` parent element\"));\n      return el;\n    }\n  }, {\n    key: \"_findChild\",\n    value: function _findChild(el, cls) {\n      var items = el.childNodes;\n      var found = [];\n\n      for (var i = 0, l = items.length; i < l; i++) {\n        if (items[i].classList) {\n          var passed = 0;\n\n          for (var j = 0; j < cls.length; j++) {\n            if (items[i].classList.contains(cls[j])) passed += 1;\n          }\n\n          if (cls.length === passed) found.push(items[i]);\n        }\n      }\n\n      return found;\n    }\n  }, {\n    key: \"_findMenu\",\n    value: function _findMenu(item) {\n      var curEl = item.childNodes[0];\n      var menu = null;\n\n      while (curEl && !menu) {\n        if (curEl.classList && curEl.classList.contains('menu-sub')) menu = curEl;\n        curEl = curEl.nextSibling;\n      }\n\n      if (!menu) throw new Error('Cannot find `.menu-sub` element for the current `.menu-toggle`');\n      return menu;\n    } // Has class\n\n  }, {\n    key: \"_hasClass\",\n    value: function _hasClass(cls) {\n      var el = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : window.Helpers.ROOT_EL;\n      var result = false;\n      cls.split(' ').forEach(function (c) {\n        if (el.classList.contains(c)) result = true;\n      });\n      return result;\n    }\n  }, {\n    key: \"_getItem\",\n    value: function _getItem(el, toggle) {\n      var item = null;\n      var selector = toggle ? 'menu-toggle' : 'menu-link';\n\n      if (el.classList.contains('menu-item')) {\n        if (Menu._findChild(el, [selector]).length) item = el;\n      } else if (el.classList.contains(selector)) {\n        item = el.parentNode.classList.contains('menu-item') ? el.parentNode : null;\n      }\n\n      if (!item) {\n        throw new Error(\"\".concat(toggle ? 'Toggable ' : '', \"`.menu-item` element not found.\"));\n      }\n\n      return item;\n    }\n  }, {\n    key: \"_getLink\",\n    value: function _getLink(el, toggle) {\n      var found = [];\n      var selector = toggle ? 'menu-toggle' : 'menu-link';\n      if (el.classList.contains(selector)) found = [el];else if (el.classList.contains('menu-item')) found = Menu._findChild(el, [selector]);\n      if (!found.length) throw new Error(\"`\".concat(selector, \"` element not found.\"));\n      return found[0];\n    }\n  }, {\n    key: \"_bindAnimationEndEvent\",\n    value: function _bindAnimationEndEvent(el, handler) {\n      var cb = function cb(e) {\n        if (e.target !== el) return;\n\n        Menu._unbindAnimationEndEvent(el);\n\n        handler(e);\n      };\n\n      var duration = window.getComputedStyle(el).transitionDuration;\n      duration = parseFloat(duration) * (duration.indexOf('ms') !== -1 ? 1 : 1000);\n      el._menuAnimationEndEventCb = cb;\n      TRANSITION_EVENTS.forEach(function (ev) {\n        return el.addEventListener(ev, el._menuAnimationEndEventCb, false);\n      });\n      el._menuAnimationEndEventTimeout = setTimeout(function () {\n        cb({\n          target: el\n        });\n      }, duration + 50);\n    }\n  }, {\n    key: \"_promisify\",\n    value: function _promisify(fn) {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n\n      var result = fn.apply(void 0, args);\n\n      if (result instanceof Promise) {\n        return result;\n      }\n\n      if (result === false) {\n        return Promise.reject();\n      }\n\n      return Promise.resolve();\n    }\n  }, {\n    key: \"_unbindAnimationEndEvent\",\n    value: function _unbindAnimationEndEvent(el) {\n      var cb = el._menuAnimationEndEventCb;\n\n      if (el._menuAnimationEndEventTimeout) {\n        clearTimeout(el._menuAnimationEndEventTimeout);\n        el._menuAnimationEndEventTimeout = null;\n      }\n\n      if (!cb) return;\n      TRANSITION_EVENTS.forEach(function (ev) {\n        return el.removeEventListener(ev, cb, false);\n      });\n      el._menuAnimationEndEventCb = null;\n    }\n  }, {\n    key: \"setDisabled\",\n    value: function setDisabled(el, disabled) {\n      Menu._getItem(el, false).classList[disabled ? 'add' : 'remove']('disabled');\n    }\n  }, {\n    key: \"isActive\",\n    value: function isActive(el) {\n      return Menu._getItem(el, false).classList.contains('active');\n    }\n  }, {\n    key: \"isOpened\",\n    value: function isOpened(el) {\n      return Menu._getItem(el, false).classList.contains('open');\n    }\n  }, {\n    key: \"isDisabled\",\n    value: function isDisabled(el) {\n      return Menu._getItem(el, false).classList.contains('disabled');\n    }\n  }]);\n\n  return Menu;\n}();\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./js/menu.js\n");

/***/ })

/******/ })));