# Generated by Django 4.2.4 on 2024-03-04 13:08

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Producto', '0007_detallefactura_estado_productofactura_estado'),
    ]

    operations = [
        migrations.CreateModel(
            name='Bitacora',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('id_prod', models.BigIntegerField(blank=True, default=0, null=True)),
                ('prod', models.CharField(blank=True, max_length=1000, null=True)),
                ('tipo', models.CharField(blank=True, max_length=550, null=True)),
                ('doc', models.BigIntegerField(blank=True, default=0, null=True)),
                ('habia', models.BigIntegerField(blank=True, default=0, null=True)),
                ('ingreso', models.BigIntegerField(blank=True, default=0, null=True)),
                ('salio', models.BigIntegerField(blank=True, default=0, null=True)),
                ('hay', models.BigIntegerField(blank=True, default=0, null=True)),
                ('tienda', models.CharField(blank=True, max_length=550, null=True)),
                ('fecha', models.CharField(blank=True, max_length=255, null=True)),
                ('usuario', models.CharField(blank=True, max_length=200, null=True)),
            ],
            options={
                'ordering': ['id'],
            },
        ),
    ]
