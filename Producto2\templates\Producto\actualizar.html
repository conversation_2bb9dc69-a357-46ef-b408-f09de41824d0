{% extends 'Base/base.html' %}
{% block title %}Actualizacion{% endblock %}

{% block content %}

{% if messages %}
{% for message in messages %}
<script>
    Swal.fire({
        "title": "Informacion Sistema",
        "text": "{{message}}",
        "icon": "{{message.tags}}"
    })
</script>
{% endfor %}
{% endif %}

<div class="content-wrapper">

    {{form.errors}}

    {% if user.rol == "admin" %}    
    <div class="container-xxl flex-grow-1 container-p-y">

        <div class="row">
            <!-- Basic Layout -->
            <div class="col-md-10">
                <div class="card md-6">
                    <div class="card-header d-flex align-items-center justify-content-between">
                        <h5 class="mb-0">Formulario de Modificacion</h5>
                        <small class="text-muted float-end">Actualizacion</small>
                    </div>
                    <div class="card-body">
                        <form action="#" method="POST" enctype="multipart/form-data">{% csrf_token %}

                            <div class="row">
                                <div class="col-md-6">
                                    <label class="col-md-4 col-form-label" for="basic-default-name">Nombre</label>
                                    {{form.nombre}}
                                </div>

                                <div class="col-md-6">
                                    <label class="col-md-4 col-form-label" for="basic-default-company">Categoria</label>
                                    {{form.id_cate}}
                                </div>

                            </div><br>

                            <div class="row">
                                <div class="col-md-12">
                                    <label class="col-md-4 col-form-label"
                                        for="basic-default-company">Descripcion</label>
                                    {{form.descripcion}}
                                </div>
                            </div><br>


                            <div class="row">
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-name">Stock</label>
                                    {{form.stock}}
                                </div>
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-company">Precio
                                        Compra</label>
                                    {{form.precio_compra}}
                                </div>
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-company">Precio
                                        Venta</label>
                                    {{form.precio_venta}}
                                </div>

                            </div><br>

                            <div class="row">
                                {% if user.rol == "admin" %}
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-name">Tienda</label>
                                    {{form.tienda}}
                                </div>
                                {% else %}
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-name">Tienda</label>
                                    <input type="text" name="tienda" class="form-control" value="{{user.tienda}}"
                                        readonly>
                                </div>
                                {% endif %}
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-company">Fecha</label>
                                    <input type="text" class="form-control" readonly value="{% now 'd-m-Y' %}" />
                                </div>
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-company">Usuario</label>
                                    <input type="text" class="form-control" readonly value="{{user.username}}" />
                                </div>

                            </div><br>

                            <div class="row justify-content-end">
                                <div class="col-md-4">
                                    <button type="submit" class="btn btn-primary">Guardar</button>
                                    <button type="reset" class="btn btn-danger">Cancelar</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

        </div>

    </div>
    {% else %}

    <div class="container-xxl flex-grow-1 container-p-y">

        <div class="row">
            <!-- Basic Layout -->
            <div class="col-md-10">
                <div class="card md-6">
                    <div class="card-header d-flex align-items-center justify-content-between">
                        <h5 class="mb-0">Formulario de Modificacion</h5>
                        <small class="text-muted float-end">Actualizacion</small>
                    </div>
                    <div class="card-body">
                        <form action="#" method="POST" enctype="multipart/form-data">{% csrf_token %}

                            <div class="row">
                                <div class="col-md-6">
                                    <label class="col-md-4 col-form-label" for="basic-default-name">Nombre</label>
                                    {{form.nombre}}
                                </div>

                                <div class="col-md-6">
                                    <label class="col-md-4 col-form-label" for="basic-default-company">Categoria</label>
                                    {{form.id_cate}}
                                </div>

                            </div><br>

                            <div class="row">
                                <div class="col-md-12">
                                    <label class="col-md-4 col-form-label"
                                        for="basic-default-company">Descripcion</label>
                                    {{form.descripcion}}
                                </div>
                            </div><br>


                            <div class="row">
                                  <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-company">
                                        Stock</label>
                                    {{form.stock}}
                                </div>
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-company">Precio
                                        Compra</label>
                                    {{form.precio_compra}}
                                </div>
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-company">Precio
                                        Venta</label>
                                    {{form.precio_venta}}
                                </div>

                            </div><br>

                            <div class="row">
                                {% if user.rol == "admin" %}
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-name">Tienda</label>
                                    {{form.tienda}}
                                </div>
                                {% else %}
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-name">Tienda</label>
                                    <input type="text" name="tienda" class="form-control" value="{{user.tienda}}"
                                        readonly>
                                </div>
                                {% endif %}
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-company">Fecha</label>
                                    <input type="text" class="form-control" readonly value="{% now 'd-m-Y' %}" />
                                </div>
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-company">Usuario</label>
                                    <input type="text" class="form-control" readonly value="{{user.username}}" />
                                </div>

                            </div><br>

                            <div class="row justify-content-end">
                                <div class="col-md-4">
                                    <button type="submit" class="btn btn-primary">Guardar</button>
                                    <button type="reset" class="btn btn-danger">Cancelar</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

        </div>

    </div>

    {% endif %}

</div>

{% endblock %}