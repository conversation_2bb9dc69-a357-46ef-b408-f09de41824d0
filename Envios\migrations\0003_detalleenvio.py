# Generated by Django 4.1.7 on 2023-11-20 14:47

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('Producto', '0002_alter_producto_options'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('Envios', '0002_remove_envio_cantidad_remove_envio_precio_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='DetalleEnvio',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cantidad', models.IntegerField()),
                ('precio', models.DecimalField(decimal_places=2, max_digits=12)),
                ('total', models.DecimalField(decimal_places=2, max_digits=12)),
                ('fecha', models.DateTimeField()),
                ('token', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('envio', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Envios.envio')),
                ('producto', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Producto.producto')),
                ('usuario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['envio'],
            },
        ),
    ]
