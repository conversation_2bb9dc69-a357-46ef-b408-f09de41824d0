{% extends 'Base/base.html' %}
{% block title %}Ingreso Detalle Por Consignacion{% endblock %}

{% block content %}

{% if messages %}
{% for message in messages %}
<script>
  Swal.fire({
    "title": "Informacion Sistema",
    "text": "{{message}}",
    "icon": "{{message.tags}}"
  })
</script>
{% endfor %}
{% endif %}



<div class="content-wrapper">

  <div class="container-xxl flex-grow-1 container-p-y">

    <div class="card-body" style="background-color: white;">

    <div class="row">

      <div class="col-md-4">
        <label for="">Consignacion</label>
        <p style="color: red;">{{t.traslado}}</p>
      </div>

      <div class="col-md-4">
        <label for="">Proveedor</label>
        <p style="color: red;">{{t.id_prov.nombre}}</p>
      </div>

      <div class="col-md-4">
        <label for="">Fecha</label>
        <p style="color: red;">{{t.fecha|date:"d-m-Y"}}</p>
      </div>

    </div><br><br>

   

    <hr>
    <form action="#" method="POST">{% csrf_token %}
    <div class="row" style="border-bottom: 2px solid black;" align="center">
      

      <div class="col-md-3">
        
        <label for="">Tipo Busqueda</label>
        <select name="tipo" class="form-control" required>
          <option value="nombre">Nombre Producto</option>
          <option value="codigo">Codigo Producto</option>
        </select>
      </div>

      <div class="col-md-6">
          <label for="">Busqueda de Productos</label><br>
              <input type="text" name="buscar" class="form-control" autofocus="buscar"
                  placeholder="Agregar/Buscar">
                </form><br>
      </div>
      <div class="col-md-2" style="margin-top: 15px;">
        <form action="#" method="POST">{% csrf_token %}
        <button name="terminar" class="btn btn-danger">Terminar</button>
      </form>
      </div>

  </div>
  
  <div class="row">

    <div class="col-md-12">

      {% if b %}
          <div style="overflow-y: scroll; max-height: 11rem;">
              <div class="table-responsive">
                  <table class="table table-bordered table-sm">
                      <thead>
                          <tr>
                              <th scope="col">Codigo</th>
                              <th scope="col">Producto</th>
                              <th scope="col">Stock</th>
                              <th scope="col">Precio Compra</th>
                              <th scope="col">Precio Venta</th>
                              <th scope="col">Nuevo Precio Compra</th>
                              <th scope="col">Nuevo Precio Venta</th>
                              <th scope="col">Cant</th>
                          </tr>
                      </thead>
                      <tbody>
                          {% for p in buscar %}
                          <tr>
                              <td>{{p.id}}</td>
                              <td>{{p.nombre}}</td>
                              <td>{{p.stock}}</td>
                              <td>Q.{{p.precio_compra}}</td>
                              <td>Q.{{p.precio_venta}}</td>
                              <form action="#" method="POST">{% csrf_token %}
                                  <input type="hidden" value="{{p.id}}" name="id">
                                  <td><input type="text" name="nuevocompra" placeholder="0"></td>
                                  <td><input type="text" name="nuevoventa" placeholder="0"></td>
                                  <td><input type="text" name="cantidad" placeholder="0"></td>
                                  <td><button name="agregar" class="btn btn-sm btn-success"><i
                                              style="color: white;"
                                              class="bx bx-plus-circle"></i></button>
                                  </td>
                              </form>
                              
                          </tr>
                          {% endfor %}
                      </tbody>
                  </table>
              </div>
          </div>
          {% else %}
          <caption>SIN RESULTADOS</caption>
          {% endif %}

    </div>

  </div>



  <div class="row" align="center">

      <div class="col-md-12">

          <label for="">Productos Agregados</label>

          <div style="height: 12rem; overflow-y: scroll;">
              <div class="table-responsive">
                  <table class="table">
                      <thead>
                          <tr>
                              <th scope="col">Cod</th>
                              <th scope="col">Prod</th>
                              <th scope="col">Cant</th>
                              <th scope="col">Precio Compra</th>
                              <th scope="col">Precio Venta</th>
                              <th scope="col">Total</th>
                          </tr>
                      </thead>
                      <tbody>
                          {% for d in d %}
                          <tr>
                              <th scope="row">{{d.id_prod.id}}</th>
                              <td>{{d.id_prod.nombre}}</td>
                              <td>{{d.cantidad}}</td>
                              <td>Q{{d.compra_ahora}}</td>
                              <td>Q{{d.venta_ahora}}</td>
                              <td>Q.{{d.total}}</td>
                              <form action="#" method="POST">{% csrf_token %}
                                  <input type="hidden" name="corr" value="{{d.id}}">
                                  <td><button name="quitar" class="btn btn-sm btn-danger"><i
                                              style="color: white;" class="bx bx-x"></i></button>
                                  </td>
                              </form>
                          </tr>
                          {% empty %}
                          <caption>SIN PRODUCTOS</caption>
                          {% endfor %}
                      </tbody>
                  </table>
              </div>
          </div>

      </div>

  </div>



    </div>

</div>
    





{% endblock %}