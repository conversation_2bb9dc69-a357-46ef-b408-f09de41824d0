from django.db import models
from user.models import User
from Producto.models import Producto
import uuid


class Vale(models.Model):
    origen = models.CharField(max_length=550, blank=False, null=False)
    destino = models.CharField(max_length=550, blank=False, null=False)
    total = models.DecimalField(
        max_digits=12, decimal_places=2, blank=True, null=True, default=0.00)
    fecha = models.DateTimeField(blank=False, null=False)
    usuario = models.ForeignKey(
        User, on_delete=models.CASCADE, blank=False, null=False)
    token = models.UUIDField(default=uuid.uuid4, editable=False)
    estado = models.IntegerField(blank=False, null=False, default=0)
    obs = models.CharField(max_length=1550, blank=True, null=True, default='')

    class Meta:
        ordering = ["id"]

    def __str__(self):
        return str(self.id)


class DetalleVale(models.Model):
    envio = models.ForeignKey(
        Vale, on_delete=models.CASCADE, blank=False, null=False)
    producto = models.ForeignKey(
        Producto, on_delete=models.CASCADE, blank=False, null=False)
    cantidad = models.IntegerField(blank=False, null=False)
    precio = models.DecimalField(
        max_digits=12, decimal_places=2, blank=False, null=False)
    total = models.DecimalField(
        max_digits=12, decimal_places=2, blank=False, null=False)
    fecha = models.DateTimeField(blank=False, null=False)
    usuario = models.ForeignKey(
        User, on_delete=models.CASCADE, blank=False, null=False)
    token = models.UUIDField(default=uuid.uuid4, editable=False)
    estado = models.IntegerField(blank=False, null=False, default=1)

    class Meta:
        ordering = ["envio"]

    def __str__(self):
        return str(self.envio)
