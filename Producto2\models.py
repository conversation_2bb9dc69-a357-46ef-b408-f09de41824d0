from django.db import models
from user.models import User
from Categoria.models import Categoria
from Proveedor.models import Proveedor


class Producto(models.Model):
    nombre = models.CharField(max_length=550,blank=False,null=False)
    descripcion = models.CharField(max_length=550,blank=False,null=False)
    stock = models.IntegerField(blank=False,null=False,default=0)
    ingreso = models.IntegerField(blank=False,null=False,default=0)
    salio = models.IntegerField(blank=False,null=False,default=0)
    precio_compra = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False,default="0.00")
    precio_venta = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False,default="0.00")
    tienda = models.CharField(max_length=550,blank=False,null=False)
    fecha = models.CharField(max_length=10,blank=False,null=False)
    id_cate = models.ForeignKey(Categoria,on_delete=models.CASCADE,blank=False,null=False)
    usuario = models.ForeignKey(User,on_delete=models.CASCADE,blank=False,null=False)

    class Meta:
        ordering = ["id"]

    def __str__(self):
        return self.nombre


class ProductoFactura(models.Model):
    factura = models.BigIntegerField(primary_key=True,blank=False,null=False)
    serie = models.CharField(max_length=50,blank=False,null=False,default='S/S')
    fecha_factura = models.DateField(blank=False,null=False)
    cantidad = models.IntegerField(blank=False,null=False,default=0)
    total = models.DecimalField(max_digits=15,decimal_places=2,blank=False,null=False,default=0.00)
    id_prov = models.ForeignKey(Proveedor,on_delete=models.CASCADE,blank=False,null=False)
    tienda = models.CharField(max_length=550,blank=True,null=True)
    fecha = models.DateTimeField(blank=False,null=False)
    usuario = models.ForeignKey(User,on_delete=models.CASCADE,blank=False,null=False)
    estado = models.IntegerField(blank=True,null=True,default=1)

    class Meta:
        ordering = ["factura"]

    def __str__(self):
        return str(self.factura)
    

class DetalleFactura(models.Model):
    factura = models.ForeignKey(ProductoFactura,on_delete=models.CASCADE,blank=False,null=False)
    id_prod = models.ForeignKey(Producto,on_delete=models.CASCADE,blank=False,null=False)
    cantidad = models.IntegerField(blank=False,null=False,default=0)
    stock_antes = models.IntegerField(blank=False,null=False,default=0)
    stock_ahora = models.IntegerField(blank=False,null=False,default=0)
    compra_antes = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False,default=0.00)
    venta_antes = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False,default=0.00)
    compra_ahora = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False,default=0.00)
    venta_ahora = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False,default=0.00)
    total = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False,default=0.00)
    fecha = models.DateTimeField(blank=False,null=False)
    usuario = models.ForeignKey(User,on_delete=models.CASCADE,blank=False,null=False)
    estado = models.IntegerField(blank=True,null=True,default=1)

    class Meta:
        ordering = ["factura"]

    def __str__(self):
        return str(self.factura)









class Bitacora(models.Model):
    id_prod = models.BigIntegerField(blank=True, null=True, default=0)
    prod = models.CharField(max_length=1000, blank=True, null=True)
    tipo = models.CharField(max_length=550, blank=True, null=True)
    doc = models.CharField(max_length=500,blank=True, null=True, default='')
    habia = models.BigIntegerField(blank=True, null=True, default=0)
    ingreso = models.BigIntegerField(blank=True, null=True, default=0)
    salio = models.BigIntegerField(blank=True, null=True, default=0)
    hay = models.BigIntegerField(blank=True, null=True, default=0)
    tienda = models.CharField(max_length=550, blank=True, null=True)
    fecha = models.CharField(max_length=255, blank=True, null=True)
    usuario = models.CharField(max_length=200, blank=True, null=True)

    class Meta:
        ordering = ["id"]

    def __str__(self):
        return self.prod
    

