from io import BytesIO
from datetime import datetime
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib.styles import ParagraphStyle, TA_CENTER, TA_LEFT
from reportlab.lib.units import inch, mm, cm
from reportlab.lib import colors
from reportlab.platypus import (
    Paragraph,
    Table,
    SimpleDocTemplate,
    Spacer,
    TableStyle,
    Paragraph)
from reportlab.lib.styles import getSampleStyleSheet
from django.db.models import Sum
from reportlab.platypus import Image

# from .models import Persona
from Venta.models import Venta, Detalle
from Sucursal.models import Envios,DetalleEnvios,Vales,DetalleVales
from user.models import User


class Comprobante():

    def __init__(self, f):
        self.buf = BytesIO()
        self.rastreo = Envios.objects.get(token=f)
        self.usuario = User.objects.get(id=self.rastreo.usuario.id)

    def run(self):

        if self.rastreo.tienda == 'Estanzuela':
            self.doc = SimpleDocTemplate(
                self.buf, title=f"Comprobante Envio # {self.rastreo.correlativo}.1", pagesize=A4,
                leftMargin=40, rightMargin=40, topMargin=-10, bottomMargin=5)
        elif self.rastreo.tienda == 'Teculutan':
            self.doc = SimpleDocTemplate(
                self.buf, title=f"Comprobante Envio # {self.rastreo.correlativo}.2", pagesize=A4,
                leftMargin=40, rightMargin=40, topMargin=-10, bottomMargin=5)
        elif self.rastreo.tienda == 'Santa Cruz':
            self.doc = SimpleDocTemplate(
                self.buf, title=f"Comprobante Envio # {self.rastreo.correlativo}.4", pagesize=A4,
                leftMargin=40, rightMargin=40, topMargin=-10, bottomMargin=5)
        elif self.rastreo.tienda == 'Gualan':
            self.doc = SimpleDocTemplate(
                self.buf, title=f"Comprobante Envio # {self.rastreo.correlativo}.5", pagesize=A4,
                leftMargin=40, rightMargin=40, topMargin=-10, bottomMargin=5)    
        else:
            self.doc = SimpleDocTemplate(
                self.buf, title=f"Comprobante Envio # {self.rastreo.correlativo}.3", pagesize=A4,
                leftMargin=40, rightMargin=40, topMargin=-10, bottomMargin=5)
                        
        self.story = []
        self.encabezado()
        self.crearTabla()
        self.doc.build(self.story, onFirstPage=self.numeroPagina,
                       onLaterPages=self.numeroPagina)
        pdf = self.buf.getvalue()
        self.buf.close()
        return pdf

    def encabezado(self):

        if self.rastreo.origen == "Zacapa":
            imagen_logo = Image('Sucursal/zacapa.jpg', width=345,
                                height=95, hAlign='LEFT', mask=[5, 5, 5, 7, 7, 7, ])

        elif self.rastreo.origen == "Estanzuela":
            imagen_logo = Image('Sucursal/estanzuela.jpg', width=345,
                                height=95, hAlign='LEFT', mask=[5, 5, 5, 7, 7, 7, ])
        
        elif self.rastreo.origen == "Teculutan":
            imagen_logo = Image('Sucursal/tecu.jpg', width=345,
                                height=95, hAlign='LEFT', mask=[5, 5, 5, 7, 7, 7, ])
        else:
            imagen_logo = Image('Sucursal/stacruz.jpg', width=345,
                               height=95, hAlign='LEFT', mask=[5, 5, 5, 7, 7, 7, ])
            

        if self.rastreo.origen == "Zacapa":
            a3 = Paragraph(
                f"Lugar y Fecha: BARRIO LA ESTACION LOCAL 2 ZACAPA {self.rastreo.fecha.strftime('%d/%m/%Y, %H:%M:%S')}", self.estiloPC2())
            a1 = Paragraph(
                f"Sucursal Origen: SANTA ROSALIA MOTOS ZACAPA", self.estiloPC2())

            a1_1 = Paragraph(
                f"Sucursal Destino: {self.rastreo.destino} ", self.estiloPC2())

        elif self.rastreo.origen == "Estanzuela":
            a3 = Paragraph(
                f"Lugar y Fecha: KILOMETRO 141 5 RUTA A ESQUIPULAS ZONA 4 ESTANZUELA ZACAPA {self.rastreo.fecha.strftime('%d/%m/%Y, %H:%M:%S')}", self.estiloPC2())
            a1 = Paragraph(
                f"Sucursal Origen: SERVICIO AGRICOLA SANTA ROSALIA", self.estiloPC2())

            a1_1 = Paragraph(
                f"Sucursal Destino: {self.rastreo.destino} ", self.estiloPC2())

        elif self.rastreo.destino == "Teculutan":
            a3 = Paragraph(
                f"Lugar y Fecha: BARRIO EL CALVARIO ZONA 1 TECULUTAN {self.rastreo.fecha.strftime('%d/%m/%Y, %H:%M:%S')}", self.estiloPC2())
            a1 = Paragraph(
                f"Sucursal Origen: SANTA ROSALIA MOTOS TECULUTAN", self.estiloPC2())

            a1_1 = Paragraph(
                f"Sucursal Destino: {self.rastreo.destino} ", self.estiloPC2())

        else:
            a3 = Paragraph(
                f"Lugar y Fecha: RUTA AL ATLANTICO EL PEAJE ZONA 0 RIO HONDO, ZACAPA {self.rastreo.fecha.strftime('%d/%m/%Y, %H:%M:%S')}", self.estiloPC2())
            a1 = Paragraph(
                f"Sucursal Origen: SANTA CRUZ", self.estiloPC2())

            a1_1 = Paragraph(
                f"Sucursal Destino: {self.rastreo.destino}", self.estiloPC2())

        imagen_logo.hAlign = 'CENTER'
        imagen_logo.vAlign = 'TOP'

        if self.rastreo.origen == "Estanzuela":
            a = Paragraph(f"Envio # {self.rastreo.correlativo}.1", self.estiloPC3())
        elif self.rastreo.origen == "Teculutan":
            a = Paragraph(f"Envio # {self.rastreo.correlativo}.2", self.estiloPC3())
        elif self.rastreo.origen == "Zacapa":
            a = Paragraph(f"Envio # {self.rastreo.correlativo}.3", self.estiloPC3())    
        else:
            a = Paragraph(f"Envio # {self.rastreo.correlativo}.4", self.estiloPC3())

        a4 = Paragraph(
            f"Observaciones {self.rastreo.observacion}", self.estiloPC2())
        self.story.append(imagen_logo)
        self.story.append(a)
        self.story.append(a3)
        self.story.append(a1)
        self.story.append(a1_1)

        self.story.append(a4)
        self.story.append(Spacer(0.0, 0.0*inch))

    def crearTabla(self):
        data = [["Producto", "Cantidad", "Precio Compra", "Precio Venta", "Total"]] \
            + [[x.id_prod.nombre, x.cantidad, "Q."+str(x.id_prod.precio_compra), "Q."+str(x.id_prod.precio_venta), "Q."+str(x.total)]
                for x in DetalleEnvios.objects.filter(token=self.rastreo.token)]\
            + [["Total de Envio", "", "", "", "Q."+str(self.rastreo.total)]]

        table = Table(data, colWidths=[
                      10*cm, 2.5*cm, 2.5*cm, 2.3*cm, 2.3*cm, 2.2*cm])
        table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), "CENTER"),
            ('VALIGN', (-1, -1), (-1, -1), 'MIDDLE'),
            ('FONTSIZE', (0, 0), (-1, -2), 7),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.gray)
        ]))

        self.story.append(table)

    def estiloPC(self):
        return ParagraphStyle(name='centrado',
                              fontName="Helvetica-Bold",
                              fontSize=10,
                              alignment=1,
                              spaceAfter=7,)

    def estiloPC2(self):
        return ParagraphStyle(name='izquierda',
                              fontName="Helvetica",
                              fontSize=10,
                              alignment=0,
                              spaceAfter=2,
                              leftIndent=-30,
                              borderColor='#FF5733')

    def estiloPC3(self):
        return ParagraphStyle(name='derecha',
                              fontName="Helvetica",
                              fontSize=10,
                              alignment=2,
                              spaceAfter=2,
                              rightIndent=-30,)

    def numeroPagina(self, canvas, doc):
        num = canvas.getPageNumber()
        text = "Pagina %s" % num
        canvas.drawRightString(200*mm, 20*mm, text)








class Comprobante2():

    def __init__(self, f):
        self.buf = BytesIO()
        self.rastreo = Vales.objects.get(token=f)
        self.usuario = User.objects.get(id=self.rastreo.usuario.id)

    def run(self):

        if self.rastreo.tienda == 'Estanzuela':
            self.doc = SimpleDocTemplate(
                self.buf, title=f"Comprobante Vale # {self.rastreo.correlativo}.1", pagesize=A4,
                leftMargin=40, rightMargin=40, topMargin=-10, bottomMargin=5)
        elif self.rastreo.tienda == 'Teculutan':
            self.doc = SimpleDocTemplate(
                self.buf, title=f"Comprobante Vale # {self.rastreo.correlativo}.2", pagesize=A4,
                leftMargin=40, rightMargin=40, topMargin=-10, bottomMargin=5)
        elif self.rastreo.tienda == 'Santa Cruz':
            self.doc = SimpleDocTemplate(
                self.buf, title=f"Comprobante Vale # {self.rastreo.correlativo}.4", pagesize=A4,
                leftMargin=40, rightMargin=40, topMargin=-10, bottomMargin=5)
        else:
            self.doc = SimpleDocTemplate(
                self.buf, title=f"Comprobante Vale # {self.rastreo.correlativo}.3", pagesize=A4,
                leftMargin=40, rightMargin=40, topMargin=-10, bottomMargin=5)
                        
        self.story = []
        self.encabezado()
        self.crearTabla()
        self.doc.build(self.story, onFirstPage=self.numeroPagina,
                       onLaterPages=self.numeroPagina)
        pdf = self.buf.getvalue()
        self.buf.close()
        return pdf

    def encabezado(self):

        if self.rastreo.origen == "Zacapa":
            imagen_logo = Image('Sucursal/zacapa.jpg', width=345,
                                height=95, hAlign='LEFT', mask=[5, 5, 5, 7, 7, 7, ])

        elif self.rastreo.origen == "Estanzuela":
            imagen_logo = Image('Sucursal/estanzuela.jpg', width=345,
                                height=95, hAlign='LEFT', mask=[5, 5, 5, 7, 7, 7, ])
        
        elif self.rastreo.origen == "Teculutan":
            imagen_logo = Image('Sucursal/tecu.jpg', width=345,
                                height=95, hAlign='LEFT', mask=[5, 5, 5, 7, 7, 7, ])
        else:
            imagen_logo = Image('Sucursal/stacruz.jpg', width=345,
                               height=95, hAlign='LEFT', mask=[5, 5, 5, 7, 7, 7, ])
            

        if self.rastreo.origen == "Zacapa":
            a3 = Paragraph(
                f"Lugar y Fecha: BARRIO LA ESTACION LOCAL 2 ZACAPA {self.rastreo.fecha.strftime('%d/%m/%Y, %H:%M:%S')}", self.estiloPC2())
            a1 = Paragraph(
                f"Sucursal Origen: SANTA ROSALIA MOTOS ZACAPA", self.estiloPC2())

            a1_1 = Paragraph(
                f"Sucursal Destino: {self.rastreo.destino} ", self.estiloPC2())

        elif self.rastreo.origen == "Estanzuela":
            a3 = Paragraph(
                f"Lugar y Fecha: KILOMETRO 141 5 RUTA A ESQUIPULAS ZONA 4 ESTANZUELA ZACAPA {self.rastreo.fecha.strftime('%d/%m/%Y, %H:%M:%S')}", self.estiloPC2())
            a1 = Paragraph(
                f"Sucursal Origen: SERVICIO AGRICOLA SANTA ROSALIA", self.estiloPC2())

            a1_1 = Paragraph(
                f"Sucursal Destino: {self.rastreo.destino} ", self.estiloPC2())

        elif self.rastreo.destino == "Teculutan":
            a3 = Paragraph(
                f"Lugar y Fecha: BARRIO EL CALVARIO ZONA 1 TECULUTAN {self.rastreo.fecha.strftime('%d/%m/%Y, %H:%M:%S')}", self.estiloPC2())
            a1 = Paragraph(
                f"Sucursal Origen: SANTA ROSALIA MOTOS TECULUTAN", self.estiloPC2())

            a1_1 = Paragraph(
                f"Sucursal Destino: {self.rastreo.destino} ", self.estiloPC2())

        else:
            a3 = Paragraph(
                f"Lugar y Fecha: RUTA AL ATLANTICO EL PEAJE ZONA 0 RIO HONDO, ZACAPA {self.rastreo.fecha.strftime('%d/%m/%Y, %H:%M:%S')}", self.estiloPC2())
            a1 = Paragraph(
                f"Sucursal Origen: SANTA CRUZ", self.estiloPC2())

            a1_1 = Paragraph(
                f"Sucursal Destino: {self.rastreo.destino}", self.estiloPC2())

        imagen_logo.hAlign = 'CENTER'
        imagen_logo.vAlign = 'TOP'

        if self.rastreo.origen == "Estanzuela":
            a = Paragraph(f"Vale # {self.rastreo.correlativo}.1", self.estiloPC3())
        elif self.rastreo.origen == "Teculutan":
            a = Paragraph(f"Vale # {self.rastreo.correlativo}.2", self.estiloPC3())
        elif self.rastreo.origen == "Zacapa":
            a = Paragraph(f"Vale # {self.rastreo.correlativo}.3", self.estiloPC3())    
        else:
            a = Paragraph(f"Vale # {self.rastreo.correlativo}.4", self.estiloPC3())

        a4 = Paragraph(
            f"Observaciones {self.rastreo.observacion}", self.estiloPC2())
        self.story.append(imagen_logo)
        self.story.append(a)
        self.story.append(a3)
        self.story.append(a1)
        self.story.append(a1_1)

        self.story.append(a4)
        self.story.append(Spacer(0.0, 0.0*inch))

    def crearTabla(self):
        data = [["Producto", "Cantidad", "Precio Compra", "Precio Venta", "Total"]] \
            + [[x.id_prod.nombre, x.cantidad, "Q."+str(x.id_prod.precio_compra), "Q."+str(x.id_prod.precio_venta), "Q."+str(x.total)]
                for x in DetalleVales.objects.filter(token=self.rastreo.token)]\
            + [["Total de Envio", "", "", "", "Q."+str(self.rastreo.total)]]

        table = Table(data, colWidths=[
                      10*cm, 2.5*cm, 2.5*cm, 2.3*cm, 2.3*cm, 2.2*cm])
        table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), "CENTER"),
            ('VALIGN', (-1, -1), (-1, -1), 'MIDDLE'),
            ('FONTSIZE', (0, 0), (-1, -2), 7),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.gray)
        ]))

        self.story.append(table)

    def estiloPC(self):
        return ParagraphStyle(name='centrado',
                              fontName="Helvetica-Bold",
                              fontSize=10,
                              alignment=1,
                              spaceAfter=7,)

    def estiloPC2(self):
        return ParagraphStyle(name='izquierda',
                              fontName="Helvetica",
                              fontSize=10,
                              alignment=0,
                              spaceAfter=2,
                              leftIndent=-30,
                              borderColor='#FF5733')

    def estiloPC3(self):
        return ParagraphStyle(name='derecha',
                              fontName="Helvetica",
                              fontSize=10,
                              alignment=2,
                              spaceAfter=2,
                              rightIndent=-30,)

    def numeroPagina(self, canvas, doc):
        num = canvas.getPageNumber()
        text = "Pagina %s" % num
        canvas.drawRightString(200*mm, 20*mm, text)