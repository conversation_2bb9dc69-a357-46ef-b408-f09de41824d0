{% extends 'Base/basever.html' %}
{% block title %}Kardex{% endblock %}

{% block content %}

<div class="content-wrapper">

  <div class="container">

    <div class="row">
      <!-- Basic Layout -->
      <div class="col-md-12">

        <div class="container">

          <div class="row">
            <div class="col md-3" style="margin-top: 15px; font-size: 14px;">
              <label for="">Codigo</label><br>
              <strong class="text-danger">{{p.id}}</strong>
            </div>
            <div class="col md-3" style="margin-top: 15px; font-size: 14px;">
              <label for="">Producto</label><br>
              <strong class="text-danger">{{p.nombre}}</strong>
            </div>
            <div class="col md-3" style="margin-top: 15px; font-size: 14px;">
              <label for="">Tienda</label><br>
              <strong class="text-danger">{{p.tienda}}</strong>
            </div>
            <div class="col md-3" style="margin-top: 15px; font-size: 14px;">
              <label for="">Stock</label><br>
              <strong class="text-danger">{{p.stock}}</strong>
            </div>
{% if vendido_fecha == None %}
  <div class="col md-3" style="margin-top: 15px; font-size: 14px;">
    <label for="">Total Vendido</label><br>
    <strong class="text-success">{{ total_vendido }}</strong>
  </div>
{% else %}
  <div class="col md-3" style="margin-top: 15px; font-size: 14px;">
    <label for="">Vendido en Rango</label><br>
    <strong class="text-primary">{{ vendido_fecha }}</strong>
  </div>
  <div class="col-md-12">
    <small class="text-info">*Total vendido filtrado por fechas seleccionadas ({{ inicio }} a {{ fin }})</small>
  </div>
{% endif %}

          </div>

          <div class="row">
            <div class="col md-3" style="margin-top: 15px; font-size: 14px;">
              <label for="">Precio Compra</label><br>
              <strong class="text-danger">Q.{{p.precio_compra}}</strong>
            </div>
            <div class="col md-3" style="margin-top: 15px; font-size: 14px;">
              <label for="">Precio Venta</label><br>
              <strong class="text-danger">Q.{{p.precio_venta}}</strong>
            </div>
            <div class="col md-3" style="margin-top: 15px; font-size: 14px;">
              <label for="">Mejor Venta</label><br>
              <strong class="text-danger">Q.{{mx}}</strong>
            </div>
            <div class="col md-3" style="margin-top: 15px; font-size: 14px;">
              <label for="">Peor Venta</label><br>
              <strong class="text-danger">Q.{{mn}}</strong>
            </div>
            <div class="col md-3" style="margin-top: 15px; font-size: 14px;">
              <label for="">En Venta</label><br>
              <strong class="text-danger">{{v}} veces</strong>
            </div>
          </div><br>

          <form action="#" method="POST">{% csrf_token %}
            <div class="row">

              <div class="col-md-3">
                <label for="">Fecha Inicio</label>
                <input type="date" class="form-control" name="inicio">
              </div>
              <div class="col-md-3">
                <label for="">Fecha Fin</label>
                <input type="date" class="form-control" name="fin">
              </div>
              <div class="col-md-3"><br>
                <button type="submit" class="btn btn-info btn-sm">Filtrar</button>
              </div>



            </div><br>
          </form>




        </div>

        <div class="table-responsive-sm"
          style="width: 100%; height: 500px; overflow: auto; border-collapse: separate; border-spacing: 0;">



          <table class="table table-bordered table-sm order-table">
            <thead>
              <tr>
                <th>Prod</th>
                <th>Fecha</th>
                <th># Doc</th>
                <th>Tipo</th>
                <th>Anterior</th>
                <th>Ingreso</th>
                <th>Salida</th>
                <th>Saldo</th>
                <th>Tienda</th>
                <th>Usuario</th>
              </tr>
            </thead>
                <tbody style="font-size: 12px;">

  {% for p in bitacoras %}
  <tr>
    <td>{{ p.prod }}</td>
    <td>{{ p.fecha }}</td>
    <td>{{ p.doc }}</td>
    <td>{{ p.tipo }}</td>
    <td>{{ p.habia }}</td>
    <td>{{ p.ingreso }}</td>
    <td>{{ p.salio }}</td>
    <td>{{ p.hay }}</td>
    <td>{{ p.tienda }}</td>
    <td>{{ p.usuario }}</td>
  </tr>
  {% endfor %}

  {% for m in movimientos %}
  <tr>
    <td>{{ m.prod }}</td>
    <td>{{ m.fecha }}</td>
    <td>{{ m.doc }}</td>
    <td>{{ m.tipo }}</td>
    <td>{{ m.ultimo }}</td>    {# lo equivalente a "habia" #}
    <td>{{ m.entrada }}</td>   {# lo equivalente a "ingreso" #}
    <td>{{ m.salida }}</td>    {# lo equivalente a "salio" #}
    <td>{{ m.actual }}</td>    {# lo equivalente a "hay" #}
    <td>{{ m.tienda }}</td>
    <td>{{ m.usuario }}</td>
  </tr>
  {% endfor %}
  {% for vale in detallevales %}
  <tr>
    <td>{{ vale.id_prod }}</td>
    <td>{{ vale.fecha }}</td>
    <td>{{ vale.doc }} {{ vale.correlativo }}</td>
    <td>Vale</td>
    <td>{{ vale.id_prod.stock }}</td>
    <td>0</td>
    <td>{{ vale.cantidad }}</td>
    <td>{{ vale.ahora }}</td>
    <td>{{ vale.correlativo.tienda }}</td>
    <td>{{ vale.usuario }}</td>
  </tr>
  {% endfor %}

  {% for envio in detalleenvios %}
  <tr>
    <td>{{ envio.id_prod }}</td>
    <td>{{ envio.fecha }}</td>
    <td>{{ envio.doc }} {{ envio.correlativo }}</td>
    <td>Envio</td>
    <td>{{ envio.id_prod.stock }}</td>
    <td>0</td>
    <td>{{ envio.cantidad }}</td>
    <td>{{ envio.ahora }}</td>
    <td>{{ envio.correlativo.tienda }}</td>
    <td>{{ envio.usuario }}</td>
  </tr>
  {% endfor %}

    {% for t in traslados %}
  <tr>
    <td>{{ t.id_prod }}</td>
    <td>{{ t.fecha }}</td>
    <td>{{ t.traslado }}</td>
    <td>Traslado</td>
    <td>{{ t.stock_antes }}</td>
    <td>0</td>
    <td>{{ t.cantidad }}</td>
    <td>{{ t.stock_ahora }}</td>
    <td>{{ t.traslado.tienda }}</td>
    <td>{{ t.usuario }}</td>
  </tr>
  {% endfor %}

  {% if bitacoras|length == 0 and movimientos|length == 0 %}
  <caption>SIN MOVIMIENTOS</caption>
  {% endif %}

</tbody>



          </table>
        </div>


      </div>

    </div>

  </div>

</div>

<script type="text/javascript">
  (function (document) {
    'use strict';

    var LightTableFilter = (function (Arr) {

      var _input;

      function _onInputEvent(e) {
        _input = e.target;
        var tables = document.getElementsByClassName(_input.getAttribute('data-table'));
        Arr.forEach.call(tables, function (table) {
          Arr.forEach.call(table.tBodies, function (tbody) {
            Arr.forEach.call(tbody.rows, _filter);
          });
        });
      }

      function _filter(row) {
        var text = row.textContent.toLowerCase(), val = _input.value.toLowerCase();
        row.style.display = text.indexOf(val) === -1 ? 'none' : 'table-row';
      }

      return {
        init: function () {
          var inputs = document.getElementsByClassName('light-table-filter');
          Arr.forEach.call(inputs, function (input) {
            input.oninput = _onInputEvent;
          });
        }
      };
    })(Array.prototype);

    document.addEventListener('readystatechange', function () {
      if (document.readyState === 'complete') {
        LightTableFilter.init();
      }
    });

  })(document);
</script>




{% endblock %}