from django import forms
from .models import Proveedor


class ProveedorForm(forms.ModelForm):
   
    class Meta:
        model = Proveedor
        fields = ['nit','nombre','direccion','telefono']

        widgets = { 
            'nit': forms.TextInput(attrs={'class': 'form-control','require':True,'autofocus': True,'placeholder':'Nit'}),
            'nombre': forms.TextInput(attrs={'class': 'form-control','require':True,'autofocus': True,'placeholder':'Nombre de Proveedor'}),
            'direccion': forms.TextInput(attrs={'class': 'form-control','require':True,'autofocus': True,'placeholder':'Direccion de Proveedor'}),
            'telefono': forms.TextInput(attrs={'class': 'form-control','require':True,'autofocus': True,'placeholder':'Telefono Proveedor'}),
        }


class UpdateProveedorForm(forms.ModelForm):
   
    class Meta:
        model = Proveedor
        fields = ['nit','nombre','direccion','telefono']

        widgets = { 
            'nit': forms.TextInput(attrs={'class': 'form-control','require':True,'autofocus': True,'readonly':True,'placeholder':'Nit'}),
            'nombre': forms.TextInput(attrs={'class': 'form-control','require':True,'autofocus': True,'placeholder':'Nombre de Proveedor'}),
            'direccion': forms.TextInput(attrs={'class': 'form-control','require':True,'autofocus': True,'placeholder':'Direccion de Proveedor'}),
            'telefono': forms.TextInput(attrs={'class': 'form-control','require':True,'autofocus': True,'placeholder':'Telefono Proveedor'}),
        }



