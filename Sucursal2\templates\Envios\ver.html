{% extends 'Base/basever.html' %}
{% block title %}Detalle Envio{% endblock %}

{% block content %}


<div class="content-wrapper">

    <div class="container-xxl flex-grow-1 container-p-y">

        <div class="row">
            <!-- Basic Layout -->
            <div class="col-md-12">
                <div class="card md-6">
                    <div class="card-header d-flex align-items-center justify-content-between">
                        <h5 class="mb-0">Detalles de Envio</h5>
                        <small class="text-muted float-end">Detalles de Envio</small>
                    </div>
                    <div class="card-body">

                        <label for="DatosCliente" class="form-label label-venta">
                            <h4>DATOS DE
                                ENVIO</h4>
                        </label>
                        <form action="#" method="POST">{% csrf_token %}
                            <div class="row">
                                <div class="col-md-4">
                                    <label for="" class="form-label">Sucursal Origen:</label>
                                    <input type="text" class="form-control" value="{{e.origen}}" readonly>
                                </div>
                                <div class="col-md-4">
                                    <label for="" class="form-label">Sucursal Destino:</label>
                                    <input type="text" class="form-control" value="{{e.destino}}" readonly>
                                </div>
                                <div class="col-md-4">
                                    <label for="" class="form-label">Total Envio:</label>
                                    <input type="text" class="form-control" value="{{e.total}}" readonly>
                                </div>
                            </div>
                        </form>

                    </div>&nbsp;

                    <div class="row" align="center">



                        <div class="col-md-12" style="border-right: 2px solid black;">
                            <label for="">Datos de Venta</label><br>
                            <div class="table-responsive">
                                <table class="table table-bordered table-sm" align="center">
                                    <thead>
                                        <tr align="center">
                                            <th>Envio</th>
                                            <th>Total</th>
                                            <th>Fecha</th>
                                            <th>Verificacion</th>
                                            <th>Estado</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr align="center">
                                            <td>{{e.id}}</td>
                                            <td>Q.{{e.total}}</td>
                                            <td>{{e.fecha| date:"d-m-Y H:m:s"}}</td>
                                            {% if tk %}
                                            <td class="table-success" align="center"><i style="font-size: 30px;"
                                                    class='bx bxs-check-shield'></i></td>
                                            {% else %}
                                            <td class="table-danger" align="center"><i style="font-size: 30px;"
                                                    class='bx bxs-bug'></i></td>
                                            {% endif %}
                                            {% if e.estado == 1 %}
                                            <td>Recibido/Terminado</td>
                                            {% elif e.estado == 0 %}
                                            <td>En Proceso/Enviado</td>
                                            {%elif e.estado == 99 %}
                                            <td>Pendiente/Recibir</td>
                                            {% endif %}
                                        </tr>

                                    </tbody>
                                </table>

                            </div>

                        </div>


                    </div><br>



                    <div class="row" align="center">

                        <div class="col-md-12">

                            <label for="">Productos Agregados</label>

                            <div style="height: 12rem; overflow-y: scroll;">
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th scope="col">Cod</th>
                                                <th scope="col">Producto</th>
                                                <th scope="col">Cant</th>
                                                <th scope="col">Precio Uni</th>
                                                <th scope="col">Total</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for d in d %}
                                            <tr>
                                                <th scope="row">{{d.id_prod}}</th>
                                                <td>{{d.id_prod.nombre}}</td>
                                                <td>{{d.cantidad}}</td>
                                                <td>Q{{d.precio_venta}}</td>
                                                <td>Q.{{d.total}}</td>
                                            </tr>
                                            {% empty %}
                                            <caption>SIN PRODUCTOS</caption>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                        </div>

                    </div>

                </div>




            </div>




        </div>
    </div>


    <script type="text/javascript">
        function popUp(URL) {
            window.open(URL, 'Ver Envio', 'toolbar=0,scrollbars=0,location=0,statusbar=0,menubar=0,resizable=1,width=1050,height=500,left = 590,top = 150');
        }
    </script>



    {% endblock %}