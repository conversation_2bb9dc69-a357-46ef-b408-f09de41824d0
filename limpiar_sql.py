#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para limpiar referencias del cliente anterior en el archivo SQL
"""

import re

def limpiar_sql():
    # Leer el archivo SQL original
    with open('corporac_corporacion.sql', 'r', encoding='utf-8') as f:
        contenido = f.read()
    
    # Reemplazos específicos
    reemplazos = {
        # Cambiar nombre de base de datos
        'corporac_corporacion': 'multiservicios_db',
        
        # Cambiar referencias a Santa Rosalia
        'SANTA ROSALIA': 'CIUDAD',
        'Santa Rosalia': 'Ciudad',
        'santa rosalia': 'ciudad',
        'CORPORACION SANTA ROSALIA': 'MULTISERVICIOS SAGASTUME',
        'Corporacion Santa Rosalia': 'Multiservicios Sagastume',
        'corporacion santa rosalia': 'multiservicios sagastume',
        
        # Cambiar referencias específicas del negocio
        'SERVICIO AGRICOLA SANTA ROSALIA': 'MULTISERVICIOS SAGASTUME',
        'Servicio Agricola Santa Rosalia': 'Multiservicios Sagastume',
        'SEMILLAS DE ZACAPA': 'PRODUCTOS GENERALES',
        'Semillas de Zacapa': 'Productos Generales',
        
        # Cambiar categorías específicas de motos
        'Accesorios Para Motos': 'Accesorios Generales',
        'Repuestos de Moto': 'Repuestos de Vehiculo',
        'Repuestos Moto': 'Repuestos Vehiculo',
        'Motos Yamaha': 'Vehiculos',
        'motos': 'vehiculos',
        'Motos': 'Vehiculos',
        'MOTOS': 'VEHICULOS',
        
        # Cambiar referencias a semillas
        'Semillas': 'Productos Agricolas',
        'SEMILLAS': 'PRODUCTOS AGRICOLAS',
        'semillas': 'productos agricolas',
        
        # Cambiar nombres de tiendas específicas
        'Zacapa': 'Sucursal 1',
        'Estanzuela': 'Sucursal 2', 
        'Teculutan': 'Sucursal 3',
        'Santa Cruz': 'Sucursal 4',
        'Gualan': 'Sucursal 5',
        
        # Cambiar referencias a agroservicio
        'agroservicio': 'multiservicios',
        'Agroservicio': 'Multiservicios',
        'AGROSERVICIO': 'MULTISERVICIOS',
        
        # Cambiar referencias a servicio agricola
        'servicio agricola': 'multiservicios',
        'Servicio Agricola': 'Multiservicios',
        'SERVICIO AGRICOLA': 'MULTISERVICIOS',
        
        # Cambiar direcciones específicas
        'ZACAPA': 'CIUDAD',
        'TECULUTAN': 'CIUDAD',
        'ESTANZUELA': 'CIUDAD',
        'RIO HONDO': 'CIUDAD',
        
        # Cambiar nombres de empresas relacionadas
        'Santa Rosalia Motos': 'Multiservicios Sagastume',
        'SANTA ROSALIA MOTOS': 'MULTISERVICIOS SAGASTUME',
        'santa rosalia motos': 'multiservicios sagastume',
    }
    
    # Aplicar reemplazos
    contenido_limpio = contenido
    for original, reemplazo in reemplazos.items():
        contenido_limpio = contenido_limpio.replace(original, reemplazo)
    
    # Limpiar productos específicos que hacen referencia a servicios de motos
    contenido_limpio = re.sub(r'Servicio[s]? [dD]e [Mm]otos?', 'Servicios Generales', contenido_limpio)
    contenido_limpio = re.sub(r'Servicio[s]? y Ventas de [Mm]otos?', 'Servicios y Ventas Generales', contenido_limpio)
    
    # Guardar el archivo limpio
    with open('corporac_corporacion_limpio.sql', 'w', encoding='utf-8') as f:
        f.write(contenido_limpio)
    
    print("Archivo SQL limpiado exitosamente. Guardado como: corporac_corporacion_limpio.sql")
    print("Reemplazos realizados:")
    for original, reemplazo in reemplazos.items():
        count = contenido.count(original)
        if count > 0:
            print(f"  '{original}' -> '{reemplazo}' ({count} veces)")

if __name__ == "__main__":
    limpiar_sql()
