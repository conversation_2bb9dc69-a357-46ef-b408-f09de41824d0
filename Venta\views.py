from decimal import Decimal
from django.shortcuts import render, redirect
from datetime import datetime
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from Venta.models import Venta, Detalle
from Venta.forms import VentaForm, UpdateVentaForm
from Venta.repote import Comprobante,ProformaServicio
from Producto.models import Producto, Bitacora
from Cliente.models import Cliente
from user.models import User
from django.db.models import Q
import uuid
from django.db.models import Sum
from Sucursal.models import Movimientos

import emisor
import receptor
import InfileFel
from django.http import HttpResponse


@login_required
def listado(request):

    if request.user.rol == "admin":
        datos = Venta.objects.all().order_by('-factura')
    else:
        datos = Venta.objects.filter(
            tienda=request.user.tienda).order_by('-factura')

    return render(request, 'Venta/lista.html', {'venta': datos})


@login_required
def ver(request, t):
    ver = Venta.objects.get(token=t)
    datos = Detalle.objects.filter(token=t)
    return render(request, 'Venta/ver.html', {'ver': ver, 'detalle': datos})


@login_required
def actualizar(request, id):
    ven = Venta.objects.get(id=id)
    if request.method == 'GET':
        form = UpdateVentaForm(instance=ven)
    else:
        form = UpdateVentaForm(request.POST, instance=ven)

        if form.is_valid():
            try:
                ven.fecha = str(datetime.today().strftime('%Y-%m-%d'))
                ven.usuario = User.objects.get(id=request.user.id)
                form.save()
                messages.success(
                    request, f'Venta Numero {ven.factura} Modificada Exitosamente!')
                return redirect('ListaVenta')
            except:
                messages.error(
                    request, f'No Se Pudo Modificar Venta Numero {ven.factura}!')
                return redirect('ListaVenta')

    return render(request, 'Venta/actualizar.html', {'form': form})


@login_required
def anular(request, id):

    venta = Venta.objects.get(factura=id)

    if venta.tipo == "FEL":

        miventa = Venta.objects.get(factura=id)
        midetalle = Detalle.objects.filter(factura=id)
        ver = Cliente.objects.filter(nit=miventa.nit).exists()

        if ver:
            micliente = Cliente.objects.get(nit=miventa.nit)
            for d in midetalle:
                prod = Producto.objects.get(id=d.id_prod.id)
                if venta.tienda == "Estanzuela":
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(venta.interno_estanzuela,'Venta-FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                             (prod.stock+d.cantidad),'Venta Fue Anulada',venta.usuario)
                elif venta.tienda == "Teculutan":
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(venta.interno_tecu,'Venta-FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                             (prod.stock+d.cantidad),'Venta Fue Anulada',venta.usuario)
                elif venta.tienda == "Zacapa":
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(venta.interno_zacapa,'Venta-FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                             (prod.stock+d.cantidad),'Venta Fue Anulada',venta.usuario)
                elif venta.tienda == "Gualan":
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(venta.interno_gualan,'Venta-FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                             (prod.stock+d.cantidad),'Venta Fue Anulada',venta.usuario)    
                else:
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(venta.interno_rio_hondo,'Venta-FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                             (prod.stock+d.cantidad),'Venta Fue Anulada',venta.usuario)
                Producto.objects.filter(id=d.id_prod.id).update(
                    stock=prod.stock+d.cantidad, salio=prod.salio-d.cantidad)

            Cliente.objects.filter(nit=micliente.nit).update(
                compras_contado=micliente.compras_contado+1, total_contado=micliente.total_contado+miventa.total)
            Venta.objects.filter(factura=id).update(estado=2)
            Detalle.objects.filter(factura=id).update(estado=2)
            messages.warning(
                request, f'Venta Anulada Productos Vuelven a Inventario!')
            return redirect('ListaVenta')
        else:
            for d in midetalle:
                prod = Producto.objects.get(id=d.id_prod.id)
                Producto.objects.filter(id=d.id_prod.id).update(
                    stock=prod.stock+d.cantidad, salio=prod.salio-d.cantidad)
                if venta.tienda == "Estanzuela":
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(venta.interno_estanzuela,'Venta-FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                             (prod.stock+d.cantidad),'Venta Fue Anulada',venta.usuario)
                elif venta.tienda == "Teculutan":
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(venta.interno_tecu,'Venta-FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                             (prod.stock+d.cantidad),'Venta Fue Anulada',venta.usuario)
                elif venta.tienda == "Gualan":
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(venta.interno_gualan,'Venta-FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                             (prod.stock+d.cantidad),'Venta Fue Anulada',venta.usuario)
                elif venta.tienda == "Zacapa":
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(venta.interno_zacapa,'Venta-FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                             (prod.stock+d.cantidad),'Venta Fue Anulada',venta.usuario)    
                else:
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(venta.interno_rio_hondo,'Venta-FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                             (prod.stock+d.cantidad),'Venta Fue Anulada',venta.usuario)

            Venta.objects.filter(factura=id).update(estado=2)
            Detalle.objects.filter(factura=id).update(estado=2)
            messages.warning(
                request, f'Venta Anulada Productos Vuelven a Inventario!')
            return redirect('ListaVenta')

    elif venta.tipo == "PROFORMA":

        miventa = Venta.objects.get(factura=id)
        midetalle = Detalle.objects.filter(factura=id)
        ver = Cliente.objects.filter(nit=miventa.nit).exists()

        if ver:
            micliente = Cliente.objects.get(nit=miventa.nit)
            for d in midetalle:
                prod = Producto.objects.get(id=d.id_prod.id)
                Producto.objects.filter(id=d.id_prod.id).update(
                    stock=prod.stock+d.cantidad, salio=prod.salio-d.cantidad)
                if venta.tienda == "Estanzuela":
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(venta.interno_estanzuela,'Proforma',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                             (prod.stock+d.cantidad),'Venta Fue Anulada',venta.usuario)
                elif venta.tienda == "Teculutan":
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(venta.interno_tecu,'Proforma',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                             (prod.stock+d.cantidad),'Venta Fue Anulada',venta.usuario)
                elif venta.tienda == "Zacapa":
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(venta.interno_zacapa,'Proforma',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                             (prod.stock+d.cantidad),'Venta Fue Anulada',venta.usuario)
                elif venta.tienda == "Gualan":
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(venta.interno_gualan,'Proforma',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                             (prod.stock+d.cantidad),'Venta Fue Anulada',venta.usuario)    
                else:
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(venta.interno_rio_hondo,'Proforma',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                             (prod.stock+d.cantidad),'Venta Fue Anulada',venta.usuario)

            Cliente.objects.filter(nit=micliente.nit).update(
                compras_contado=micliente.compras_contado+1, total_contado=micliente.total_contado+miventa.total)
            Venta.objects.filter(factura=id).update(estado=2)
            Detalle.objects.filter(factura=id).update(estado=2)
            messages.warning(
                request, f'Venta Anulada Productos Vuelven a Inventario!')
            return redirect('ListaVenta')
        else:
            for d in midetalle:
                prod = Producto.objects.get(id=d.id_prod.id)
                Producto.objects.filter(id=d.id_prod.id).update(
                    stock=prod.stock+d.cantidad, salio=prod.salio-d.cantidad)
                if venta.tienda == "Estanzuela":
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(venta.interno_estanzuela,'Proforma',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                             (prod.stock+d.cantidad),'Venta Fue Anulada',venta.usuario)
                elif venta.tienda == "Teculutan":
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(venta.interno_tecu,'Proforma',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                             (prod.stock+d.cantidad),'Venta Fue Anulada',venta.usuario)
                elif venta.tienda == "Zacapa":
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(venta.interno_zacapa,'Proforma',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                             (prod.stock+d.cantidad),'Venta Fue Anulada',venta.usuario)
                elif venta.tienda == "Gualan":
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(venta.interno_gualan,'Proforma',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                             (prod.stock+d.cantidad),'Venta Fue Anulada',venta.usuario)    
                else:
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(venta.interno_rio_hondo,'Proforma',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                             (prod.stock+d.cantidad),'Venta Fue Anulada',venta.usuario)

            Venta.objects.filter(factura=id).update(estado=2)
            Detalle.objects.filter(factura=id).update(estado=2)
            messages.warning(
                request, f'Venta Anulada Productos Vuelven a Inventario!')
            return redirect('ListaVenta')

    elif venta.tipo == "NOTA CREDITO":

        miventa = Venta.objects.get(factura=id)
        midetalle = Detalle.objects.filter(factura=id)
        ver = Cliente.objects.filter(nit=miventa.nit).exists()

        if ver:
            micliente = Cliente.objects.get(nit=miventa.nit)
            for d in midetalle:
                prod = Producto.objects.get(id=d.id_prod.id)
                Producto.objects.filter(id=d.id_prod.id).update(
                    stock=prod.stock+d.cantidad, salio=prod.salio-d.cantidad)
                if venta.tienda == "Estanzuela":
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(venta.interno_estanzuela,'Nota de Credito',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                             (prod.stock+d.cantidad),'Venta Fue Anulada',venta.usuario)
                elif venta.tienda == "Teculutan":
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(venta.interno_tecu,'Nota de Credito',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                             (prod.stock+d.cantidad),'Venta Fue Anulada',venta.usuario)
                elif venta.tienda == "Zacapa":
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(venta.interno_zacapa,'Nota de Credito',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                             (prod.stock+d.cantidad),'Venta Fue Anulada',venta.usuario)
                elif venta.tienda == "Gualan":
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(venta.interno_gualan,'Nota de Credito',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                             (prod.stock+d.cantidad),'Venta Fue Anulada',venta.usuario)    
                else:
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(venta.interno_rio_hondo,'Nota de Credito',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                             (prod.stock+d.cantidad),'Venta Fue Anulada',venta.usuario)

            Cliente.objects.filter(nit=micliente.nit).update(
                compras_credito=micliente.compras_credito+1, total_credito=micliente.total_credito+miventa.total)
            Venta.objects.filter(factura=id).update(estado=2)
            Detalle.objects.filter(factura=id).update(estado=2)
            messages.warning(
                request, f'Nota de Credito Anulada Productos Vuelven a Inventario!')
            return redirect('ListaVenta')
        else:
            for d in midetalle:
                prod = Producto.objects.get(id=d.id_prod.id)
                Producto.objects.filter(id=d.id_prod.id).update(
                    stock=prod.stock+d.cantidad, salio=prod.salio-d.cantidad)
                if venta.tienda == "Estanzuela":
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(venta.interno_estanzuela,'Nota de Credito',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                             (prod.stock+d.cantidad),'Venta Fue Anulada',venta.usuario)
                elif venta.tienda == "Teculutan":
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(venta.interno_tecu,'Nota de Credito',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                             (prod.stock+d.cantidad),'Venta Fue Anulada',venta.usuario)
                elif venta.tienda == "Zacapa":
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(venta.interno_zacapa,'Nota de Credito',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                             (prod.stock+d.cantidad),'Venta Fue Anulada',venta.usuario)
                elif venta.tienda == "Gualan":
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(venta.interno_gualan,'Nota de Credito',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                             (prod.stock+d.cantidad),'Venta Fue Anulada',venta.usuario)    
                else:
                    # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                    movi(venta.interno_rio_hondo,'Nota de Credito',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                             (prod.stock+d.cantidad),'Venta Fue Anulada',venta.usuario)

            Venta.objects.filter(factura=id).update(estado=2)
            Detalle.objects.filter(factura=id).update(estado=2)
            messages.warning(
                request, f'Nota de Credito Anulada Productos Vuelven a Inventario!')
            return redirect('ListaVenta')

    else:
        return redirect('ListaVenta')


@login_required
def nueva(request):
    form = VentaForm()
    if request.method == "POST":
        form = VentaForm(request.POST)
        if form.is_valid():

            if request.user.tienda == "Estanzuela":
                esta = Venta.objects.filter(tienda="Estanzuela").order_by(
                    'interno_estanzuela').last()
                estapro = Venta.objects.filter(tienda="Estanzuela").order_by(
                    'pro_estanzuela').last()
                if Cliente.objects.filter(nit=form.cleaned_data['nit']).exists():
                    c = Cliente.objects.get(nit=form.cleaned_data['nit'])
                    v = Venta()
                    v.nit = c.nit
                    v.nombre = c.nombre
                    v.direccion = c.direccion
                    v.tipo = form.cleaned_data['tipo']
                    v.total = 0.00
                    v.token = uuid.uuid4()
                    v.tienda = request.user.tienda
                    v.estado = 0
                    v.fecha = datetime.today()
                    v.usuario = User.objects.get(id=request.user.id)
                    if esta.interno_estanzuela == 0:
                        v.interno_estanzuela = 6305
                    else:
                        v.interno_estanzuela = esta.interno_estanzuela+1
                    
                    if v.tipo == 'PROFORMA' or v.tipo == 'PROFORMA-Servicio':
                        if estapro.pro_estanzuela == 0:
                            v.pro_estanzuela = 2001
                        else:
                            v.pro_estanzuela = estapro.pro_estanzuela+1
                        
                    else:
                        pass          
                               
                    v.save()
                    messages.success(request, f'{v.nombre} Ingresado!')
                    return redirect('Detalle', v.token)
                else:
                    estapro = Venta.objects.filter(tienda="Estanzuela").order_by(
                    'pro_estanzuela').last()
                    v = Venta()
                    v.nit = 'CF'
                    v.nombre = 'Consumidor Final'
                    v.direccion = 'Ciudad'
                    v.tipo = form.cleaned_data['tipo']
                    v.total = 0.00
                    v.token = uuid.uuid4()
                    v.tienda = request.user.tienda
                    v.estado = 0
                    v.fecha = datetime.today()
                    v.usuario = User.objects.get(id=request.user.id)

                    if esta.interno_estanzuela == 0:
                        v.interno_estanzuela = 6305
                    else:
                        v.interno_estanzuela = esta.interno_estanzuela+1

                    
                    if v.tipo == 'PROFORMA' or v.tipo == 'PROFORMA-Servicio':
                        if estapro.pro_estanzuela == 0:
                            v.pro_estanzuela = 2001
                        else:
                            v.pro_estanzuela = estapro.pro_estanzuela+1
                    else:
                        pass          
                    v.save()
                    messages.success(request, f'{v.nombre} Ingresado!')
                    return redirect('Detalle', v.token)
            # -----------------------------------------------------------------

            elif request.user.tienda == "Santa Cruz":
                if Venta.objects.filter(tienda="Santa Cruz").order_by(
                    'interno_rio_hondo').last():

                    esta = Venta.objects.filter(tienda="Santa Cruz").order_by(
                        'interno_rio_hondo').last()
                    print(esta)
                else:
                    esta = 0

                if Venta.objects.filter(tienda="Santa Cruz").order_by(
                    'pro_rio_hondo').last():
                    estapro = Venta.objects.filter(tienda="Santa Cruz").order_by(
                    'pro_rio_hondo').last()
                else:
                    estapro = 0    
                print(esta)
                print(estapro)
                if Cliente.objects.filter(nit=form.cleaned_data['nit']).exists():
                    c = Cliente.objects.get(nit=form.cleaned_data['nit'])
                    v = Venta()
                    v.nit = c.nit
                    v.nombre = c.nombre
                    v.direccion = c.direccion
                    v.tipo = form.cleaned_data['tipo']
                    v.total = 0.00
                    v.token = uuid.uuid4()
                    v.tienda = request.user.tienda
                    v.estado = 0
                    v.fecha = datetime.today()
                    v.usuario = User.objects.get(id=request.user.id)
                    if esta == 0:
                        v.interno_rio_hondo = 1
                    else:
                        v.interno_rio_hondo = esta.interno_rio_hondo+1
                    
                    if v.tipo == 'PROFORMA' or v.tipo == 'PROFORMA-Servicio':
                        if estapro == 0:
                            v.pro_rio_hondo = 1
                        else:
                            v.pro_rio_hondo = estapro.pro_rio_hondo+1
                        
                    else:
                        pass          
                               
                    v.save()
                    messages.success(request, f'{v.nombre} Ingresado!')
                    return redirect('Detalle', v.token)
                else:
                    estapro = Venta.objects.filter(tienda="Santa Cruz").order_by(
                    'pro_rio_hondo').last()
                    v = Venta()
                    v.nit = 'CF'
                    v.nombre = 'Consumidor Final'
                    v.direccion = 'Ciudad'
                    v.tipo = form.cleaned_data['tipo']
                    v.total = 0.00
                    v.token = uuid.uuid4()
                    v.tienda = request.user.tienda
                    v.estado = 0
                    v.fecha = datetime.today()
                    v.usuario = User.objects.get(id=request.user.id)

                    if esta == 0:
                        v.interno_rio_hondo = 1
                    else:
                        v.interno_rio_hondo = esta.interno_rio_hondo+1

                    
                    if v.tipo == 'PROFORMA' or v.tipo == 'PROFORMA-Servicio':
                        if estapro == 0:
                            v.pro_rio_hondo = 1
                        else:
                            v.pro_rio_hondo = estapro.pro_rio_hondo+1
                    else:
                        pass          
                    v.save()
                    messages.success(request, f'{v.nombre} Ingresado!')
                    return redirect('Detalle', v.token)
            # -----------------------------------------------------------------

            # -----------------------------------------------------------------

            elif request.user.tienda == "Gualan":
                if Venta.objects.filter(tienda="Gualan").order_by(
                    'interno_gualan').last():

                    esta = Venta.objects.filter(tienda="Gualan").order_by(
                        'interno_gualan').last()
                    print(esta)
                else:
                    esta = 0

                if Venta.objects.filter(tienda="Gualan").order_by(
                    'pro_gualan').last():
                    estapro = Venta.objects.filter(tienda="Gualan").order_by(
                    'pro_gualan').last()
                else:
                    estapro = 0    
                print(esta)
                print(estapro)
                if Cliente.objects.filter(nit=form.cleaned_data['nit']).exists():
                    c = Cliente.objects.get(nit=form.cleaned_data['nit'])
                    v = Venta()
                    v.nit = c.nit
                    v.nombre = c.nombre
                    v.direccion = c.direccion
                    v.tipo = form.cleaned_data['tipo']
                    v.total = 0.00
                    v.token = uuid.uuid4()
                    v.tienda = request.user.tienda
                    v.estado = 0
                    v.fecha = datetime.today()
                    v.usuario = User.objects.get(id=request.user.id)
                    if esta == 0:
                        v.interno_gualan = 1
                    else:
                        v.interno_gualan = esta.interno_gualan+1
                    
                    if v.tipo == 'PROFORMA' or v.tipo == 'PROFORMA-Servicio':
                        if estapro == 0:
                            v.pro_gualan = 1
                        else:
                            v.pro_gualan = estapro.pro_gualan+1
                        
                    else:
                        pass          
                               
                    v.save()
                    messages.success(request, f'{v.nombre} Ingresado!')
                    return redirect('Detalle', v.token)
                else:
                    estapro = Venta.objects.filter(tienda="Gualan").order_by(
                    'pro_rio_hondo').last()
                    v = Venta()
                    v.nit = 'CF'
                    v.nombre = 'Consumidor Final'
                    v.direccion = 'Ciudad'
                    v.tipo = form.cleaned_data['tipo']
                    v.total = 0.00
                    v.token = uuid.uuid4()
                    v.tienda = request.user.tienda
                    v.estado = 0
                    v.fecha = datetime.today()
                    v.usuario = User.objects.get(id=request.user.id)

                    if esta == 0:
                        v.interno_gualan = 1
                    else:
                        v.interno_gualan = esta.interno_gualan+1

                    
                    if v.tipo == 'PROFORMA' or v.tipo == 'PROFORMA-Servicio':
                        if estapro == 0:
                            v.pro_rio_hondo = 1
                        else:
                            v.pro_gualan = estapro.pro_gualan+1
                    else:
                        pass          
                    v.save()
                    messages.success(request, f'{v.nombre} Ingresado!')
                    return redirect('Detalle', v.token)
            # -----------------------------------------------------------------


            elif request.user.tienda == "Teculutan":
                esta = Venta.objects.filter(
                    tienda="Teculutan").order_by('interno_tecu').last()
                estapro = Venta.objects.filter(tienda="Teculutan").order_by(
                    'pro_tecu').last()
                if Cliente.objects.filter(nit=form.cleaned_data['nit']).exists():
                    c = Cliente.objects.get(nit=form.cleaned_data['nit'])
                    v = Venta()
                    v.nit = c.nit
                    v.nombre = c.nombre
                    v.direccion = c.direccion
                    v.tipo = form.cleaned_data['tipo']
                    v.total = 0.00
                    v.token = uuid.uuid4()
                    v.tienda = request.user.tienda
                    v.estado = 0
                    v.fecha = datetime.today()
                    v.usuario = User.objects.get(id=request.user.id)
                    if esta.interno_tecu == 0:
                        v.interno_tecu = 571
                    else:
                        v.interno_tecu = esta.interno_tecu+1
                    
                    if v.tipo == 'PROFORMA' or v.tipo == 'PROFORMA-Servicio':
                        if estapro.pro_tecu == 0:
                            v.pro_tecu = 2001
                        else:
                            v.pro_tecu = estapro.pro_tecu+1
                        
                    else:
                        pass          
                    v.save()
                    messages.success(request, f'{v.nombre} Ingresado!')
                    return redirect('Detalle', v.token)
                else:
                    estapro = Venta.objects.filter(tienda="Teculutan").order_by(
                    'pro_tecu').last()
                    v = Venta()
                    v.nit = 'CF'
                    v.nombre = 'Consumidor Final'
                    v.direccion = 'Ciudad'
                    v.tipo = form.cleaned_data['tipo']
                    v.total = 0.00
                    v.token = uuid.uuid4()
                    v.tienda = request.user.tienda
                    v.estado = 0
                    v.fecha = datetime.today()
                    v.usuario = User.objects.get(id=request.user.id)
                    if esta.interno_tecu == 0:
                        v.interno_tecu = 571
                    else:
                        v.interno_tecu = esta.interno_tecu+1
                    if v.tipo == 'PROFORMA' or v.tipo == 'PROFORMA-Servicio':
                        if estapro.pro_tecu == 0:
                            v.pro_tecu = 2001
                        else:
                            v.pro_tecu = estapro.pro_tecu+1
                    else:
                        pass          
                    v.save()
                    messages.success(request, f'{v.nombre} Ingresado!')
                    return redirect('Detalle', v.token)

            else:
                esta = Venta.objects.filter(
                    tienda="Zacapa").order_by('interno_zacapa').last()
                estapro = Venta.objects.filter(tienda="Zacapa").order_by(
                    'pro_zacapa').last()
                if Cliente.objects.filter(nit=form.cleaned_data['nit']).exists():
                    c = Cliente.objects.get(nit=form.cleaned_data['nit'])
                    v = Venta()
                    v.nit = c.nit
                    v.nombre = c.nombre
                    v.direccion = c.direccion
                    v.tipo = form.cleaned_data['tipo']
                    v.total = 0.00
                    v.token = uuid.uuid4()
                    v.tienda = request.user.tienda
                    v.estado = 0
                    v.fecha = datetime.today()
                    v.usuario = User.objects.get(id=request.user.id)
                    if esta.interno_zacapa == 0:
                        v.interno_zacapa = 270
                    else:
                        v.interno_zacapa = esta.interno_zacapa+1
                    
                    if v.tipo == 'PROFORMA' or v.tipo == 'PROFORMA-Servicio':
                        if estapro.pro_zacapa == 0:
                            v.pro_zacapa = 526
                        else:
                            v.pro_zacapa = estapro.pro_zacapa+1
                        
                    else:
                        pass          
                    v.save()
                    messages.success(request, f'{v.nombre} Ingresado!')
                    return redirect('Detalle', v.token)
                else:
                    estapro = Venta.objects.filter(tienda=request.user.tienda).order_by(
                    'pro_zacapa').last()
                    v = Venta()
                    v.nit = 'CF'
                    v.nombre = 'Consumidor Final'
                    v.direccion = 'Ciudad'
                    v.tipo = form.cleaned_data['tipo']
                    v.total = 0.00
                    v.token = uuid.uuid4()
                    v.tienda = request.user.tienda
                    v.estado = 0
                    v.fecha = datetime.today()
                    v.usuario = User.objects.get(id=request.user.id)
                    if esta.interno_zacapa == 0:
                        v.interno_zacapa = 270
                    else:
                        v.interno_zacapa = esta.interno_zacapa+1
                    if v.tipo == 'PROFORMA' or v.tipo == 'PROFORMA-Servicio':
                        if estapro.pro_zacapa == 0:
                            v.pro_zacapa = 526
                        else:
                            v.pro_zacapa = estapro.pro_zacapa+1
                    else:
                        pass          
                    v.save()
                    messages.success(request, f'{v.nombre} Ingresado!')
                    return redirect('Detalle', v.token)

    return render(request, 'Venta/nueva.html', {'form': form})
    

@login_required
def proformaser(request):

    b = False
    if request.method == 'POST':
            return redirect('PDFProformaServicio',request.POST['numero'],request.POST['detalle'],request.POST['cantidad'],request.POST['precio'],request.POST['tienda'],str(request.POST['cliente']).replace('%20',' '))

        
    return render(request, 'Venta/proforma-ser.html',{'b':b})    


@login_required
def detalle(request, t):

    venta = Venta.objects.get(token=t)
    det = Detalle.objects.filter(token=t)
    vercli = Cliente.objects.filter(nit=venta.nit).exists()

    if vercli:
        micli = Cliente.objects.get(nit=venta.nit)
        Venta.objects.filter(token=t).update(
            nit=micli.nit, nombre=micli.nombre, direccion=micli.direccion)
    else:
        Venta.objects.filter(token=t).update(
            nit='CF', nombre='Consumidor Final', direccion='Ciudad')

    # verificacion de token para validar venta
    if str(t) == str(venta.token):
        tok = True
    else:
        tok = False

    if request.method == "POST":

        if tok:

            if 'buscar' in request.POST:

                if request.user.rol == "admin":
                    if request.POST['buscar'] == "":
                        messages.error(
                            request, f'Campo Busqueda No Puede Estar Vacio')
                        return redirect('Detalle', t)
                    else:
                        querys = (Q(id__icontains=request.POST['buscar'].strip()) | Q(
                            nombre__icontains=request.POST['buscar'].strip()))
                        busqueda = Producto.objects.filter(
                            querys)
                        return render(request, 'Venta/detalle.html', {'venta': venta, 'tok': tok, 'b': busqueda, 'd': det})
                else:
                    if request.POST['buscar'] == "":
                        messages.error(
                            request, f'Campo Busqueda No Puede Estar Vacio')
                        return redirect('Detalle', t)
                    else:
                        querys = (Q(id__icontains=request.POST['buscar'].strip()) | Q(
                            nombre__icontains=request.POST['buscar'].strip()))
                        busqueda = Producto.objects.filter(
                            querys, tienda=request.user.tienda)
                        return render(request, 'Venta/detalle.html', {'venta': venta, 'tok': tok, 'b': busqueda, 'd': det})

            elif 'agregarservicio' in request.POST:

                if request.user.tienda == "Estanzuela":

                    d = Detalle()
                    d.factura = Venta.objects.get(factura=venta.factura)
                    d.id_prod = Producto.objects.get(id=4518)
                    d.detalle_ser = request.POST['nombreservicio']
                    d.cantidad = int(request.POST['cantidadservicio'])
                    d.precio = Decimal(request.POST['precioservicio'])
                    d.subtotal = d.precio*d.cantidad
                    d.descuento = int(0)
                    d.total = d.subtotal-d.descuento
                    d.fecha = datetime.today()
                    d.usuario = User.objects.get(id=request.user.id)
                    d.token = t
                    d.save()

                    if request.user.rol == "admin":
                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                        movi(venta.pro_estanzuela,'Servicio-FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,0,d.cantidad,0,\
                             0,'Venta Por Servicios',venta.usuario)
                    else:
                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                        movi(venta.pro_estanzuela,'Servicio-FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,0,d.cantidad,0,\
                             0,'Venta Por Servicios',venta.usuario)

                    Venta.objects.filter(factura=venta.factura, token=t).update(
                        total=venta.total+((int(request.POST['cantidadservicio'])*d.precio)-d.descuento))
                    messages.success(
                        request, f'Agregando {request.POST["cantidadservicio"]} de {d.detalle_ser}')
                    return redirect('Detalle', t)

                elif request.user.tienda == "Teculutan":

                    d = Detalle()
                    d.factura = Venta.objects.get(factura=venta.factura)
                    d.id_prod = Producto.objects.get(id=4505)
                    d.detalle_ser = request.POST['nombreservicio']
                    d.cantidad = int(request.POST['cantidadservicio'])
                    d.precio = Decimal(request.POST['precioservicio'])
                    d.subtotal = d.precio*d.cantidad
                    d.descuento = int(0)
                    d.total = d.subtotal-d.descuento
                    d.fecha = datetime.today()
                    d.usuario = User.objects.get(id=request.user.id)
                    d.token = t
                    d.save()

                    if request.user.rol == "admin":
                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                        movi(venta.pro_tecu,'Servicio-FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,0,d.cantidad,0,\
                             0,'Venta Por Servicios',venta.usuario)
                    else:
                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                        movi(venta.pro_tecu,'Servicio-FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,0,d.cantidad,0,\
                             0,'Venta Por Servicios',venta.usuario)

                    Venta.objects.filter(factura=venta.factura, token=t).update(
                        total=venta.total+((int(request.POST['cantidadservicio'])*d.precio)-d.descuento))
                    messages.success(
                        request, f'Agregando {request.POST["cantidadservicio"]} de {d.detalle_ser}')
                    return redirect('Detalle', t)
                
                elif request.user.tienda == "Santa Cruz":

                    d = Detalle()
                    d.factura = Venta.objects.get(factura=venta.factura)
                    d.id_prod = Producto.objects.get(id=4505)
                    d.detalle_ser = request.POST['nombreservicio']
                    d.cantidad = int(request.POST['cantidadservicio'])
                    d.precio = Decimal(request.POST['precioservicio'])
                    d.subtotal = d.precio*d.cantidad
                    d.descuento = int(0)
                    d.total = d.subtotal-d.descuento
                    d.fecha = datetime.today()
                    d.usuario = User.objects.get(id=request.user.id)
                    d.token = t
                    d.save()

                    if request.user.rol == "admin":
                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                        movi(venta.pro_rio_hondo,'Servicio-FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,0,d.cantidad,0,\
                             0,'Venta Por Servicios',venta.usuario)
                    else:
                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                        movi(venta.pro_rio_hondo,'Servicio-FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,0,d.cantidad,0,\
                             0,'Venta Por Servicios',venta.usuario)

                    Venta.objects.filter(factura=venta.factura, token=t).update(
                        total=venta.total+((int(request.POST['cantidadservicio'])*d.precio)-d.descuento))
                    messages.success(
                        request, f'Agregando {request.POST["cantidadservicio"]} de {d.detalle_ser}')
                    return redirect('Detalle', t)
                
                elif request.user.tienda == "Gualan":

                    d = Detalle()
                    d.factura = Venta.objects.get(factura=venta.factura)
                    d.id_prod = Producto.objects.get(id=5494)
                    d.detalle_ser = request.POST['nombreservicio']
                    d.cantidad = int(request.POST['cantidadservicio'])
                    d.precio = Decimal(request.POST['precioservicio'])
                    d.subtotal = d.precio*d.cantidad
                    d.descuento = int(0)
                    d.total = d.subtotal-d.descuento
                    d.fecha = datetime.today()
                    d.usuario = User.objects.get(id=request.user.id)
                    d.token = t
                    d.save()

                    if request.user.rol == "admin":
                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                        movi(venta.pro_gualan,'Servicio-FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,0,d.cantidad,0,\
                             0,'Venta Por Servicios',venta.usuario)
                    else:
                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                        movi(venta.pro_gualan,'Servicio-FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,0,d.cantidad,0,\
                             0,'Venta Por Servicios',venta.usuario)

                    Venta.objects.filter(factura=venta.factura, token=t).update(
                        total=venta.total+((int(request.POST['cantidadservicio'])*d.precio)-d.descuento))
                    messages.success(
                        request, f'Agregando {request.POST["cantidadservicio"]} de {d.detalle_ser}')
                    return redirect('Detalle', t)

                else:

                    d = Detalle()
                    d.factura = Venta.objects.get(factura=venta.factura)
                    d.id_prod = Producto.objects.get(id=4519)
                    d.detalle_ser = request.POST['nombreservicio']
                    d.cantidad = int(request.POST['cantidadservicio'])
                    d.precio = Decimal(request.POST['precioservicio'])
                    d.subtotal = d.precio*d.cantidad
                    d.descuento = int(0)
                    d.total = d.subtotal-d.descuento
                    d.fecha = datetime.today()
                    d.usuario = User.objects.get(id=request.user.id)
                    d.token = t
                    d.save()

                    if request.user.rol == "admin":
                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                        movi(venta.pro_zacapa,'Servicio-FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,0,d.cantidad,0,\
                             0,'Venta Por Servicios',venta.usuario)
                    else:
                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                        movi(venta.pro_zacapa,'Servicio-FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,0,d.cantidad,0,\
                             0,'Venta Por Servicios',venta.usuario)

                    Venta.objects.filter(factura=venta.factura, token=t).update(
                        total=venta.total+((int(request.POST['cantidadservicio'])*d.precio)-d.descuento))
                    messages.success(
                        request, f'Agregando {request.POST["cantidadservicio"]} de {d.detalle_ser}')
                    return redirect('Detalle', t)

            elif 'agregar' in request.POST:

                ver = Producto.objects.get(id=request.POST['id'])

                if request.POST['descuento'] == "":
                    des = Decimal(0.00)
                else:
                    des = Decimal(request.POST['descuento'])

                if ver.stock >= int(request.POST['cantidad']):

                    if Detalle.objects.filter(id_prod=ver.id, token=t).exists():

                        endetalle = Detalle.objects.get(
                            id_prod=ver.id, token=t)

                        Detalle.objects.filter(id_prod=ver.id, token=t).update(cantidad=endetalle.cantidad+int(
                            request.POST['cantidad']), descuento=des, total=endetalle.total+(Decimal(request.POST['precio'])*int(request.POST['cantidad']))-des, subtotal=endetalle.total+(ver.precio_venta*int(request.POST['cantidad'])))
                        
                        
                        if request.user.rol == "admin":
                            if venta.tienda == 'Estanzuela':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_estanzuela,'FEL',venta.tienda,endetalle.id_prod.pk,endetalle.id_prod.nombre,endetalle.id_prod.stock,0,endetalle.cantidad,\
                                (endetalle.id_prod.stock-endetalle.cantidad),'Venta FEL',venta.usuario)
                            elif venta.tienda == 'Teculutan':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_tecu,'FEL',venta.tienda,endetalle.id_prod.pk,endetalle.id_prod.nombre,endetalle.id_prod.stock,0,endetalle.cantidad,\
                                (endetalle.id_prod.stock-endetalle.cantidad),'Venta FEL',venta.usuario)
                            elif venta.tienda == 'Santa Cruz':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_rio_hondo,'FEL',venta.tienda,endetalle.id_prod.pk,endetalle.id_prod.nombre,endetalle.id_prod.stock,0,endetalle.cantidad,\
                                (endetalle.id_prod.stock-endetalle.cantidad),'Venta FEL',venta.usuario)    
                            elif venta.tienda == 'Gualan':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_gualan,'FEL',venta.tienda,endetalle.id_prod.pk,endetalle.id_prod.nombre,endetalle.id_prod.stock,0,endetalle.cantidad,\
                                (endetalle.id_prod.stock-endetalle.cantidad),'Venta FEL',venta.usuario)        
                            else:
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_zacapa,'FEL',venta.tienda,endetalle.id_prod.pk,endetalle.id_prod.nombre,endetalle.id_prod.stock,0,endetalle.cantidad,\
                                (endetalle.id_prod.stock-endetalle.cantidad),'Venta FEL',venta.usuario)       
                        else:
                            if venta.tienda == 'Estanzuela':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_estanzuela,'FEL',venta.tienda,endetalle.id_prod.pk,endetalle.id_prod.nombre,endetalle.id_prod.stock,0,endetalle.cantidad,\
                                (endetalle.id_prod.stock-endetalle.cantidad),'Venta FEL',venta.usuario)
                            elif venta.tienda == 'Teculutan':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_tecu,'FEL',venta.tienda,endetalle.id_prod.pk,endetalle.id_prod.nombre,endetalle.id_prod.stock,0,endetalle.cantidad,\
                                (endetalle.id_prod.stock-endetalle.cantidad),'Venta FEL',venta.usuario)
                            elif venta.tienda == 'Santa Cruz':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_rio_hondo,'FEL',venta.tienda,endetalle.id_prod.pk,endetalle.id_prod.nombre,endetalle.id_prod.stock,0,endetalle.cantidad,\
                                (endetalle.id_prod.stock-endetalle.cantidad),'Venta FEL',venta.usuario)  
                            elif venta.tienda == 'Gualan':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_gualan,'FEL',venta.tienda,endetalle.id_prod.pk,endetalle.id_prod.nombre,endetalle.id_prod.stock,0,endetalle.cantidad,\
                                (endetalle.id_prod.stock-endetalle.cantidad),'Venta FEL',venta.usuario)      
                            else:
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_zacapa,'FEL',venta.tienda,endetalle.id_prod.pk,endetalle.id_prod.nombre,endetalle.id_prod.stock,0,endetalle.cantidad,\
                                (endetalle.id_prod.stock-endetalle.cantidad),'Venta FEL',venta.usuario) 
                        
                        if venta.tipo == 'PROFORMA':
                            pass
                        else:
                            Producto.objects.filter(id=ver.id).update(
                            stock=ver.stock-int(request.POST['cantidad']), salio=ver.salio+int(request.POST['cantidad']))    


                        Venta.objects.filter(factura=venta.factura, token=t).update(
                            total=venta.total+((int(request.POST['cantidad'])*ver.precio_venta)-des))  


                        messages.success(
                            request, f'Agregando {request.POST["cantidad"]} Unidad de {ver.nombre}')
                        return redirect('Detalle', t)
                    else:

                        d = Detalle()
                        d.factura = Venta.objects.get(factura=venta.factura)
                        d.id_prod = Producto.objects.get(id=ver.id)
                        d.cantidad = int(request.POST['cantidad'])
                        d.precio = Decimal(request.POST['precio'])
                        d.subtotal = d.precio*d.cantidad
                        d.descuento = des
                        d.total = d.subtotal-d.descuento
                        d.fecha = datetime.today()
                        d.usuario = User.objects.get(id=request.user.id)
                        d.token = t
                        d.save()

                        if request.user.rol == "admin":
                            if venta.tienda == 'Estanzuela':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_estanzuela,'FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,d.id_prod.stock,0,d.cantidad,\
                                (d.id_prod.stock-d.cantidad),'Venta FEL',venta.usuario)
                            elif venta.tienda == 'Teculutan':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_tecu,'FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,d.id_prod.stock,0,d.cantidad,\
                                (d.id_prod.stock-d.cantidad),'Venta FEL',venta.usuario)
                            elif venta.tienda == 'Santa Cruz':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_rio_hondo,'FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,d.id_prod.stock,0,d.cantidad,\
                                (d.id_prod.stock-d.cantidad),'Venta FEL',venta.usuario)
                            elif venta.tienda == 'Gualan':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_gualan,'FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,d.id_prod.stock,0,d.cantidad,\
                                (d.id_prod.stock-d.cantidad),'Venta FEL',venta.usuario)        
                            else:
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_zacapa,'FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,d.id_prod.stock,0,d.cantidad,\
                                (d.id_prod.stock-d.cantidad),'Venta FEL',venta.usuario)       
                        else:
                            if venta.tienda == 'Estanzuela':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_estanzuela,'FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,d.id_prod.stock,0,d.cantidad,\
                                (d.id_prod.stock-d.cantidad),'Venta FEL',venta.usuario)
                            elif venta.tienda == 'Teculutan':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_tecu,'FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,d.id_prod.stock,0,d.cantidad,\
                                (d.id_prod.stock-d.cantidad),'Venta FEL',venta.usuario)
                            elif venta.tienda == 'Santa Cruz':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_rio_hondo,'FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,d.id_prod.stock,0,d.cantidad,\
                                (d.id_prod.stock-d.cantidad),'Venta FEL',venta.usuario)  
                            elif venta.tienda == 'Gualan':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_gualan,'FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,d.id_prod.stock,0,d.cantidad,\
                                (d.id_prod.stock-d.cantidad),'Venta FEL',venta.usuario)      
                            else:
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_zacapa,'FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,d.id_prod.stock,0,d.cantidad,\
                                (d.id_prod.stock-d.cantidad),'Venta FEL',venta.usuario)         

                        
                        if venta.tipo == 'PROFORMA':
                            pass
                        else:
                            Producto.objects.filter(id=ver.id).update(
                            stock=ver.stock-int(request.POST['cantidad']), salio=ver.salio+int(request.POST['cantidad']))
                        
                            
                            
                        Venta.objects.filter(factura=venta.factura, token=t).update(
                            total=venta.total+((int(request.POST['cantidad'])*Decimal(request.POST['precio']))-d.descuento))
                        messages.success(
                            request, f'Agregando {request.POST["cantidad"]} Unidad de {ver.nombre}')
                        return redirect('Detalle', t)

                else:
                    messages.error(
                        request, f'Producto {ver.nombre} No Tiene Existencia!')
                    return redirect('Detalle', t)

            elif 'quitar' in request.POST:
                idetalle = Detalle.objects.get(
                    id=request.POST['corr'], token=t)
                elprod = Producto.objects.get(id=idetalle.id_prod.id)
                Venta.objects.filter(factura=venta.factura, token=t).update(
                    total=venta.total-((idetalle.cantidad*idetalle.precio)-idetalle.descuento))
                
                if venta.tipo == 'PROFORMA':
                    pass
                else:
                    Producto.objects.filter(id=idetalle.id_prod.id).update(
                    stock=elprod.stock+idetalle.cantidad, salio=elprod.salio-idetalle.cantidad)
             

                if request.user.rol == "admin":
                            if venta.tienda == 'Estanzuela':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_estanzuela,'FEL',venta.tienda,idetalle.id_prod.pk,idetalle.id_prod.nombre,idetalle.id_prod.stock,idetalle.cantidad,0,\
                                (idetalle.id_prod.stock+idetalle.cantidad),'Quitado Venta FEL',venta.usuario)
                            elif venta.tienda == 'Teculutan':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_tecu,'FEL',venta.tienda,idetalle.id_prod.pk,idetalle.id_prod.nombre,idetalle.id_prod.stock,idetalle.cantidad,0,\
                                (idetalle.id_prod.stock+idetalle.cantidad),'Quitado Venta FEL',venta.usuario)
                            elif venta.tienda == 'Santa Cruz':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_rio_hondo,'FEL',venta.tienda,idetalle.id_prod.pk,idetalle.id_prod.nombre,idetalle.id_prod.stock,idetalle.cantidad,0,\
                                (idetalle.id_prod.stock+idetalle.cantidad),'Quitado Venta FEL',venta.usuario)
                            elif venta.tienda == 'Gualan':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_gualan,'FEL',venta.tienda,idetalle.id_prod.pk,idetalle.id_prod.nombre,idetalle.id_prod.stock,idetalle.cantidad,0,\
                                (idetalle.id_prod.stock+idetalle.cantidad),'Quitado Venta FEL',venta.usuario)        
                            else:
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_zacapa,'FEL',venta.tienda,idetalle.id_prod.pk,idetalle.id_prod.nombre,idetalle.id_prod.stock,idetalle.cantidad,0,\
                                (idetalle.id_prod.stock+idetalle.cantidad),'Quitado Venta FEL',venta.usuario)       
                else:
                            if venta.tienda == 'Estanzuela':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_estanzuela,'FEL',venta.tienda,idetalle.id_prod.pk,idetalle.id_prod.nombre,idetalle.id_prod.stock,idetalle.cantidad,0,\
                                (idetalle.id_prod.stock+idetalle.cantidad),'Quitado Venta FEL',venta.usuario)
                            elif venta.tienda == 'Teculutan':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_tecu,'FEL',venta.tienda,idetalle.id_prod.pk,idetalle.id_prod.nombre,idetalle.id_prod.stock,idetalle.cantidad,0,\
                                (idetalle.id_prod.stock+idetalle.cantidad),'Quitado Venta FEL',venta.usuario)
                            elif venta.tienda == 'Santa Cruz':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_rio_hondo,'FEL',venta.tienda,idetalle.id_prod.pk,idetalle.id_prod.nombre,idetalle.id_prod.stock,idetalle.cantidad,0,\
                                (idetalle.id_prod.stock+idetalle.cantidad),'Quitado Venta FEL',venta.usuario)
                            elif venta.tienda == 'Gualan':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_gualan,'FEL',venta.tienda,idetalle.id_prod.pk,idetalle.id_prod.nombre,idetalle.id_prod.stock,idetalle.cantidad,0,\
                                (idetalle.id_prod.stock+idetalle.cantidad),'Quitado Venta FEL',venta.usuario)      
                            else:
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_zacapa,'FEL',venta.tienda,idetalle.id_prod.pk,idetalle.id_prod.nombre,idetalle.id_prod.stock,idetalle.cantidad,0,\
                                (idetalle.id_prod.stock+idetalle.cantidad),'Quitado Venta FEL',venta.usuario)     

                idetalle.delete()
                messages.success(
                    request, f'Se Quitado {idetalle.cantidad} de {elprod.nombre}!')
                return redirect('Detalle', t)

            elif 'quitarservicio' in request.POST:
                idetalle = Detalle.objects.get(
                    id=request.POST['corr'], token=t)
                Venta.objects.filter(factura=venta.factura, token=t).update(
                    total=venta.total-((idetalle.cantidad*idetalle.precio)-idetalle.descuento))
                if request.user.rol == "admin":
                    if request.user.rol == "admin":
                            if venta.tienda == 'Estanzuela':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_estanzuela,'FEL',venta.tienda,idetalle.id_prod.pk,idetalle.id_prod.nombre,idetalle.id_prod.stock,0,0,\
                                (idetalle.id_prod.stock+0),'Quitado Venta FEL',venta.usuario)
                            elif venta.tienda == 'Teculutan':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_tecu,'FEL',venta.tienda,idetalle.id_prod.pk,idetalle.id_prod.nombre,idetalle.id_prod.stock,0,0,\
                                (idetalle.id_prod.stock+0),'Quitado Venta FEL',venta.usuario)
                            elif venta.tienda == 'Santa Cruz':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_rio_hondo,'FEL',venta.tienda,idetalle.id_prod.pk,idetalle.id_prod.nombre,idetalle.id_prod.stock,0,0,\
                                (idetalle.id_prod.stock+0),'Quitado Venta FEL',venta.usuario) 
                            elif venta.tienda == 'Gualan':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_gualan,'FEL',venta.tienda,idetalle.id_prod.pk,idetalle.id_prod.nombre,idetalle.id_prod.stock,0,0,\
                                (idetalle.id_prod.stock+0),'Quitado Venta FEL',venta.usuario)        
                            else:
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_zacapa,'FEL',venta.tienda,idetalle.id_prod.pk,idetalle.id_prod.nombre,idetalle.id_prod.stock,0,0,\
                                (idetalle.id_prod.stock+0),'Quitado Venta FEL',venta.usuario)       
                else:
                            if venta.tienda == 'Estanzuela':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_estanzuela,'FEL',venta.tienda,idetalle.id_prod.pk,idetalle.id_prod.nombre,idetalle.id_prod.stock,0,0,\
                                (idetalle.id_prod.stock+0),'Quitado Venta FEL',venta.usuario)
                            elif venta.tienda == 'Teculutan':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_tecu,'FEL',venta.tienda,idetalle.id_prod.pk,idetalle.id_prod.nombre,idetalle.id_prod.stock,0,0,\
                                (idetalle.id_prod.stock+0),'Quitado Venta FEL',venta.usuario)
                            elif venta.tienda == 'Santa Cruz':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_rio_hondo,'FEL',venta.tienda,idetalle.id_prod.pk,idetalle.id_prod.nombre,idetalle.id_prod.stock,0,0,\
                                (idetalle.id_prod.stock+0),'Quitado Venta FEL',venta.usuario)  
                            elif venta.tienda == 'Gualan':
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_gualan,'FEL',venta.tienda,idetalle.id_prod.pk,idetalle.id_prod.nombre,idetalle.id_prod.stock,0,0,\
                                (idetalle.id_prod.stock+0),'Quitado Venta FEL',venta.usuario)      
                            else:
                                # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                movi(venta.interno_zacapa,'FEL',venta.tienda,idetalle.id_prod.pk,idetalle.id_prod.nombre,idetalle.id_prod.stock,0,0,\
                                (idetalle.id_prod.stock+0),'Quitado Venta FEL',venta.usuario)
                idetalle.delete()
                messages.success(
                    request, f'Se Quitado {idetalle.cantidad} de {idetalle.detalle_ser}!')
                return redirect('Detalle', t)

            elif 'terminar' in request.POST:
                miventa = Venta.objects.get(token=t)

                if venta.tipo == "FEL":
                    if request.user.tienda == "Estanzuela":
                        clientenoexiste(
                            request.POST['nit'], request.POST['nombre'], request.POST['direccion'], t)
                        miventa = Venta.objects.get(token=t)
                        micliente = Cliente.objects.get(nit=miventa.nit)
                        Cliente.objects.filter(nit=micliente.nit).update(
                            compras_contado=micliente.compras_contado+1, total_contado=micliente.total_contado+miventa.total)
                        fel(request, t)
                        messages.info(request, venta.link)
                        return redirect('NuevaVenta')
                    elif request.user.tienda == "Teculutan":
                        clientenoexiste(
                            request.POST['nit'], request.POST['nombre'], request.POST['direccion'], t)
                        miventa = Venta.objects.get(token=t)
                        micliente = Cliente.objects.get(nit=miventa.nit)
                        Cliente.objects.filter(nit=micliente.nit).update(
                            compras_contado=micliente.compras_contado+1, total_contado=micliente.total_contado+miventa.total)
                        fel2(request, t)
                        messages.info(request, venta.link)
                        return redirect('NuevaVenta')
                    elif request.user.tienda == "Santa Cruz":
                        clientenoexiste(
                            request.POST['nit'], request.POST['nombre'], request.POST['direccion'], t)
                        miventa = Venta.objects.get(token=t)
                        micliente = Cliente.objects.get(nit=miventa.nit)
                        Cliente.objects.filter(nit=micliente.nit).update(
                            compras_contado=micliente.compras_contado+1, total_contado=micliente.total_contado+miventa.total)
                        fel7(request, t)
                        messages.info(request, venta.link)
                        return redirect('NuevaVenta')
                    elif request.user.tienda == "Gualan":
                        clientenoexiste(
                            request.POST['nit'], request.POST['nombre'], request.POST['direccion'], t)
                        miventa = Venta.objects.get(token=t)
                        micliente = Cliente.objects.get(nit=miventa.nit)
                        Cliente.objects.filter(nit=micliente.nit).update(
                            compras_contado=micliente.compras_contado+1, total_contado=micliente.total_contado+miventa.total)
                        fel9(request, t)
                        messages.info(request, venta.link)
                        return redirect('NuevaVenta')
                    else:
                        clientenoexiste(
                            request.POST['nit'], request.POST['nombre'], request.POST['direccion'], t)
                        miventa = Venta.objects.get(token=t)
                        micliente = Cliente.objects.get(nit=miventa.nit)
                        Cliente.objects.filter(nit=micliente.nit).update(
                            compras_contado=micliente.compras_contado+1, total_contado=micliente.total_contado+miventa.total)
                        fel3(request, t)
                        messages.info(request, venta.link)
                        return redirect('NuevaVenta')

                if venta.tipo == "FEL-Servicio":
                    if request.user.tienda == "Estanzuela":
                        clientenoexiste(
                            request.POST['nit'], request.POST['nombre'], request.POST['direccion'], t)
                        miventa = Venta.objects.get(token=t)
                        micliente = Cliente.objects.get(nit=miventa.nit)
                        Cliente.objects.filter(nit=micliente.nit).update(
                            compras_contado=micliente.compras_contado+1, total_contado=micliente.total_contado+miventa.total)
                        fel4(request, t)
                        messages.info(request, venta.link)
                        return redirect('NuevaVenta')
                    elif request.user.tienda == "Teculutan":
                        clientenoexiste(
                            request.POST['nit'], request.POST['nombre'], request.POST['direccion'], t)
                        miventa = Venta.objects.get(token=t)
                        micliente = Cliente.objects.get(nit=miventa.nit)
                        Cliente.objects.filter(nit=micliente.nit).update(
                            compras_contado=micliente.compras_contado+1, total_contado=micliente.total_contado+miventa.total)
                        fel5(request, t)
                        messages.info(request, venta.link)
                        return redirect('NuevaVenta')
                    elif request.user.tienda == "Santa Cruz":
                        clientenoexiste(
                            request.POST['nit'], request.POST['nombre'], request.POST['direccion'], t)
                        miventa = Venta.objects.get(token=t)
                        micliente = Cliente.objects.get(nit=miventa.nit)
                        Cliente.objects.filter(nit=micliente.nit).update(
                            compras_contado=micliente.compras_contado+1, total_contado=micliente.total_contado+miventa.total)
                        fel8(request, t)
                        messages.info(request, venta.link)
                        return redirect('NuevaVenta')
                    elif request.user.tienda == "Gualan":
                        clientenoexiste(
                            request.POST['nit'], request.POST['nombre'], request.POST['direccion'], t)
                        miventa = Venta.objects.get(token=t)
                        micliente = Cliente.objects.get(nit=miventa.nit)
                        Cliente.objects.filter(nit=micliente.nit).update(
                            compras_contado=micliente.compras_contado+1, total_contado=micliente.total_contado+miventa.total)
                        fel10(request, t)
                        messages.info(request, venta.link)
                        return redirect('NuevaVenta')
                    else:
                        clientenoexiste(
                            request.POST['nit'], request.POST['nombre'], request.POST['direccion'], t)
                        miventa = Venta.objects.get(token=t)
                        micliente = Cliente.objects.get(nit=miventa.nit)
                        Cliente.objects.filter(nit=micliente.nit).update(
                            compras_contado=micliente.compras_contado+1, total_contado=micliente.total_contado+miventa.total)
                        fel6(request, t)
                        messages.info(request, venta.link)
                        return redirect('NuevaVenta')

                elif venta.tipo == "PROFORMA":
                    miventa = Venta.objects.get(token=t)
                    micliente = Cliente.objects.get(nit=miventa.nit)
                    Cliente.objects.filter(nit=micliente.nit).update(
                        compras_contado=micliente.compras_contado+1, total_contado=micliente.total_contado+miventa.total)
                    Venta.objects.filter(token=t).update(
                        nit=request.POST['nit'], nombre=request.POST['nombre'], direccion=request.POST['direccion'], estado=1)
                    clientenoexiste(
                        request.POST['nit'], request.POST['nombre'], request.POST['direccion'], t)
                    messages.success(request, f'{venta.factura}')
                    return redirect('NuevaVenta')
                
                elif venta.tipo == "PROFORMA-Servicio":
                    miventa = Venta.objects.get(token=t)
                    micliente = Cliente.objects.get(nit=miventa.nit)
                    Cliente.objects.filter(nit=micliente.nit).update(
                        compras_contado=micliente.compras_contado+1, total_contado=micliente.total_contado+miventa.total)
                    Venta.objects.filter(token=t).update(
                        nit=request.POST['nit'], nombre=request.POST['nombre'], direccion=request.POST['direccion'],estado=1)
                    clientenoexiste(
                        request.POST['nit'], request.POST['nombre'], request.POST['direccion'], t)
                    messages.success(request, f'{venta.factura}')
                    return redirect('NuevaVenta')    

                elif venta.tipo == "NOTA CREDITO":
                    miventa = Venta.objects.get(token=t)
                    micliente = Cliente.objects.get(nit=miventa.nit)
                    Cliente.objects.filter(nit=micliente.nit).update(
                        compras_credito=micliente.compras_credito+1, total_credito=micliente.total_credito+miventa.total)
                    Venta.objects.filter(token=t).update(
                        nit=request.POST['nit'], nombre=request.POST['nombre'], direccion=request.POST['direccion'], estado=3)
                    clientenoexiste(
                        request.POST['nit'], request.POST['nombre'], request.POST['direccion'], t)
                    messages.success(request, f'{venta.factura}')
                    return redirect('NuevaVenta')

                else:
                    pass

            elif 'descartar' in request.POST:

                # obetenemos la venta
                miventa = Venta.objects.get(token=t)

                # obtener productos en detalle
                midetalle = Detalle.objects.filter(token=t)

                # obtenemos el cliente para restar lo comprado
                ver = Cliente.objects.filter(nit=miventa.nit).exists()

                if ver:
                    micliente = Cliente.objects.get(nit=miventa.nit)
                    # regresamos el inventario a los productos
                    
                    if miventa.tipo == 'PROFORMA':
                        pass
                    else:
                        for d in midetalle:
                            # lista productos
                            prod = Producto.objects.get(id=d.id_prod.id)
                            Producto.objects.filter(id=d.id_prod.id).update(
                                stock=prod.stock+d.cantidad, salio=prod.salio-d.cantidad)

                            if venta.tipo == "FEL-Servicio":

                                if request.user.rol == "admin":
                                    if venta.tienda == 'Estanzuela':
                                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                        movi(venta.interno_estanzuela,'FEL-Servicio',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                                        (prod.stock+d.cantidad),'Descartado FEL-Servicio',venta.usuario)
                                    elif venta.tienda == 'Teculutan':
                                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                        movi(venta.interno_tecu,'FEL-Servicio',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                                        (prod.stock+d.cantidad),'Descartado FEL-Servicio',venta.usuario)
                                    elif venta.tienda == 'Santa Cruz':
                                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                        movi(venta.interno_rio_hondo,'FEL-Servicio',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                                        (prod.stock+d.cantidad),'Descartado FEL-Servicio',venta.usuario)
                                    elif venta.tienda == 'Gualan':
                                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                        movi(venta.interno_gualan,'FEL-Servicio',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                                        (prod.stock+d.cantidad),'Descartado FEL-Servicio',venta.usuario)        
                                    else:
                                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                        movi(venta.interno_zacapa,'FEL-Servicio',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                                        (prod.stock+d.cantidad),'Descartado FEL-Servicio',venta.usuario)       
                                else:
                                    if venta.tienda == 'Estanzuela':
                                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                        movi(venta.interno_estanzuela,'FEL-Servicio',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                                        (prod.stock+idetalle.cantidad),'Descartado FEL-Servicio',venta.usuario)
                                    elif venta.tienda == 'Teculutan':
                                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                        movi(venta.interno_tecu,'FEL-Servicio',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                                        (d.id_prod.stock+d.cantidad),'Descartado FEL-Servicio',venta.usuario)
                                    elif venta.tienda == 'Santa Cruz':
                                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                        movi(venta.interno_rio_hondo,'FEL-Servicio',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                                        (prod.stock+d.cantidad),'Descartado FEL-Servicio',venta.usuario)
                                    elif venta.tienda == 'Gualan':
                                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                        movi(venta.interno_gualan,'FEL-Servicio',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                                        (prod.stock+d.cantidad),'Descartado FEL-Servicio',venta.usuario)      
                                    else:
                                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                        movi(venta.interno_zacapa,'FEL-Servicio',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                                        (prod.stock+d.cantidad),'Descartado FEL-Servicio',venta.usuario)


                            if venta.tipo == "FEL":

                                if request.user.rol == "admin":
                                    if venta.tienda == 'Estanzuela':
                                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                        movi(venta.interno_estanzuela,'FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                                        (prod.stock+d.cantidad),'Descartado FEL',venta.usuario)
                                    elif venta.tienda == 'Teculutan':
                                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                        movi(venta.interno_tecu,'FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                                        (prod.stock+d.cantidad),'Descartado FEL',venta.usuario)
                                    elif venta.tienda == 'Santa Cruz':
                                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                        movi(venta.interno_rio_hondo,'FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                                        (prod.stock+d.cantidad),'Descartado FEL',venta.usuario) 
                                    elif venta.tienda == 'Gualan':
                                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                        movi(venta.interno_gualan,'FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                                        (prod.stock+d.cantidad),'Descartado FEL',venta.usuario)        
                                    else:
                                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                        movi(venta.interno_zacapa,'FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                                        (prod.stock+d.cantidad),'Descartado FEL',venta.usuario)       
                                else:
                                    if venta.tienda == 'Estanzuela':
                                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                        movi(venta.interno_estanzuela,'FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                                        (prod.stock+idetalle.cantidad),'Descartado FEL',venta.usuario)
                                    elif venta.tienda == 'Teculutan':
                                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                        movi(venta.interno_tecu,'FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                                        (d.id_prod.stock+d.cantidad),'Descartado FEL',venta.usuario)
                                    elif venta.tienda == 'Santa Cruz':
                                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                        movi(venta.interno_rio_hondo,'FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                                        (prod.stock+d.cantidad),'Descartado FEL',venta.usuario) 
                                    elif venta.tienda == 'Gualan':
                                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                        movi(venta.interno_gualan,'FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                                        (prod.stock+d.cantidad),'Descartado FEL',venta.usuario)      
                                    else:
                                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                        movi(venta.interno_zacapa,'FEL',venta.tienda,d.id_prod.pk,d.id_prod.nombre,prod.stock,d.cantidad,0,\
                                        (prod.stock+d.cantidad),'Descartado FEL',venta.usuario)


                            else:

                                if request.user.rol == "admin":
                                    if venta.tienda == 'Estanzuela':
                                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                        movi(venta.interno_estanzuela,'Proforma',venta.tienda,d.id_prod.pk,d.id_prod.nombre,d.id_prod.stock,d.cantidad,0,\
                                        (d.id_prod.stock+d.cantidad),'Descartado Proforma',venta.usuario)
                                    elif venta.tienda == 'Teculutan':
                                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                        movi(venta.interno_tecu,'Proforma',venta.tienda,d.id_prod.pk,d.id_prod.nombre,d.id_prod.stock,d.cantidad,0,\
                                        (d.id_prod.stock+d.cantidad),'Descartado Proforma',venta.usuario)
                                    elif venta.tienda == 'Santa Cruz':
                                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                        movi(venta.interno_rio_hondo,'Proforma',venta.tienda,d.id_prod.pk,d.id_prod.nombre,d.id_prod.stock,d.cantidad,0,\
                                        (d.id_prod.stock+d.cantidad),'Descartado Proforma',venta.usuario) 
                                    elif venta.tienda == 'Gualan':
                                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                        movi(venta.interno_gualan,'Proforma',venta.tienda,d.id_prod.pk,d.id_prod.nombre,d.id_prod.stock,d.cantidad,0,\
                                        (d.id_prod.stock+d.cantidad),'Descartado Proforma',venta.usuario)        
                                    else:
                                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                        movi(venta.interno_zacapa,'Proforma',venta.tienda,d.id_prod.pk,d.id_prod.nombre,d.id_prod.stock,d.cantidad,0,\
                                        (d.id_prod.stock+d.cantidad),'Descartado Proforma',venta.usuario)       
                                else:
                                    if venta.tienda == 'Estanzuela':
                                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                        movi(venta.interno_estanzuela,'Proforma',venta.tienda,d.id_prod.pk,d.id_prod.nombre,d.id_prod.stock,d.cantidad,0,\
                                        (d.id_prod.stock+idetalle.cantidad),'Descartado Proforma',venta.usuario)
                                    elif venta.tienda == 'Teculutan':
                                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                        movi(venta.interno_tecu,'Proforma',venta.tienda,d.id_prod.pk,d.id_prod.nombre,d.id_prod.stock,d.cantidad,0,\
                                        (d.id_prod.stock+d.cantidad),'Descartado Proforma',venta.usuario)
                                    elif venta.tienda == 'Santa Cruz':
                                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                        movi(venta.interno_rio_hondo,'Proforma',venta.tienda,d.id_prod.pk,d.id_prod.nombre,d.id_prod.stock,d.cantidad,0,\
                                        (d.id_prod.stock+d.cantidad),'Descartado Proforma',venta.usuario)  
                                    elif venta.tienda == 'Gualan':
                                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                        movi(venta.interno_gualan,'Proforma',venta.tienda,d.id_prod.pk,d.id_prod.nombre,d.id_prod.stock,d.cantidad,0,\
                                        (d.id_prod.stock+d.cantidad),'Descartado Proforma',venta.usuario)      
                                    else:
                                        # docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
                                        movi(venta.interno_zacapa,'Proforma',venta.tienda,d.id_prod.pk,d.id_prod.nombre,d.id_prod.stock,d.cantidad,0,\
                                        (d.id_prod.stock+d.cantidad),'Descartado Proforma',venta.usuario)      

                    if miventa.tipo == "FEL":
                        midetalle.delete()
                        miventa.delete()
                        messages.warning(
                            request, f'Venta FEL Descartada Productos Vuelven a Inventario!')
                        return redirect('NuevaVenta')

                    elif miventa.tipo == "FEL-Servicio":
                        midetalle.delete()
                        miventa.delete()
                        messages.warning(
                            request, f'Venta FEL-Servicio Descartada!')
                        return redirect('NuevaVenta')

                    elif miventa.tipo == "PROFORMA":
                        midetalle.delete()
                        miventa.delete()
                        messages.warning(
                            request, f'Venta Descartada Productos Vuelven a Inventario!')
                        return redirect('NuevaVenta')

                    elif miventa.tipo == "NOTA CREDITO":
                        midetalle.delete()
                        miventa.delete()
                        messages.warning(
                            request, f'Venta Descartada Productos Vuelven a Inventario!')
                        return redirect('NuevaVenta')

                    else:
                        midetalle.delete()
                        miventa.delete()
                else:

                    if miventa.tipo == 'PROFORMA':
                        pass
                    else:
                        for d in midetalle:
                            # lista productos
                            prod = Producto.objects.get(id=d.id_prod.id)
                            Producto.objects.filter(id=d.id_prod.id).update(
                                stock=prod.stock+d.cantidad, salio=prod.salio-d.cantidad)

                    if miventa.tipo == "FEL":
                        midetalle.delete()
                        miventa.delete()
                        messages.warning(
                            request, f'Venta FEL Descartada Productos Vuelven a Inventario!')
                        return redirect('NuevaVenta')

                    elif miventa.tipo == "FEL-Servicio":
                        midetalle.delete()
                        miventa.delete()
                        messages.warning(
                            request, f'Venta FEL-Servicio Descartada!')
                        return redirect('NuevaVenta')

                    elif miventa.tipo == "PROFORMA":
                        midetalle.delete()
                        miventa.delete()
                        messages.warning(
                            request, f'Venta Descartada Productos Vuelven a Inventario!')
                        return redirect('NuevaVenta')

                    elif miventa.tipo == "NOTA CREDITO":
                        midetalle.delete()
                        miventa.delete()
                        messages.warning(
                            request, f'Venta Descartada Productos Vuelven a Inventario!')
                        return redirect('NuevaVenta')

                    else:
                        midetalle.delete()
                        miventa.delete()

        else:
            messages.error(
                request, f'Venta Numero {venta.factura} Cancelada Por Falta de Verificacion!')
            Venta.objects.filter(token=t).update(
                estado=99, tipo="Venta Corrupta")
            return redirect('NuevaVenta')

    return render(request, 'Venta/detalle.html', {'venta': venta, 'tok': tok, 'd': det})


def clientenoexiste(nit, nombre, dir, t):

    venta = Venta.objects.get(token=t)
    if venta.tipo == "NOTA CREDITO":
        c = Cliente()
        c.nit = nit
        c.nombre = nombre
        c.direccion = dir
        c.tel = '0000-0000'
        c.compras_contado = 0
        c.total_contado = 0.00
        c.compras_credito = 1
        c.total_credito = venta.total
        c.total_credito_pagado = 0.00
        c.fecha = str(datetime.today().strftime('%Y-%m-%d'))
        c.usuario = User.objects.get(id=venta.usuario.id)
        c.save()
    else:
        c = Cliente()
        c.nit = nit
        c.nombre = nombre
        c.direccion = dir
        c.tel = '0000-0000'
        c.compras_contado = 1
        c.total_contado = venta.total
        c.compras_credito = 0
        c.total_credito = 0.00
        c.total_credito_pagado = 0.00
        c.fecha = str(datetime.today().strftime('%Y-%m-%d'))
        c.usuario = User.objects.get(id=venta.usuario.id)
        c.save()


def pdf(request, f):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        response = HttpResponse(content_type='application/pdf')
        response[
            'Content-Disposition'] = f'attachment; filename="comprobante-venta-#-{f}.pdf"'
        r = Comprobante(f)
        response.write(r.run())
        return response
        


def pdfproformaservicio(request,v,d,c,p,t,cli):
        response = HttpResponse(content_type='application/pdf')
        response[
            'Content-Disposition'] = f'attachment; filename="Proforma-Servicio-venta-#-{v}.pdf"'
        r = ProformaServicio(v,d,c,p,t,cli)
        response.write(r.run())
        return response       


################# FEL #############################

cont = 0

### ESTANZUELA ###


def fel(request, t):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        factura = Venta.objects.filter(token=t)
        factura_ver = Venta.objects.get(token=t)
        detalle = Detalle.objects.filter(token=t)
        cd = Detalle.objects.filter(token=t).aggregate(
            cantidad=Sum('cantidad'))  # total de la venta
        total = Detalle.objects.filter(token=t).aggregate(
            tot=Sum('total'))  # total de la venta
        miventa = Detalle.objects.filter(token=t).aggregate(
            t=Sum('total'))  # total de la venta
        for f in factura:
            pass

        for d in detalle:
            pass

            Venta.objects.filter(token=t).update(
                nit=request.POST["nit"], nombre=request.POST["nombre"], direccion=request.POST["direccion"], estado=1)
            # miventa = Venta.objects.filter(factura=id).values_list('nit','direccion','negocio')
            # facturar(request,id)

            # crear el DTE a Certificar

            dte_fel_a_certificar = InfileFel.fel_dte()  # aki mandamos el correlativo

            # crear el emisor
            emisor_fel = emisor.emisor()

            # crear el receptor
            receptor_fel = receptor.receptor()

            # crear los totales
            total_fel = InfileFel.totales()

            # totales impuestos
            totales_impuestos = InfileFel.total_impuesto()

            for datoscliente in Venta.objects.filter(token=t):
                print(datoscliente.nit, datoscliente.nombre,
                      datoscliente.direccion, t)

            clientenoexiste(datoscliente.nit,
                            datoscliente.nombre, datoscliente.direccion, t)

            # setear direccion del emisor
            emisor_fel.set_direccion('KILOMETRO 141 5 RUTA A ESQUIPULAS ZONA 4 ESTANZUELA ZACAPA \n',
                                     '19001', 'TEL.', '5774-7328', 'GT')  # Cabecera de Negocio

            # setear datos del emisor fel
            # Parametros:  afiliacion_iva, codigo_establecimiento, correo_emisor, nit_emisor, nombre_comercial, nombre_emisor
            emisor_fel.set_datos_emisor('GEN', '1', '<EMAIL>', '96638303',
                                        f'CORPORACION SANTA ROSALIA SOCIEDAD ANONIMA', 'SERVICIO AGRICOLA SANTA ROSALIA')  # Cabecera de Negocio

            # setear direccion del receptor
            receptor_fel.set_direccion(
                datoscliente.direccion, '19001', 'Zacapa', 'guatemala', 'GT')

            # setear datos del receptor
            receptor_fel.set_datos_receptor(
                '<EMAIL>', datoscliente.nit, datoscliente.nombre)

            # identificador unico del dte del cliente
            dte_fel_a_certificar.set_clave_unica(f'202132{id}')  # no importa

            # setear datos generales
            dte_fel_a_certificar.set_datos_generales(
                'GTQ', f'{datoscliente.fecha}T00:00:00-06:00', 'FACT')  # fecha de venta

            # agregar los datos del emisor
            dte_fel_a_certificar.set_datos_emisor(emisor_fel)
            # agregar los datos del receptor
            dte_fel_a_certificar.set_datos_receptor(receptor_fel)

            # agregar las frases
            dte_fel_a_certificar.frase_fel.set_frase('1', '1')
            dte_fel_a_certificar.frase_fel.set_frase('1', '2')
            # dte_fel_a_certificar.frase_fel.set_frase('1','1','20185687029123456789','2018-10-11')

            num = 1
            acutotal = 0
            acuiva = 0
            miventa = 0
            for item in Detalle.objects.filter(token=t):

                num = num+1

                item_1 = InfileFel.item()

                item_1_impuesto = InfileFel.impuesto()

                # llenar el item con los datos necesarios
                item_1.set_numero_linea(num)  # esto falta
                item_1.set_bien_o_servicio('B')
                item_1.set_cantidad(item.cantidad)
                item_1.set_unidad_medida('UND')  # no se puede cambiar
                item_1.set_descripcion(item.id_prod.nombre)
                item_1.set_precio_unitario(item.precio)
                item_1.set_precio(item.cantidad*item.precio)
                item_1.set_descuento(0)
                item_1.set_total(item.total)

                grav = round((item.total/112)*100, 2)
                iva = round((grav*12)/100, 2)

                # llenar los impuestos del item
                item_1_impuesto.set_monto_impuesto(iva)
                item_1_impuesto.set_monto_gravable(grav)
                item_1_impuesto.set_codigo_unidad_gravable(1)
                item_1_impuesto.set_nombre_corto('IVA')
                item_1.set_impuesto(item_1_impuesto)

                acutotal = acutotal + grav
                acuiva = acuiva + iva
                miventa = miventa + item.total

                # Agregar el item 1
                dte_fel_a_certificar.agregar_item(item_1)

                # agregar el gran total

            total_fel.set_gran_total(miventa)

            # agregar datos del gran total de impuestos

            totales_impuestos.set_nombre_corto('IVA')
            totales_impuestos.set_total_monto_impuesto(acuiva)

            total_fel.set_total_impuestos(totales_impuestos)

            # agregar los totales
            dte_fel_a_certificar.agregar_totales(total_fel)

            # agregar adendas al gusto
            fel_adenda = InfileFel.adenda()
            fel_adenda.nombre = "Perso1"
            fel_adenda.valor = f"{factura_ver.interno_estanzuela}.1"
            dte_fel_a_certificar.agregar_adenda(fel_adenda)
            print("#########################################################")

            # contingencia
            # dte_fel_a_certificar.set_acceso('123123')

            # agregando el tipo de personeria
            # dte_fel_a_certificar.set_tipo_personeria('2')

            # realizar el llamado a la certificada
            certificacion_fel = dte_fel_a_certificar.certificar()
            if (certificacion_fel["resultado"]):
                print("UUID:" + certificacion_fel["uuid"])
                print("FECHA:" + certificacion_fel["fecha"])
                print("SERIE:" + certificacion_fel["serie"])
                print("numero:" + str(certificacion_fel["numero"]))
                total = Detalle.objects.all().filter(token=t).aggregate(
                    tot=Sum('total'))  # total de la venta
                messages.info(
                    request, f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}")
                Venta.objects.filter(token=t).update(nit=request.POST["nit"], nombre=request.POST["nombre"], direccion=request.POST["direccion"], fecha_fel=str(
                    certificacion_fel['fecha']), estado=1, link=f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}", anula=certificacion_fel['uuid'], serie=certificacion_fel['serie'], numero=certificacion_fel['numero'], total=total['tot'])
                # Venta.objects.filter(factura=id).update(link=f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}")
                # messages.info(request,factura_ver.link)
                return redirect('NuevaVenta')

            else:
                print("No pudo ser certificada")
                print("Descripcion: " + certificacion_fel["descripcion"])

                for error_fel in certificacion_fel["descripcion_errores"]:
                    print("Mensaje Error: " + error_fel["fuente"])
                    print("fuente: " + error_fel["mensaje_error"])
                    print("categoria: " + error_fel["categoria"])
                    print("numeral: " + error_fel["numeral"])
                    print("validacion: " + error_fel["validacion"])

        else:
            pass

        ###################################################################################################################################

    # return render(request,"PagoApp/pagoefectivo.html",{'factura':factura,'detalle':detalle,'cd':cd,'total':total,'id':id})


def fel2(request, t):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        factura = Venta.objects.filter(token=t)
        factura_ver = Venta.objects.get(token=t)
        detalle = Detalle.objects.filter(token=t)
        cd = Detalle.objects.filter(token=t).aggregate(
            cantidad=Sum('cantidad'))  # total de la venta
        total = Detalle.objects.filter(token=t).aggregate(
            tot=Sum('total'))  # total de la venta
        miventa = Detalle.objects.filter(token=t).aggregate(
            t=Sum('total'))  # total de la venta
        for f in factura:
            pass

        for d in detalle:
            pass

            Venta.objects.filter(token=t).update(
                nit=request.POST["nit"], nombre=request.POST["nombre"], direccion=request.POST["direccion"], estado=1)
            # miventa = Venta.objects.filter(factura=id).values_list('nit','direccion','negocio')
            # facturar(request,id)

            # crear el DTE a Certificar

            dte_fel_a_certificar = InfileFel.fel_dte()  # aki mandamos el correlativo

            # crear el emisor
            emisor_fel = emisor.emisor()

            # crear el receptor
            receptor_fel = receptor.receptor()

            # crear los totales
            total_fel = InfileFel.totales()

            # totales impuestos
            totales_impuestos = InfileFel.total_impuesto()

            for datoscliente in Venta.objects.filter(token=t):
                print(datoscliente.nit, datoscliente.nombre,
                      datoscliente.direccion, t)

            clientenoexiste(datoscliente.nit,
                            datoscliente.nombre, datoscliente.direccion, t)

            # setear direccion del emisor
            emisor_fel.set_direccion('BARRIO EL CALVARIO ZONA 1 TECULUTAN \n',
                                     '19001', 'TEL.', '5053-8711', 'GT')  # Cabecera de Negocio

            # setear datos del emisor fel
            # Parametros:  afiliacion_iva, codigo_establecimiento, correo_emisor, nit_emisor, nombre_comercial, nombre_emisor
            emisor_fel.set_datos_emisor('GEN', '2', '<EMAIL>', '96638303',
                                        f'CORPORACION SANTA ROSALIA SOCIEDAD ANONIMA', 'SANTA ROSALIA MOTOS')  # Cabecera de Negocio

            # setear direccion del receptor
            receptor_fel.set_direccion(
                datoscliente.direccion, '19001', 'Zacapa', 'guatemala', 'GT')

            # setear datos del receptor
            receptor_fel.set_datos_receptor(
                '<EMAIL>', datoscliente.nit, datoscliente.nombre)

            # identificador unico del dte del cliente
            dte_fel_a_certificar.set_clave_unica(f'202032{id}')  # no importa

            # setear datos generales
            dte_fel_a_certificar.set_datos_generales(
                'GTQ', f'{datoscliente.fecha}T00:00:00-06:00', 'FACT')  # fecha de venta

            # agregar los datos del emisor
            dte_fel_a_certificar.set_datos_emisor(emisor_fel)
            # agregar los datos del receptor
            dte_fel_a_certificar.set_datos_receptor(receptor_fel)

            # agregar las frases escenario y tipo
            dte_fel_a_certificar.frase_fel.set_frase('1', '1')
            dte_fel_a_certificar.frase_fel.set_frase('1', '2')
            # dte_fel_a_certificar.frase_fel.set_frase('1','1','20185687029123456789','2018-10-11')

            num = 1
            acutotal = 0
            acuiva = 0
            miventa = 0
            for item in Detalle.objects.filter(token=t):

                num = num+1

                item_1 = InfileFel.item()

                item_1_impuesto = InfileFel.impuesto()

                # llenar el item con los datos necesarios
                item_1.set_numero_linea(num)  # esto falta
                item_1.set_bien_o_servicio('B')
                item_1.set_cantidad(item.cantidad)
                item_1.set_unidad_medida('UND')  # no se puede cambiar
                item_1.set_descripcion(item.id_prod.nombre)
                item_1.set_precio_unitario(item.precio)
                item_1.set_precio(item.cantidad*item.precio)
                item_1.set_descuento(0)
                item_1.set_total(item.total)

                grav = round((item.total/112)*100, 2)
                iva = round((grav*12)/100, 2)

                # llenar los impuestos del item
                item_1_impuesto.set_monto_impuesto(iva)
                item_1_impuesto.set_monto_gravable(grav)
                item_1_impuesto.set_codigo_unidad_gravable(1)
                item_1_impuesto.set_nombre_corto('IVA')
                item_1.set_impuesto(item_1_impuesto)

                acutotal = acutotal + grav
                acuiva = acuiva + iva
                miventa = miventa + item.total

                # Agregar el item 1
                dte_fel_a_certificar.agregar_item(item_1)

                # agregar el gran total

            total_fel.set_gran_total(miventa)

            # agregar datos del gran total de impuestos

            totales_impuestos.set_nombre_corto('IVA')
            totales_impuestos.set_total_monto_impuesto(acuiva)

            total_fel.set_total_impuestos(totales_impuestos)

            # agregar los totales
            dte_fel_a_certificar.agregar_totales(total_fel)

            # agregar adendas al gusto
            fel_adenda = InfileFel.adenda()
            fel_adenda.nombre = "Perso1"
            fel_adenda.valor = f"{factura_ver.interno_tecu}.2"
            dte_fel_a_certificar.agregar_adenda(fel_adenda)
            print("#########################################################")

            # contingencia
            # dte_fel_a_certificar.set_acceso('123123')

            # agregando el tipo de personeria
            # dte_fel_a_certificar.set_tipo_personeria('2')

            # realizar el llamado a la certificada
            certificacion_fel = dte_fel_a_certificar.certificar()
            if (certificacion_fel["resultado"]):
                print("UUID:" + certificacion_fel["uuid"])
                print("FECHA:" + certificacion_fel["fecha"])
                print("SERIE:" + certificacion_fel["serie"])
                print("numero:" + str(certificacion_fel["numero"]))
                total = Detalle.objects.all().filter(token=t).aggregate(
                    tot=Sum('total'))  # total de la venta
                messages.info(
                    request, f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}")
                Venta.objects.filter(token=t).update(nit=request.POST["nit"], nombre=request.POST["nombre"], direccion=request.POST["direccion"], fecha_fel=str(
                    certificacion_fel['fecha']), estado=1, link=f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}", anula=certificacion_fel['uuid'], serie=certificacion_fel['serie'], numero=certificacion_fel['numero'], total=total['tot'])
                # Venta.objects.filter(factura=id).update(link=f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}")
                # messages.info(request,factura_ver.link)
                return redirect('NuevaVenta')

            else:
                print("No pudo ser certificada")
                print("Descripcion: " + certificacion_fel["descripcion"])

                for error_fel in certificacion_fel["descripcion_errores"]:
                    print("Mensaje Error: " + error_fel["fuente"])
                    print("fuente: " + error_fel["mensaje_error"])
                    print("categoria: " + error_fel["categoria"])
                    print("numeral: " + error_fel["numeral"])
                    print("validacion: " + error_fel["validacion"])

        else:
            pass

        ###################################################################################################################################

    # return render(request,"PagoApp/pagoefectivo.html",{'factura':factura,'detalle':detalle,'cd':cd,'total':total,'id':id})


def fel3(request, t):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        factura = Venta.objects.filter(token=t)
        factura_ver = Venta.objects.get(token=t)
        detalle = Detalle.objects.filter(token=t)
        cd = Detalle.objects.filter(token=t).aggregate(
            cantidad=Sum('cantidad'))  # total de la venta
        total = Detalle.objects.filter(token=t).aggregate(
            tot=Sum('total'))  # total de la venta
        miventa = Detalle.objects.filter(token=t).aggregate(
            t=Sum('total'))  # total de la venta
        for f in factura:
            pass

        for d in detalle:
            pass

            Venta.objects.filter(token=t).update(
                nit=request.POST["nit"], nombre=request.POST["nombre"], direccion=request.POST["direccion"], estado=1)
            # miventa = Venta.objects.filter(factura=id).values_list('nit','direccion','negocio')
            # facturar(request,id)

            # crear el DTE a Certificar

            dte_fel_a_certificar = InfileFel.fel_dte()  # aki mandamos el correlativo

            # crear el emisor
            emisor_fel = emisor.emisor()

            # crear el receptor
            receptor_fel = receptor.receptor()

            # crear los totales
            total_fel = InfileFel.totales()

            # totales impuestos
            totales_impuestos = InfileFel.total_impuesto()

            for datoscliente in Venta.objects.filter(token=t):
                print(datoscliente.nit, datoscliente.nombre,
                      datoscliente.direccion, t)

            clientenoexiste(datoscliente.nit,
                            datoscliente.nombre, datoscliente.direccion, t)

            # setear direccion del emisor
            emisor_fel.set_direccion('BARRIO LA ESTACION LOCAL 2 ZACAPA \n',
                                     '19001', 'TEL.', '3040-8780', 'GT')  # Cabecera de Negocio

            # setear datos del emisor fel
            # Parametros:  afiliacion_iva, codigo_establecimiento, correo_emisor, nit_emisor, nombre_comercial, nombre_emisor
            emisor_fel.set_datos_emisor('GEN', '3', '<EMAIL>', '96638303',
                                        f'CORPORACION SANTA ROSALIA SOCIEDAD ANONIMA', 'SANTA ROSALIA MOTOS')  # Cabecera de Negocio

            # setear direccion del receptor
            receptor_fel.set_direccion(
                datoscliente.direccion, '19001', 'Zacapa', 'guatemala', 'GT')

            # setear datos del receptor
            receptor_fel.set_datos_receptor(
                '<EMAIL>', datoscliente.nit, datoscliente.nombre)

            # identificador unico del dte del cliente
            dte_fel_a_certificar.set_clave_unica(f'202032{id}')  # no importa

            # setear datos generales
            dte_fel_a_certificar.set_datos_generales(
                'GTQ', f'{datoscliente.fecha}T00:00:00-06:00', 'FACT')  # fecha de venta

            # agregar los datos del emisor
            dte_fel_a_certificar.set_datos_emisor(emisor_fel)
            # agregar los datos del receptor
            dte_fel_a_certificar.set_datos_receptor(receptor_fel)

            # agregar las frases
            dte_fel_a_certificar.frase_fel.set_frase('1', '1')
            dte_fel_a_certificar.frase_fel.set_frase('1', '2')
            # dte_fel_a_certificar.frase_fel.set_frase('1','1','20185687029123456789','2018-10-11')

            num = 1
            acutotal = 0
            acuiva = 0
            miventa = 0
            for item in Detalle.objects.filter(token=t):

                num = num+1

                item_1 = InfileFel.item()

                item_1_impuesto = InfileFel.impuesto()

                # llenar el item con los datos necesarios
                item_1.set_numero_linea(num)  # esto falta
                item_1.set_bien_o_servicio('B')
                item_1.set_cantidad(item.cantidad)
                item_1.set_unidad_medida('UND')  # no se puede cambiar
                item_1.set_descripcion(item.id_prod.nombre)
                item_1.set_precio_unitario(item.precio)
                item_1.set_precio(item.cantidad*item.precio)
                item_1.set_descuento(0)
                item_1.set_total(item.total)

                grav = round((item.total/112)*100, 2)
                iva = round((grav*12)/100, 2)

                # llenar los impuestos del item
                item_1_impuesto.set_monto_impuesto(iva)
                item_1_impuesto.set_monto_gravable(grav)
                item_1_impuesto.set_codigo_unidad_gravable(1)
                item_1_impuesto.set_nombre_corto('IVA')
                item_1.set_impuesto(item_1_impuesto)

                acutotal = acutotal + grav
                acuiva = acuiva + iva
                miventa = miventa + item.total

                # Agregar el item 1
                dte_fel_a_certificar.agregar_item(item_1)

                # agregar el gran total

            total_fel.set_gran_total(miventa)

            # agregar datos del gran total de impuestos

            totales_impuestos.set_nombre_corto('IVA')
            totales_impuestos.set_total_monto_impuesto(acuiva)

            total_fel.set_total_impuestos(totales_impuestos)

            # agregar los totales
            dte_fel_a_certificar.agregar_totales(total_fel)

            # agregar adendas al gusto
            fel_adenda = InfileFel.adenda()
            fel_adenda.nombre = "Perso1"
            fel_adenda.valor = f"{factura_ver.interno_zacapa}.3"
            dte_fel_a_certificar.agregar_adenda(fel_adenda)
            print("#########################################################")

            # contingencia
            # dte_fel_a_certificar.set_acceso('123123')

            # agregando el tipo de personeria
            # dte_fel_a_certificar.set_tipo_personeria('2')

            # realizar el llamado a la certificada
            certificacion_fel = dte_fel_a_certificar.certificar()
            if (certificacion_fel["resultado"]):
                print("UUID:" + certificacion_fel["uuid"])
                print("FECHA:" + certificacion_fel["fecha"])
                print("SERIE:" + certificacion_fel["serie"])
                print("numero:" + str(certificacion_fel["numero"]))
                total = Detalle.objects.all().filter(token=t).aggregate(
                    tot=Sum('total'))  # total de la venta
                messages.info(
                    request, f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}")
                Venta.objects.filter(token=t).update(nit=request.POST["nit"], nombre=request.POST["nombre"], direccion=request.POST["direccion"], fecha_fel=str(
                    certificacion_fel['fecha']), estado=1, link=f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}", anula=certificacion_fel['uuid'], serie=certificacion_fel['serie'], numero=certificacion_fel['numero'], total=total['tot'])
                # Venta.objects.filter(factura=id).update(link=f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}")
                # messages.info(request,factura_ver.link)
                return redirect('NuevaVenta')

            else:
                print("No pudo ser certificada")
                print("Descripcion: " + certificacion_fel["descripcion"])

                for error_fel in certificacion_fel["descripcion_errores"]:
                    print("Mensaje Error: " + error_fel["fuente"])
                    print("fuente: " + error_fel["mensaje_error"])
                    print("categoria: " + error_fel["categoria"])
                    print("numeral: " + error_fel["numeral"])
                    print("validacion: " + error_fel["validacion"])

        else:
            pass

        ###################################################################################################################################

    # return render(request,"PagoApp/pagoefectivo.html",{'factura':factura,'detalle':detalle,'cd':cd,'total':total,'id':id})


### fel servicio ###


### ESTANZUELA ###
def fel4(request, t):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        factura = Venta.objects.filter(token=t)
        factura_ver = Venta.objects.get(token=t)
        detalle = Detalle.objects.filter(token=t)
        cd = Detalle.objects.filter(token=t).aggregate(
            cantidad=Sum('cantidad'))  # total de la venta
        total = Detalle.objects.filter(token=t).aggregate(
            tot=Sum('total'))  # total de la venta
        miventa = Detalle.objects.filter(token=t).aggregate(
            t=Sum('total'))  # total de la venta
        for f in factura:
            pass

        for d in detalle:
            pass

            Venta.objects.filter(token=t).update(
                nit=request.POST["nit"], nombre=request.POST["nombre"], direccion=request.POST["direccion"], estado=1)
            # miventa = Venta.objects.filter(factura=id).values_list('nit','direccion','negocio')
            # facturar(request,id)

            # crear el DTE a Certificar

            dte_fel_a_certificar = InfileFel.fel_dte()  # aki mandamos el correlativo

            # crear el emisor
            emisor_fel = emisor.emisor()

            # crear el receptor
            receptor_fel = receptor.receptor()

            # crear los totales
            total_fel = InfileFel.totales()

            # totales impuestos
            totales_impuestos = InfileFel.total_impuesto()

            for datoscliente in Venta.objects.filter(token=t):
                print(datoscliente.nit, datoscliente.nombre,
                      datoscliente.direccion, t)

            clientenoexiste(datoscliente.nit,
                            datoscliente.nombre, datoscliente.direccion, t)

            # setear direccion del emisor
            emisor_fel.set_direccion('KILOMETRO 141 5 RUTA A ESQUIPULAS ZONA 4 ESTANZUELA ZACAPA \n',
                                     '19001', 'TEL.', '5774-7328', 'GT')  # Cabecera de Negocio

            # setear datos del emisor fel
            # Parametros:  afiliacion_iva, codigo_establecimiento, correo_emisor, nit_emisor, nombre_comercial, nombre_emisor
            emisor_fel.set_datos_emisor('GEN', '1', '<EMAIL>', '96638303',
                                        f'CORPORACION SANTA ROSALIA SOCIEDAD ANONIMA', 'SERVICIO AGRICOLA SANTA ROSALIA')  # Cabecera de Negocio

            # setear direccion del receptor
            receptor_fel.set_direccion(
                datoscliente.direccion, '19001', 'Zacapa', 'guatemala', 'GT')

            # setear datos del receptor
            receptor_fel.set_datos_receptor(
                '<EMAIL>', datoscliente.nit, datoscliente.nombre)

            # identificador unico del dte del cliente
            dte_fel_a_certificar.set_clave_unica(f'202132{id}')  # no importa

            # setear datos generales
            dte_fel_a_certificar.set_datos_generales(
                'GTQ', f'{datoscliente.fecha}T00:00:00-06:00', 'FACT')  # fecha de venta

            # agregar los datos del emisor
            dte_fel_a_certificar.set_datos_emisor(emisor_fel)
            # agregar los datos del receptor
            dte_fel_a_certificar.set_datos_receptor(receptor_fel)

            # agregar las frases
            dte_fel_a_certificar.frase_fel.set_frase('1', '1')
            dte_fel_a_certificar.frase_fel.set_frase('1', '2')
            # dte_fel_a_certificar.frase_fel.set_frase('1','1','20185687029123456789','2018-10-11')

            num = 1
            acutotal = 0
            acuiva = 0
            miventa = 0
            for item in Detalle.objects.filter(token=t):

                num = num+1

                item_1 = InfileFel.item()

                item_1_impuesto = InfileFel.impuesto()

                # llenar el item con los datos necesarios
                item_1.set_numero_linea(num)  # esto falta
                item_1.set_bien_o_servicio('B')
                item_1.set_cantidad(item.cantidad)
                item_1.set_unidad_medida('UND')  # no se puede cambiar
                item_1.set_descripcion(item.detalle_ser)
                item_1.set_precio_unitario(item.precio)
                item_1.set_precio(item.cantidad*item.precio)
                item_1.set_descuento(0)
                item_1.set_total(item.total)

                grav = round((item.total/112)*100, 2)
                iva = round((grav*12)/100, 2)

                # llenar los impuestos del item
                item_1_impuesto.set_monto_impuesto(iva)
                item_1_impuesto.set_monto_gravable(grav)
                item_1_impuesto.set_codigo_unidad_gravable(1)
                item_1_impuesto.set_nombre_corto('IVA')
                item_1.set_impuesto(item_1_impuesto)

                acutotal = acutotal + grav
                acuiva = acuiva + iva
                miventa = miventa + item.total

                # Agregar el item 1
                dte_fel_a_certificar.agregar_item(item_1)

                # agregar el gran total

            total_fel.set_gran_total(miventa)

            # agregar datos del gran total de impuestos

            totales_impuestos.set_nombre_corto('IVA')
            totales_impuestos.set_total_monto_impuesto(acuiva)

            total_fel.set_total_impuestos(totales_impuestos)

            # agregar los totales
            dte_fel_a_certificar.agregar_totales(total_fel)

            # agregar adendas al gusto
            fel_adenda = InfileFel.adenda()
            fel_adenda.nombre = "Perso1"
            fel_adenda.valor = f"{factura_ver.interno_estanzuela}.1"
            dte_fel_a_certificar.agregar_adenda(fel_adenda)
            print("#########################################################")

            # contingencia
            # dte_fel_a_certificar.set_acceso('123123')

            # agregando el tipo de personeria
            # dte_fel_a_certificar.set_tipo_personeria('2')

            # realizar el llamado a la certificada
            certificacion_fel = dte_fel_a_certificar.certificar()
            if (certificacion_fel["resultado"]):
                print("UUID:" + certificacion_fel["uuid"])
                print("FECHA:" + certificacion_fel["fecha"])
                print("SERIE:" + certificacion_fel["serie"])
                print("numero:" + str(certificacion_fel["numero"]))
                total = Detalle.objects.all().filter(token=t).aggregate(
                    tot=Sum('total'))  # total de la venta
                messages.info(
                    request, f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}")
                Venta.objects.filter(token=t).update(nit=request.POST["nit"], nombre=request.POST["nombre"], direccion=request.POST["direccion"], fecha_fel=str(
                    certificacion_fel['fecha']), estado=1, link=f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}", anula=certificacion_fel['uuid'], serie=certificacion_fel['serie'], numero=certificacion_fel['numero'], total=total['tot'])
                # Venta.objects.filter(factura=id).update(link=f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}")
                # messages.info(request,factura_ver.link)
                return redirect('NuevaVenta')

            else:
                print("No pudo ser certificada")
                print("Descripcion: " + certificacion_fel["descripcion"])

                for error_fel in certificacion_fel["descripcion_errores"]:
                    print("Mensaje Error: " + error_fel["fuente"])
                    print("fuente: " + error_fel["mensaje_error"])
                    print("categoria: " + error_fel["categoria"])
                    print("numeral: " + error_fel["numeral"])
                    print("validacion: " + error_fel["validacion"])

        else:
            pass

        ###################################################################################################################################

    # return render(request,"PagoApp/pagoefectivo.html",{'factura':factura,'detalle':detalle,'cd':cd,'total':total,'id':id})


## TECU ##
def fel5(request, t):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        factura = Venta.objects.filter(token=t)
        factura_ver = Venta.objects.get(token=t)
        detalle = Detalle.objects.filter(token=t)
        cd = Detalle.objects.filter(token=t).aggregate(
            cantidad=Sum('cantidad'))  # total de la venta
        total = Detalle.objects.filter(token=t).aggregate(
            tot=Sum('total'))  # total de la venta
        miventa = Detalle.objects.filter(token=t).aggregate(
            t=Sum('total'))  # total de la venta
        for f in factura:
            pass

        for d in detalle:
            pass

            Venta.objects.filter(token=t).update(
                nit=request.POST["nit"], nombre=request.POST["nombre"], direccion=request.POST["direccion"], estado=1)
            # miventa = Venta.objects.filter(factura=id).values_list('nit','direccion','negocio')
            # facturar(request,id)

            # crear el DTE a Certificar

            dte_fel_a_certificar = InfileFel.fel_dte()  # aki mandamos el correlativo

            # crear el emisor
            emisor_fel = emisor.emisor()

            # crear el receptor
            receptor_fel = receptor.receptor()

            # crear los totales
            total_fel = InfileFel.totales()

            # totales impuestos
            totales_impuestos = InfileFel.total_impuesto()

            for datoscliente in Venta.objects.filter(token=t):
                print(datoscliente.nit, datoscliente.nombre,
                      datoscliente.direccion, t)

            clientenoexiste(datoscliente.nit,
                            datoscliente.nombre, datoscliente.direccion, t)

            # setear direccion del emisor
            emisor_fel.set_direccion('BARRIO EL CALVARIO ZONA 1 TECULUTAN \n',
                                     '19001', 'TEL.', '5053-8711', 'GT')  # Cabecera de Negocio

            # setear datos del emisor fel
            # Parametros:  afiliacion_iva, codigo_establecimiento, correo_emisor, nit_emisor, nombre_comercial, nombre_emisor
            emisor_fel.set_datos_emisor('GEN', '2', '<EMAIL>', '96638303',
                                        f'CORPORACION SANTA ROSALIA SOCIEDAD ANONIMA', 'SANTA ROSALIA MOTOS')  # Cabecera de Negocio

            # setear direccion del receptor
            receptor_fel.set_direccion(
                datoscliente.direccion, '19001', 'Zacapa', 'guatemala', 'GT')

            # setear datos del receptor
            receptor_fel.set_datos_receptor(
                '<EMAIL>', datoscliente.nit, datoscliente.nombre)

            # identificador unico del dte del cliente
            dte_fel_a_certificar.set_clave_unica(f'202032{id}')  # no importa

            # setear datos generales
            dte_fel_a_certificar.set_datos_generales(
                'GTQ', f'{datoscliente.fecha}T00:00:00-06:00', 'FACT')  # fecha de venta

            # agregar los datos del emisor
            dte_fel_a_certificar.set_datos_emisor(emisor_fel)
            # agregar los datos del receptor
            dte_fel_a_certificar.set_datos_receptor(receptor_fel)

            # agregar las frases escenario y tipo
            dte_fel_a_certificar.frase_fel.set_frase('1', '1')
            dte_fel_a_certificar.frase_fel.set_frase('1', '2')
            # dte_fel_a_certificar.frase_fel.set_frase('1','1','20185687029123456789','2018-10-11')

            num = 1
            acutotal = 0
            acuiva = 0
            miventa = 0
            for item in Detalle.objects.filter(token=t):

                num = num+1

                item_1 = InfileFel.item()

                item_1_impuesto = InfileFel.impuesto()

                # llenar el item con los datos necesarios
                item_1.set_numero_linea(num)  # esto falta
                item_1.set_bien_o_servicio('B')
                item_1.set_cantidad(item.cantidad)
                item_1.set_unidad_medida('UND')  # no se puede cambiar
                item_1.set_descripcion(item.detalle_ser)
                item_1.set_precio_unitario(item.precio)
                item_1.set_precio(item.cantidad*item.precio)
                item_1.set_descuento(0)
                item_1.set_total(item.total)

                grav = round((item.total/112)*100, 2)
                iva = round((grav*12)/100, 2)

                # llenar los impuestos del item
                item_1_impuesto.set_monto_impuesto(iva)
                item_1_impuesto.set_monto_gravable(grav)
                item_1_impuesto.set_codigo_unidad_gravable(1)
                item_1_impuesto.set_nombre_corto('IVA')
                item_1.set_impuesto(item_1_impuesto)

                acutotal = acutotal + grav
                acuiva = acuiva + iva
                miventa = miventa + item.total

                # Agregar el item 1
                dte_fel_a_certificar.agregar_item(item_1)

                # agregar el gran total

            total_fel.set_gran_total(miventa)

            # agregar datos del gran total de impuestos

            totales_impuestos.set_nombre_corto('IVA')
            totales_impuestos.set_total_monto_impuesto(acuiva)

            total_fel.set_total_impuestos(totales_impuestos)

            # agregar los totales
            dte_fel_a_certificar.agregar_totales(total_fel)

            # agregar adendas al gusto
            fel_adenda = InfileFel.adenda()
            fel_adenda.nombre = "Perso1"
            fel_adenda.valor = f"{factura_ver.interno_tecu}.2"
            dte_fel_a_certificar.agregar_adenda(fel_adenda)
            print("#########################################################")

            # contingencia
            # dte_fel_a_certificar.set_acceso('123123')

            # agregando el tipo de personeria
            # dte_fel_a_certificar.set_tipo_personeria('2')

            # realizar el llamado a la certificada
            certificacion_fel = dte_fel_a_certificar.certificar()
            if (certificacion_fel["resultado"]):
                print("UUID:" + certificacion_fel["uuid"])
                print("FECHA:" + certificacion_fel["fecha"])
                print("SERIE:" + certificacion_fel["serie"])
                print("numero:" + str(certificacion_fel["numero"]))
                total = Detalle.objects.all().filter(token=t).aggregate(
                    tot=Sum('total'))  # total de la venta
                messages.info(
                    request, f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}")
                Venta.objects.filter(token=t).update(nit=request.POST["nit"], nombre=request.POST["nombre"], direccion=request.POST["direccion"], fecha_fel=str(
                    certificacion_fel['fecha']), estado=1, link=f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}", anula=certificacion_fel['uuid'], serie=certificacion_fel['serie'], numero=certificacion_fel['numero'], total=total['tot'])
                # Venta.objects.filter(factura=id).update(link=f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}")
                # messages.info(request,factura_ver.link)
                return redirect('NuevaVenta')

            else:
                print("No pudo ser certificada")
                print("Descripcion: " + certificacion_fel["descripcion"])

                for error_fel in certificacion_fel["descripcion_errores"]:
                    print("Mensaje Error: " + error_fel["fuente"])
                    print("fuente: " + error_fel["mensaje_error"])
                    print("categoria: " + error_fel["categoria"])
                    print("numeral: " + error_fel["numeral"])
                    print("validacion: " + error_fel["validacion"])

        else:
            pass

        ###################################################################################################################################

    # return render(request,"PagoApp/pagoefectivo.html",{'factura':factura,'detalle':detalle,'cd':cd,'total':total,'id':id})


def fel6(request, t):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        factura = Venta.objects.filter(token=t)
        factura_ver = Venta.objects.get(token=t)
        detalle = Detalle.objects.filter(token=t)
        cd = Detalle.objects.filter(token=t).aggregate(
            cantidad=Sum('cantidad'))  # total de la venta
        total = Detalle.objects.filter(token=t).aggregate(
            tot=Sum('total'))  # total de la venta
        miventa = Detalle.objects.filter(token=t).aggregate(
            t=Sum('total'))  # total de la venta
        for f in factura:
            pass

        for d in detalle:
            pass

            Venta.objects.filter(token=t).update(
                nit=request.POST["nit"], nombre=request.POST["nombre"], direccion=request.POST["direccion"], estado=1)
            # miventa = Venta.objects.filter(factura=id).values_list('nit','direccion','negocio')
            # facturar(request,id)

            # crear el DTE a Certificar

            dte_fel_a_certificar = InfileFel.fel_dte()  # aki mandamos el correlativo

            # crear el emisor
            emisor_fel = emisor.emisor()

            # crear el receptor
            receptor_fel = receptor.receptor()

            # crear los totales
            total_fel = InfileFel.totales()

            # totales impuestos
            totales_impuestos = InfileFel.total_impuesto()

            for datoscliente in Venta.objects.filter(token=t):
                print(datoscliente.nit, datoscliente.nombre,
                      datoscliente.direccion, t)

            clientenoexiste(datoscliente.nit,
                            datoscliente.nombre, datoscliente.direccion, t)

            # setear direccion del emisor
            emisor_fel.set_direccion('BARRIO LA ESTACION LOCAL 2 ZACAPA \n',
                                     '19001', 'TEL.', '3040-8780', 'GT')  # Cabecera de Negocio

            # setear datos del emisor fel
            # Parametros:  afiliacion_iva, codigo_establecimiento, correo_emisor, nit_emisor, nombre_comercial, nombre_emisor
            emisor_fel.set_datos_emisor('GEN', '3', '<EMAIL>', '96638303',
                                        f'CORPORACION SANTA ROSALIA SOCIEDAD ANONIMA', 'SANTA ROSALIA MOTOS')  # Cabecera de Negocio

            # setear direccion del receptor
            receptor_fel.set_direccion(
                datoscliente.direccion, '19001', 'Zacapa', 'guatemala', 'GT')

            # setear datos del receptor
            receptor_fel.set_datos_receptor(
                '<EMAIL>', datoscliente.nit, datoscliente.nombre)

            # identificador unico del dte del cliente
            dte_fel_a_certificar.set_clave_unica(f'202032{id}')  # no importa

            # setear datos generales
            dte_fel_a_certificar.set_datos_generales(
                'GTQ', f'{datoscliente.fecha}T00:00:00-06:00', 'FACT')  # fecha de venta

            # agregar los datos del emisor
            dte_fel_a_certificar.set_datos_emisor(emisor_fel)
            # agregar los datos del receptor
            dte_fel_a_certificar.set_datos_receptor(receptor_fel)

            # agregar las frases
            dte_fel_a_certificar.frase_fel.set_frase('1', '1')
            dte_fel_a_certificar.frase_fel.set_frase('1', '2')
            # dte_fel_a_certificar.frase_fel.set_frase('1','1','20185687029123456789','2018-10-11')

            num = 1
            acutotal = 0
            acuiva = 0
            miventa = 0
            for item in Detalle.objects.filter(token=t):

                num = num+1

                item_1 = InfileFel.item()

                item_1_impuesto = InfileFel.impuesto()

                # llenar el item con los datos necesarios
                item_1.set_numero_linea(num)  # esto falta
                item_1.set_bien_o_servicio('B')
                item_1.set_cantidad(item.cantidad)
                item_1.set_unidad_medida('UND')  # no se puede cambiar
                item_1.set_descripcion(item.detalle_ser)
                item_1.set_precio_unitario(item.precio)
                item_1.set_precio(item.cantidad*item.precio)
                item_1.set_descuento(0)
                item_1.set_total(item.total)

                grav = round((item.total/112)*100, 2)
                iva = round((grav*12)/100, 2)

                # llenar los impuestos del item
                item_1_impuesto.set_monto_impuesto(iva)
                item_1_impuesto.set_monto_gravable(grav)
                item_1_impuesto.set_codigo_unidad_gravable(1)
                item_1_impuesto.set_nombre_corto('IVA')
                item_1.set_impuesto(item_1_impuesto)

                acutotal = acutotal + grav
                acuiva = acuiva + iva
                miventa = miventa + item.total

                # Agregar el item 1
                dte_fel_a_certificar.agregar_item(item_1)

                # agregar el gran total

            total_fel.set_gran_total(miventa)

            # agregar datos del gran total de impuestos

            totales_impuestos.set_nombre_corto('IVA')
            totales_impuestos.set_total_monto_impuesto(acuiva)

            total_fel.set_total_impuestos(totales_impuestos)

            # agregar los totales
            dte_fel_a_certificar.agregar_totales(total_fel)

            # agregar adendas al gusto
            fel_adenda = InfileFel.adenda()
            fel_adenda.nombre = "Perso1"
            fel_adenda.valor = f"{factura_ver.interno_zacapa}.3"
            dte_fel_a_certificar.agregar_adenda(fel_adenda)
            print("#########################################################")

            # contingencia
            # dte_fel_a_certificar.set_acceso('123123')

            # agregando el tipo de personeria
            # dte_fel_a_certificar.set_tipo_personeria('2')

            # realizar el llamado a la certificada
            certificacion_fel = dte_fel_a_certificar.certificar()
            if (certificacion_fel["resultado"]):
                print("UUID:" + certificacion_fel["uuid"])
                print("FECHA:" + certificacion_fel["fecha"])
                print("SERIE:" + certificacion_fel["serie"])
                print("numero:" + str(certificacion_fel["numero"]))
                total = Detalle.objects.all().filter(token=t).aggregate(
                    tot=Sum('total'))  # total de la venta
                messages.info(
                    request, f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}")
                Venta.objects.filter(token=t).update(nit=request.POST["nit"], nombre=request.POST["nombre"], direccion=request.POST["direccion"], fecha_fel=str(
                    certificacion_fel['fecha']), estado=1, link=f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}", anula=certificacion_fel['uuid'], serie=certificacion_fel['serie'], numero=certificacion_fel['numero'], total=total['tot'])
                # Venta.objects.filter(factura=id).update(link=f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}")
                # messages.info(request,factura_ver.link)
                return redirect('NuevaVenta')

            else:
                print("No pudo ser certificada")
                print("Descripcion: " + certificacion_fel["descripcion"])

                for error_fel in certificacion_fel["descripcion_errores"]:
                    print("Mensaje Error: " + error_fel["fuente"])
                    print("fuente: " + error_fel["mensaje_error"])
                    print("categoria: " + error_fel["categoria"])
                    print("numeral: " + error_fel["numeral"])
                    print("validacion: " + error_fel["validacion"])

        else:
            pass

        ###################################################################################################################################

    # return render(request,"PagoApp/pagoefectivo.html",{'factura':factura,'detalle':detalle,'cd':cd,'total':total,'id':id})



### Santa Cruz ###


def fel7(request, t):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        factura = Venta.objects.filter(token=t)
        factura_ver = Venta.objects.get(token=t)
        detalle = Detalle.objects.filter(token=t)
        cd = Detalle.objects.filter(token=t).aggregate(
            cantidad=Sum('cantidad'))  # total de la venta
        total = Detalle.objects.filter(token=t).aggregate(
            tot=Sum('total'))  # total de la venta
        miventa = Detalle.objects.filter(token=t).aggregate(
            t=Sum('total'))  # total de la venta
        for f in factura:
            pass

        for d in detalle:
            pass

            Venta.objects.filter(token=t).update(
                nit=request.POST["nit"], nombre=request.POST["nombre"], direccion=request.POST["direccion"], estado=1)
            # miventa = Venta.objects.filter(factura=id).values_list('nit','direccion','negocio')
            # facturar(request,id)

            # crear el DTE a Certificar

            dte_fel_a_certificar = InfileFel.fel_dte()  # aki mandamos el correlativo

            # crear el emisor
            emisor_fel = emisor.emisor()

            # crear el receptor
            receptor_fel = receptor.receptor()

            # crear los totales
            total_fel = InfileFel.totales()

            # totales impuestos
            totales_impuestos = InfileFel.total_impuesto()

            for datoscliente in Venta.objects.filter(token=t):
                print(datoscliente.nit, datoscliente.nombre,
                      datoscliente.direccion, t)

            clientenoexiste(datoscliente.nit,
                            datoscliente.nombre, datoscliente.direccion, t)

            # setear direccion del emisor
            emisor_fel.set_direccion('RUTA AL ATLANTICO EL PEAJE ZONA 0 RIO HONDO, ZACAPA \n',
                                     '19001', 'TEL.', '5774-7328', 'GT')  # Cabecera de Negocio

            # setear datos del emisor fel
            # Parametros:  afiliacion_iva, codigo_establecimiento, correo_emisor, nit_emisor, nombre_comercial, nombre_emisor
            emisor_fel.set_datos_emisor('GEN', '4', '<EMAIL>', '96638303',
                                        f'CORPORACION SANTA ROSALIA SOCIEDAD ANONIMA', 'SERVICIO AGRICOLA SANTA ROSALIA')  # Cabecera de Negocio

            # setear direccion del receptor
            receptor_fel.set_direccion(
                datoscliente.direccion, '19001', 'Zacapa', 'guatemala', 'GT')

            # setear datos del receptor
            receptor_fel.set_datos_receptor(
                '<EMAIL>', datoscliente.nit, datoscliente.nombre)

            # identificador unico del dte del cliente
            dte_fel_a_certificar.set_clave_unica(f'202132{id}')  # no importa

            # setear datos generales
            dte_fel_a_certificar.set_datos_generales(
                'GTQ', f'{datoscliente.fecha}T00:00:00-06:00', 'FACT')  # fecha de venta

            # agregar los datos del emisor
            dte_fel_a_certificar.set_datos_emisor(emisor_fel)
            # agregar los datos del receptor
            dte_fel_a_certificar.set_datos_receptor(receptor_fel)

            # agregar las frases
            dte_fel_a_certificar.frase_fel.set_frase('1', '1')
            dte_fel_a_certificar.frase_fel.set_frase('1', '2')
            # dte_fel_a_certificar.frase_fel.set_frase('1','1','20185687029123456789','2018-10-11')

            num = 1
            acutotal = 0
            acuiva = 0
            miventa = 0
            for item in Detalle.objects.filter(token=t):

                num = num+1

                item_1 = InfileFel.item()

                item_1_impuesto = InfileFel.impuesto()

                # llenar el item con los datos necesarios
                item_1.set_numero_linea(num)  # esto falta
                item_1.set_bien_o_servicio('B')
                item_1.set_cantidad(item.cantidad)
                item_1.set_unidad_medida('UND')  # no se puede cambiar
                item_1.set_descripcion(item.id_prod.nombre)
                item_1.set_precio_unitario(item.precio)
                item_1.set_precio(item.cantidad*item.precio)
                item_1.set_descuento(0)
                item_1.set_total(item.total)

                grav = round((item.total/112)*100, 2)
                iva = round((grav*12)/100, 2)

                # llenar los impuestos del item
                item_1_impuesto.set_monto_impuesto(iva)
                item_1_impuesto.set_monto_gravable(grav)
                item_1_impuesto.set_codigo_unidad_gravable(1)
                item_1_impuesto.set_nombre_corto('IVA')
                item_1.set_impuesto(item_1_impuesto)

                acutotal = acutotal + grav
                acuiva = acuiva + iva
                miventa = miventa + item.total

                # Agregar el item 1
                dte_fel_a_certificar.agregar_item(item_1)

                # agregar el gran total

            total_fel.set_gran_total(miventa)

            # agregar datos del gran total de impuestos

            totales_impuestos.set_nombre_corto('IVA')
            totales_impuestos.set_total_monto_impuesto(acuiva)

            total_fel.set_total_impuestos(totales_impuestos)

            # agregar los totales
            dte_fel_a_certificar.agregar_totales(total_fel)

            # agregar adendas al gusto
            fel_adenda = InfileFel.adenda()
            fel_adenda.nombre = "Perso1"
            fel_adenda.valor = f"{factura_ver.interno_rio_hondo}.4"
            dte_fel_a_certificar.agregar_adenda(fel_adenda)
            print("#########################################################")

            # contingencia
            # dte_fel_a_certificar.set_acceso('123123')

            # agregando el tipo de personeria
            # dte_fel_a_certificar.set_tipo_personeria('2')

            # realizar el llamado a la certificada
            certificacion_fel = dte_fel_a_certificar.certificar()
            if (certificacion_fel["resultado"]):
                print("UUID:" + certificacion_fel["uuid"])
                print("FECHA:" + certificacion_fel["fecha"])
                print("SERIE:" + certificacion_fel["serie"])
                print("numero:" + str(certificacion_fel["numero"]))
                total = Detalle.objects.all().filter(token=t).aggregate(
                    tot=Sum('total'))  # total de la venta
                messages.info(
                    request, f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}")
                Venta.objects.filter(token=t).update(nit=request.POST["nit"], nombre=request.POST["nombre"], direccion=request.POST["direccion"], fecha_fel=str(
                    certificacion_fel['fecha']), estado=1, link=f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}", anula=certificacion_fel['uuid'], serie=certificacion_fel['serie'], numero=certificacion_fel['numero'], total=total['tot'])
                # Venta.objects.filter(factura=id).update(link=f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}")
                # messages.info(request,factura_ver.link)
                return redirect('NuevaVenta')

            else:
                print("No pudo ser certificada")
                print("Descripcion: " + certificacion_fel["descripcion"])

                for error_fel in certificacion_fel["descripcion_errores"]:
                    print("Mensaje Error: " + error_fel["fuente"])
                    print("fuente: " + error_fel["mensaje_error"])
                    print("categoria: " + error_fel["categoria"])
                    print("numeral: " + error_fel["numeral"])
                    print("validacion: " + error_fel["validacion"])

        else:
            pass

        ###################################################################################################################################




# FEL SERVICIOS


def fel8(request, t):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        factura = Venta.objects.filter(token=t)
        factura_ver = Venta.objects.get(token=t)
        detalle = Detalle.objects.filter(token=t)
        cd = Detalle.objects.filter(token=t).aggregate(
            cantidad=Sum('cantidad'))  # total de la venta
        total = Detalle.objects.filter(token=t).aggregate(
            tot=Sum('total'))  # total de la venta
        miventa = Detalle.objects.filter(token=t).aggregate(
            t=Sum('total'))  # total de la venta
        for f in factura:
            pass

        for d in detalle:
            pass

            Venta.objects.filter(token=t).update(
                nit=request.POST["nit"], nombre=request.POST["nombre"], direccion=request.POST["direccion"], estado=1)
            # miventa = Venta.objects.filter(factura=id).values_list('nit','direccion','negocio')
            # facturar(request,id)

            # crear el DTE a Certificar

            dte_fel_a_certificar = InfileFel.fel_dte()  # aki mandamos el correlativo

            # crear el emisor
            emisor_fel = emisor.emisor()

            # crear el receptor
            receptor_fel = receptor.receptor()

            # crear los totales
            total_fel = InfileFel.totales()

            # totales impuestos
            totales_impuestos = InfileFel.total_impuesto()

            for datoscliente in Venta.objects.filter(token=t):
                print(datoscliente.nit, datoscliente.nombre,
                      datoscliente.direccion, t)

            clientenoexiste(datoscliente.nit,
                            datoscliente.nombre, datoscliente.direccion, t)

            # setear direccion del emisor
            emisor_fel.set_direccion('RUTA AL ATLANTICO EL PEAJE ZONA 0 RIO HONDO, ZACAPA \n',
                                     '19001', 'TEL.', '5774-7328', 'GT')  # Cabecera de Negocio

            # setear datos del emisor fel
            # Parametros:  afiliacion_iva, codigo_establecimiento, correo_emisor, nit_emisor, nombre_comercial, nombre_emisor
            emisor_fel.set_datos_emisor('GEN', '4', '<EMAIL>', '96638303',
                                        f'CORPORACION SANTA ROSALIA SOCIEDAD ANONIMA', 'SERVICIO AGRICOLA SANTA ROSALIA')  # Cabecera de Negocio

            # setear direccion del receptor
            receptor_fel.set_direccion(
                datoscliente.direccion, '19001', 'Zacapa', 'guatemala', 'GT')

            # setear datos del receptor
            receptor_fel.set_datos_receptor(
                '<EMAIL>', datoscliente.nit, datoscliente.nombre)

            # identificador unico del dte del cliente
            dte_fel_a_certificar.set_clave_unica(f'202032{id}')  # no importa

            # setear datos generales
            dte_fel_a_certificar.set_datos_generales(
                'GTQ', f'{datoscliente.fecha}T00:00:00-06:00', 'FACT')  # fecha de venta

            # agregar los datos del emisor
            dte_fel_a_certificar.set_datos_emisor(emisor_fel)
            # agregar los datos del receptor
            dte_fel_a_certificar.set_datos_receptor(receptor_fel)

            # agregar las frases
            dte_fel_a_certificar.frase_fel.set_frase('1', '1')
            dte_fel_a_certificar.frase_fel.set_frase('1', '2')
            # dte_fel_a_certificar.frase_fel.set_frase('1','1','20185687029123456789','2018-10-11')

            num = 1
            acutotal = 0
            acuiva = 0
            miventa = 0
            for item in Detalle.objects.filter(token=t):

                num = num+1

                item_1 = InfileFel.item()

                item_1_impuesto = InfileFel.impuesto()

                # llenar el item con los datos necesarios
                item_1.set_numero_linea(num)  # esto falta
                item_1.set_bien_o_servicio('B')
                item_1.set_cantidad(item.cantidad)
                item_1.set_unidad_medida('UND')  # no se puede cambiar
                item_1.set_descripcion(item.detalle_ser)
                item_1.set_precio_unitario(item.precio)
                item_1.set_precio(item.cantidad*item.precio)
                item_1.set_descuento(0)
                item_1.set_total(item.total)

                grav = round((item.total/112)*100, 2)
                iva = round((grav*12)/100, 2)

                # llenar los impuestos del item
                item_1_impuesto.set_monto_impuesto(iva)
                item_1_impuesto.set_monto_gravable(grav)
                item_1_impuesto.set_codigo_unidad_gravable(1)
                item_1_impuesto.set_nombre_corto('IVA')
                item_1.set_impuesto(item_1_impuesto)

                acutotal = acutotal + grav
                acuiva = acuiva + iva
                miventa = miventa + item.total

                # Agregar el item 1
                dte_fel_a_certificar.agregar_item(item_1)

                # agregar el gran total

            total_fel.set_gran_total(miventa)

            # agregar datos del gran total de impuestos

            totales_impuestos.set_nombre_corto('IVA')
            totales_impuestos.set_total_monto_impuesto(acuiva)

            total_fel.set_total_impuestos(totales_impuestos)

            # agregar los totales
            dte_fel_a_certificar.agregar_totales(total_fel)

            # agregar adendas al gusto
            fel_adenda = InfileFel.adenda()
            fel_adenda.nombre = "Perso1"
            fel_adenda.valor = f"{factura_ver.interno_rio_hondo}.4"
            dte_fel_a_certificar.agregar_adenda(fel_adenda)
            print("#########################################################")

            # contingencia
            # dte_fel_a_certificar.set_acceso('123123')

            # agregando el tipo de personeria
            # dte_fel_a_certificar.set_tipo_personeria('2')

            # realizar el llamado a la certificada
            certificacion_fel = dte_fel_a_certificar.certificar()
            if (certificacion_fel["resultado"]):
                print("UUID:" + certificacion_fel["uuid"])
                print("FECHA:" + certificacion_fel["fecha"])
                print("SERIE:" + certificacion_fel["serie"])
                print("numero:" + str(certificacion_fel["numero"]))
                total = Detalle.objects.all().filter(token=t).aggregate(
                    tot=Sum('total'))  # total de la venta
                messages.info(
                    request, f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}")
                Venta.objects.filter(token=t).update(nit=request.POST["nit"], nombre=request.POST["nombre"], direccion=request.POST["direccion"], fecha_fel=str(
                    certificacion_fel['fecha']), estado=1, link=f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}", anula=certificacion_fel['uuid'], serie=certificacion_fel['serie'], numero=certificacion_fel['numero'], total=total['tot'])
                # Venta.objects.filter(factura=id).update(link=f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}")
                # messages.info(request,factura_ver.link)
                return redirect('NuevaVenta')

            else:
                print("No pudo ser certificada")
                print("Descripcion: " + certificacion_fel["descripcion"])

                for error_fel in certificacion_fel["descripcion_errores"]:
                    print("Mensaje Error: " + error_fel["fuente"])
                    print("fuente: " + error_fel["mensaje_error"])
                    print("categoria: " + error_fel["categoria"])
                    print("numeral: " + error_fel["numeral"])
                    print("validacion: " + error_fel["validacion"])

        else:
            pass

        ###################################################################################################################################



# FIN FEL Santa Cruz 



### Gualan ###


def fel9(request, t):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        factura = Venta.objects.filter(token=t)
        factura_ver = Venta.objects.get(token=t)
        detalle = Detalle.objects.filter(token=t)
        cd = Detalle.objects.filter(token=t).aggregate(
            cantidad=Sum('cantidad'))  # total de la venta
        total = Detalle.objects.filter(token=t).aggregate(
            tot=Sum('total'))  # total de la venta
        miventa = Detalle.objects.filter(token=t).aggregate(
            t=Sum('total'))  # total de la venta
        for f in factura:
            pass

        for d in detalle:
            pass

            Venta.objects.filter(token=t).update(
                nit=request.POST["nit"], nombre=request.POST["nombre"], direccion=request.POST["direccion"], estado=1)
            # miventa = Venta.objects.filter(factura=id).values_list('nit','direccion','negocio')
            # facturar(request,id)

            # crear el DTE a Certificar

            dte_fel_a_certificar = InfileFel.fel_dte()  # aki mandamos el correlativo

            # crear el emisor
            emisor_fel = emisor.emisor()

            # crear el receptor
            receptor_fel = receptor.receptor()

            # crear los totales
            total_fel = InfileFel.totales()

            # totales impuestos
            totales_impuestos = InfileFel.total_impuesto()

            for datoscliente in Venta.objects.filter(token=t):
                print(datoscliente.nit, datoscliente.nombre,
                      datoscliente.direccion, t)

            clientenoexiste(datoscliente.nit,
                            datoscliente.nombre, datoscliente.direccion, t)

            # setear direccion del emisor
            emisor_fel.set_direccion('ALDEA MAYUELA RUTA AL ATLANTICO ZONA 0 GUALAN, ZACAPA \n',
                                     '19001', 'TEL.', '5774-7328', 'GT')  # Cabecera de Negocio

            # setear datos del emisor fel cambiar el numero de establecimiento
            # Parametros:  afiliacion_iva, codigo_establecimiento, correo_emisor, nit_emisor, nombre_comercial, nombre_emisor
            emisor_fel.set_datos_emisor('GEN', '5', '<EMAIL>', '96638303',
                                        f'CORPORACION SANTA ROSALIA SOCIEDAD ANONIMA', 'SANTA ROSALIA MOTOS')  # Cabecera de Negocio

            # setear direccion del receptor
            receptor_fel.set_direccion(
                datoscliente.direccion, '19001', 'Zacapa', 'guatemala', 'GT')

            # setear datos del receptor
            receptor_fel.set_datos_receptor(
                '<EMAIL>', datoscliente.nit, datoscliente.nombre)

            # identificador unico del dte del cliente
            dte_fel_a_certificar.set_clave_unica(f'202132{id}')  # no importa

            # setear datos generales
            dte_fel_a_certificar.set_datos_generales(
                'GTQ', f'{datoscliente.fecha}T00:00:00-06:00', 'FACT')  # fecha de venta

            # agregar los datos del emisor
            dte_fel_a_certificar.set_datos_emisor(emisor_fel)
            # agregar los datos del receptor
            dte_fel_a_certificar.set_datos_receptor(receptor_fel)

            # agregar las frases
            dte_fel_a_certificar.frase_fel.set_frase('1', '1')
            dte_fel_a_certificar.frase_fel.set_frase('1', '2')
            # dte_fel_a_certificar.frase_fel.set_frase('1','1','20185687029123456789','2018-10-11')

            num = 1
            acutotal = 0
            acuiva = 0
            miventa = 0
            for item in Detalle.objects.filter(token=t):

                num = num+1

                item_1 = InfileFel.item()

                item_1_impuesto = InfileFel.impuesto()

                # llenar el item con los datos necesarios
                item_1.set_numero_linea(num)  # esto falta
                item_1.set_bien_o_servicio('B')
                item_1.set_cantidad(item.cantidad)
                item_1.set_unidad_medida('UND')  # no se puede cambiar
                item_1.set_descripcion(item.id_prod.nombre)
                item_1.set_precio_unitario(item.precio)
                item_1.set_precio(item.cantidad*item.precio)
                item_1.set_descuento(0)
                item_1.set_total(item.total)

                grav = round((item.total/112)*100, 2)
                iva = round((grav*12)/100, 2)

                # llenar los impuestos del item
                item_1_impuesto.set_monto_impuesto(iva)
                item_1_impuesto.set_monto_gravable(grav)
                item_1_impuesto.set_codigo_unidad_gravable(1)
                item_1_impuesto.set_nombre_corto('IVA')
                item_1.set_impuesto(item_1_impuesto)

                acutotal = acutotal + grav
                acuiva = acuiva + iva
                miventa = miventa + item.total

                # Agregar el item 1
                dte_fel_a_certificar.agregar_item(item_1)

                # agregar el gran total

            total_fel.set_gran_total(miventa)

            # agregar datos del gran total de impuestos

            totales_impuestos.set_nombre_corto('IVA')
            totales_impuestos.set_total_monto_impuesto(acuiva)

            total_fel.set_total_impuestos(totales_impuestos)

            # agregar los totales
            dte_fel_a_certificar.agregar_totales(total_fel)

            # agregar adendas al gusto
            fel_adenda = InfileFel.adenda()
            fel_adenda.nombre = "Perso1"
            fel_adenda.valor = f"{factura_ver.interno_gualan}.5"
            dte_fel_a_certificar.agregar_adenda(fel_adenda)
            print("#########################################################")

            # contingencia
            # dte_fel_a_certificar.set_acceso('123123')

            # agregando el tipo de personeria
            # dte_fel_a_certificar.set_tipo_personeria('2')

            # realizar el llamado a la certificada
            certificacion_fel = dte_fel_a_certificar.certificar()
            if (certificacion_fel["resultado"]):
                print("UUID:" + certificacion_fel["uuid"])
                print("FECHA:" + certificacion_fel["fecha"])
                print("SERIE:" + certificacion_fel["serie"])
                print("numero:" + str(certificacion_fel["numero"]))
                total = Detalle.objects.all().filter(token=t).aggregate(
                    tot=Sum('total'))  # total de la venta
                messages.info(
                    request, f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}")
                Venta.objects.filter(token=t).update(nit=request.POST["nit"], nombre=request.POST["nombre"], direccion=request.POST["direccion"], fecha_fel=str(
                    certificacion_fel['fecha']), estado=1, link=f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}", anula=certificacion_fel['uuid'], serie=certificacion_fel['serie'], numero=certificacion_fel['numero'], total=total['tot'])
                # Venta.objects.filter(factura=id).update(link=f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}")
                # messages.info(request,factura_ver.link)
                return redirect('NuevaVenta')

            else:
                print("No pudo ser certificada")
                print("Descripcion: " + certificacion_fel["descripcion"])

                for error_fel in certificacion_fel["descripcion_errores"]:
                    print("Mensaje Error: " + error_fel["fuente"])
                    print("fuente: " + error_fel["mensaje_error"])
                    print("categoria: " + error_fel["categoria"])
                    print("numeral: " + error_fel["numeral"])
                    print("validacion: " + error_fel["validacion"])

        else:
            pass

        ###################################################################################################################################




# FEL SERVICIOS


def fel10(request, t):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        factura = Venta.objects.filter(token=t)
        factura_ver = Venta.objects.get(token=t)
        detalle = Detalle.objects.filter(token=t)
        cd = Detalle.objects.filter(token=t).aggregate(
            cantidad=Sum('cantidad'))  # total de la venta
        total = Detalle.objects.filter(token=t).aggregate(
            tot=Sum('total'))  # total de la venta
        miventa = Detalle.objects.filter(token=t).aggregate(
            t=Sum('total'))  # total de la venta
        for f in factura:
            pass

        for d in detalle:
            pass

            Venta.objects.filter(token=t).update(
                nit=request.POST["nit"], nombre=request.POST["nombre"], direccion=request.POST["direccion"], estado=1)
            # miventa = Venta.objects.filter(factura=id).values_list('nit','direccion','negocio')
            # facturar(request,id)

            # crear el DTE a Certificar

            dte_fel_a_certificar = InfileFel.fel_dte()  # aki mandamos el correlativo

            # crear el emisor
            emisor_fel = emisor.emisor()

            # crear el receptor
            receptor_fel = receptor.receptor()

            # crear los totales
            total_fel = InfileFel.totales()

            # totales impuestos
            totales_impuestos = InfileFel.total_impuesto()

            for datoscliente in Venta.objects.filter(token=t):
                print(datoscliente.nit, datoscliente.nombre,
                      datoscliente.direccion, t)

            clientenoexiste(datoscliente.nit,
                            datoscliente.nombre, datoscliente.direccion, t)

             # setear direccion del emisor
            emisor_fel.set_direccion('ALDEA MAYUELA RUTA AL ATLANTICO ZONA 0 GUALAN, ZACAPA \n',
                                     '19001', 'TEL.', '5774-7328', 'GT')  # Cabecera de Negocio

            # setear datos del emisor fel cambiar el numero de establecimiento
            # Parametros:  afiliacion_iva, codigo_establecimiento, correo_emisor, nit_emisor, nombre_comercial, nombre_emisor
            emisor_fel.set_datos_emisor('GEN', '5', '<EMAIL>', '96638303',
                                        f'CORPORACION SANTA ROSALIA SOCIEDAD ANONIMA', 'SANTA ROSALIA MOTOS')  # Cabecera de Negocio

            # setear direccion del receptor
            receptor_fel.set_direccion(
                datoscliente.direccion, '19001', 'Zacapa', 'guatemala', 'GT')

            # setear datos del receptor
            receptor_fel.set_datos_receptor(
                '<EMAIL>', datoscliente.nit, datoscliente.nombre)

            # identificador unico del dte del cliente
            dte_fel_a_certificar.set_clave_unica(f'202032{id}')  # no importa

            # setear datos generales
            dte_fel_a_certificar.set_datos_generales(
                'GTQ', f'{datoscliente.fecha}T00:00:00-06:00', 'FACT')  # fecha de venta

            # agregar los datos del emisor
            dte_fel_a_certificar.set_datos_emisor(emisor_fel)
            # agregar los datos del receptor
            dte_fel_a_certificar.set_datos_receptor(receptor_fel)

            # agregar las frases
            dte_fel_a_certificar.frase_fel.set_frase('1', '1')
            dte_fel_a_certificar.frase_fel.set_frase('1', '2')
            # dte_fel_a_certificar.frase_fel.set_frase('1','1','20185687029123456789','2018-10-11')

            num = 1
            acutotal = 0
            acuiva = 0
            miventa = 0
            for item in Detalle.objects.filter(token=t):

                num = num+1

                item_1 = InfileFel.item()

                item_1_impuesto = InfileFel.impuesto()

                # llenar el item con los datos necesarios
                item_1.set_numero_linea(num)  # esto falta
                item_1.set_bien_o_servicio('B')
                item_1.set_cantidad(item.cantidad)
                item_1.set_unidad_medida('UND')  # no se puede cambiar
                item_1.set_descripcion(item.detalle_ser)
                item_1.set_precio_unitario(item.precio)
                item_1.set_precio(item.cantidad*item.precio)
                item_1.set_descuento(0)
                item_1.set_total(item.total)

                grav = round((item.total/112)*100, 2)
                iva = round((grav*12)/100, 2)

                # llenar los impuestos del item
                item_1_impuesto.set_monto_impuesto(iva)
                item_1_impuesto.set_monto_gravable(grav)
                item_1_impuesto.set_codigo_unidad_gravable(1)
                item_1_impuesto.set_nombre_corto('IVA')
                item_1.set_impuesto(item_1_impuesto)

                acutotal = acutotal + grav
                acuiva = acuiva + iva
                miventa = miventa + item.total

                # Agregar el item 1
                dte_fel_a_certificar.agregar_item(item_1)

                # agregar el gran total

            total_fel.set_gran_total(miventa)

            # agregar datos del gran total de impuestos

            totales_impuestos.set_nombre_corto('IVA')
            totales_impuestos.set_total_monto_impuesto(acuiva)

            total_fel.set_total_impuestos(totales_impuestos)

            # agregar los totales
            dte_fel_a_certificar.agregar_totales(total_fel)

            # agregar adendas al gusto
            fel_adenda = InfileFel.adenda()
            fel_adenda.nombre = "Perso1"
            fel_adenda.valor = f"{factura_ver.interno_gualan}.5"
            dte_fel_a_certificar.agregar_adenda(fel_adenda)
            print("#########################################################")

            # contingencia
            # dte_fel_a_certificar.set_acceso('123123')

            # agregando el tipo de personeria
            # dte_fel_a_certificar.set_tipo_personeria('2')

            # realizar el llamado a la certificada
            certificacion_fel = dte_fel_a_certificar.certificar()
            if (certificacion_fel["resultado"]):
                print("UUID:" + certificacion_fel["uuid"])
                print("FECHA:" + certificacion_fel["fecha"])
                print("SERIE:" + certificacion_fel["serie"])
                print("numero:" + str(certificacion_fel["numero"]))
                total = Detalle.objects.all().filter(token=t).aggregate(
                    tot=Sum('total'))  # total de la venta
                messages.info(
                    request, f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}")
                Venta.objects.filter(token=t).update(nit=request.POST["nit"], nombre=request.POST["nombre"], direccion=request.POST["direccion"], fecha_fel=str(
                    certificacion_fel['fecha']), estado=1, link=f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}", anula=certificacion_fel['uuid'], serie=certificacion_fel['serie'], numero=certificacion_fel['numero'], total=total['tot'])
                # Venta.objects.filter(factura=id).update(link=f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}")
                # messages.info(request,factura_ver.link)
                return redirect('NuevaVenta')

            else:
                print("No pudo ser certificada")
                print("Descripcion: " + certificacion_fel["descripcion"])

                for error_fel in certificacion_fel["descripcion_errores"]:
                    print("Mensaje Error: " + error_fel["fuente"])
                    print("fuente: " + error_fel["mensaje_error"])
                    print("categoria: " + error_fel["categoria"])
                    print("numeral: " + error_fel["numeral"])
                    print("validacion: " + error_fel["validacion"])

        else:
            pass

        ###################################################################################################################################



# FIN FEL Gualan 







def anularfel(request, t):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        for datoscliente in Venta.objects.filter(token=t):
            print(datoscliente.nit, datoscliente.nombre, datoscliente.direccion)

        dte_fel_a_anular = InfileFel.fel_dte()

        # realizar el llamado a la certificada
        certificacion_fel = dte_fel_a_anular.anular(str(datoscliente.fecha_fel), '96638303', str(
            datoscliente.fecha_fel), datoscliente.nit, datoscliente.anula, datoscliente.serie)
        if (certificacion_fel["resultado"]):
            print("UUID:" + certificacion_fel["uuid"])
            print("FECHA:" + certificacion_fel["fecha"])
            print("SERIE:" + certificacion_fel["serie"])
            print("numero:" + str(certificacion_fel["numero"]))
        else:
            print("No pudo ser certificada")
            print("Descripcion: " + certificacion_fel["descripcion"])

            for error_fel in certificacion_fel["descripcion_errores"]:
                print("Mensaje Error: " + error_fel["fuente"])
                print("fuente: " + error_fel["mensaje_error"])
                print("categoria: " + error_fel["categoria"])
                print("numeral: " + error_fel["numeral"])
                print("validacion: " + error_fel["validacion"])

        total = Detalle.objects.all().filter(token=t).aggregate(
            tot=Sum('total'))  # total de la venta
        Venta.objects.filter(token=t).update(
            fecha_fel=datoscliente.fecha_fel, estado=2, total=total['tot'])
        anular(request, datoscliente.factura)
        messages.success(request, 'Anulacion FEL Exitosa')
        return redirect('ListaVenta')


def bitacora(id, prod, t, d, h, i, s, hy, td, u):

    b = Bitacora()
    b.id_prod = id
    b.prod = prod
    b.tipo = t
    b.doc = d
    b.habia = h
    b.ingreso = i
    b.salio = s
    b.hay = hy
    b.tienda = td
    b.fecha = str(datetime.today())
    b.usuario = u
    b.save()






# movimientos
# docs,tipo,tienda,id_prod,prod,ultimo,entrada,salida,actual,obs,fecha,usuario
def movi(d,tp,td,id,prod,u,e,s,ac,obs,us):

    m = Movimientos()
    m.doc = d
    m.tipo = tp
    m.tienda = td
    m.id_prod = id
    m.prod = prod
    m.ultimo = u
    m.entrada = e
    m.salida = s
    m.actual = ac
    m.obs = obs
    m.fecha = datetime.today()
    m.usuario = User.objects.get(id=us.id)
    m.save()


# fin movimientos