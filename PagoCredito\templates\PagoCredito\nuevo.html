{% extends 'Base/base.html' %}
{% block title %}Nuevo Pago Abono Credito{% endblock %}

{% block content %}

{% if messages %}
{% for message in messages %}

<script>
    Swal.fire({
        "title": "Informacion Sistema",
        "text": "Pago Realizado!",
        "html": "<a href='{% url 'PDFCOMPROBANTE' message %}'' class='btn btn-danger'>Comprobante</a>",
        "icon": "{{message.tags}}"
    })
</script>

{% endfor %}
{% endif %}

<div class="content-wrapper">

    <div class="container-xxl flex-grow-1 container-p-y">


        <!-- Basic Layout -->
        <div class="col-md-12">
            <div class="card md-6">
                <div class="card-header d-flex align-items-center justify-content-between">
                    <h5 class="mb-0">Formulario Nuevo Pago Abono</h5>
                    <small class="text-muted float-end">Pago Abono</small>
                </div>
                <div class="card-body">
                    <form action="#" method="POST" enctype="multipart/form-data">{% csrf_token %}
                        <div class="row">
                            <div class="col-md-4">
                                <label>Numero Factura</label>
                                <input type="text" name="factura" class="form-control" required
                                    placeholder="Numero Factura" autofocus="factura" value="{{f}}" readonly>
                            </div>
                            <div class="col-md-4">
                                <label>Tipo de Pago</label>
                                {{form.tipo}}
                            </div>
                            <div class="col-md-4">
                                <label>Cantidad Abonado</label>
                                {{form.abono}}
                            </div>
                        </div><br>
                        <div class="row">
                            <div class="row">
                                <div class="col-md-4">
                                    <label>Adeudado</label>
                                    <input type="text" class="form-control" readonly value="{{v}}" />
                                </div>
                                <div class="col-md-4">
                                    <label>Fecha</label>
                                    <input type="text" class="form-control" readonly value="{% now 'd-m-Y' %}" />
                                </div>
                                <div class="col-md-4">
                                    <label>Usuario</label>
                                    <input type="text" class="form-control" readonly value="{{user.username}}" />
                                </div>
                            </div><br>

                            <div class="row">
                                <div class="col-md-4"><br>
                                    <button type="submit" class="btn btn-primary">Aplicar</button>
                                    <button type="reset" class="btn btn-danger">Cancelar</button>
                                </div>
                                <div class="col-md-4"></div><br>
                                <div class="col-md-4" align="right"><br>
                                    <a href="{% url 'ListaPagoPendiente' %}" class="btn btn-info">Terminar</a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>


        </div>

    </div>



    {% endblock %}