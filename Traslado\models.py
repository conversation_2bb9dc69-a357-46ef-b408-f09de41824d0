from django.db import models
from Proveedor.models import Proveedor
from Producto.models import Producto
from user.models import User


class Traslado(models.Model):
    traslado = models.CharField(primary_key=True,max_length=250,blank=False,null=False)
    id_prov = models.ForeignKey(Proveedor,on_delete=models.CASCADE,blank=False,null=False)
    fecha = models.DateField(blank=False,null=False)
    usuario = models.ForeignKey(User,on_delete=models.CASCADE,blank=True,null=True)
    estado = models.IntegerField(blank=True,null=True,default=1)
    tienda = models.CharField(max_length=250,blank=True,null=True,default='Estanzuela')

    class Meta:
        ordering = ["-traslado"]

    def __str__(self):
        return self.traslado  
    

class DetalleTraslado(models.Model):
    traslado = models.ForeignKey(Traslado,on_delete=models.CASCADE,blank=False,null=False)
    id_prod = models.ForeignKey(Producto,on_delete=models.CASCADE,blank=False,null=False)
    cantidad = models.IntegerField(blank=False,null=False,default=0)
    stock_antes = models.IntegerField(blank=False,null=False,default=0)
    stock_ahora = models.IntegerField(blank=False,null=False,default=0)
    compra_antes = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False,default=0.00)
    venta_antes = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False,default=0.00)
    compra_ahora = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False,default=0.00)
    venta_ahora = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False,default=0.00)
    total = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False,default=0.00)
    fecha = models.DateTimeField(blank=False,null=False)
    usuario = models.ForeignKey(User,on_delete=models.CASCADE,blank=False,null=False)
    estado = models.IntegerField(blank=True,null=True,default=1)
    
    class Meta:
        ordering = ["-traslado"]

    def __str__(self):
        return self.traslado     
