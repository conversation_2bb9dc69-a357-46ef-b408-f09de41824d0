from io import BytesIO
from datetime import datetime
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib.styles import ParagraphStyle, TA_CENTER, TA_LEFT
from reportlab.lib.units import inch, mm, cm
from reportlab.lib import colors
from decimal import Decimal
from reportlab.platypus import (
    Paragraph,
    Table,
    SimpleDocTemplate,
    Spacer,
    TableStyle,
    Paragraph)
from reportlab.lib.styles import getSampleStyleSheet
from django.db.models import Sum
from reportlab.platypus import Image

# from .models import Persona
from Venta.models import Venta, Detalle
from Sucursal.models import Sucursal
from user.models import User



from reportlab.lib.pagesizes import A4
from reportlab.platypus import Image, Table, TableStyle
from reportlab.lib import colors
from reportlab.lib.styles import ParagraphStyle
from io import BytesIO
from datetime import datetime
from reportlab.platypus import Paragraph

class Comprobante:

    def __init__(self, f):
        self.buf = BytesIO()
        self.rastreo = Venta.objects.get(factura=f)
        self.usuario = User.objects.get(id=self.rastreo.usuario.id)
        self.vertienda = Sucursal.objects.get(ubicacion=self.rastreo.usuario.tienda)
        self.tienda = Sucursal.objects.get(id=self.vertienda.id)

    def run(self):
        self.doc = SimpleDocTemplate(
            self.buf, title=f"Comprobante {self.rastreo.factura}", pagesize=A4)
        self.story = []
        self.encabezado()
        self.crearTabla()
        self.doc.build(self.story, onFirstPage=self.numeroPagina, onLaterPages=self.numeroPagina)
        pdf = self.buf.getvalue()
        self.buf.close()
        return pdf

    def encabezado(self):
        # Obtener el tamaño de la página A4
        width, height = A4

        if self.usuario.tienda == 'Estanzuela':
            logo_path = 'Venta/estanzuela.jpg'
        elif self.usuario.tienda == 'Teculutan':
            logo_path = 'Venta/tecu.jpg'
        else:
            logo_path = 'Venta/zacapa.jpg'

        # Ajustar el tamaño de la imagen de acuerdo al ancho de la página (max 250px de ancho)
        imagen_logo = Image(logo_path, width=width * 0.6, height=(width * 0.6 * 85) / 250, hAlign='CENTER')

        p1 = Paragraph(f"TIENDA {self.usuario.tienda.upper()} ", self.estiloPC())
        a1 = Paragraph(f"Cliente: {self.rastreo.nombre}", self.estiloPC2())
        
        if self.usuario.tienda == 'Estanzuela':
            a111 = Paragraph(f"Proforma #  {self.rastreo.pro_estanzuela}", self.estiloPC2())
            a112 = Paragraph(f"Interno #  {self.rastreo.interno_estanzuela}", self.estiloPC2())
        elif self.usuario.tienda == 'Teculutan':
            a111 = Paragraph(f"Proforma #  {self.rastreo.pro_tecu}", self.estiloPC2())
            a112 = Paragraph(f"Interno #  {self.rastreo.interno_tecu}", self.estiloPC2())
        else:
            a111 = Paragraph(f"Proforma #  {self.rastreo.pro_zacapa}", self.estiloPC2())
            a112 = Paragraph(f"Interno #  {self.rastreo.interno_zacapa}", self.estiloPC2())

        a3 = Paragraph(f"Dirección: {self.rastreo.direccion}", self.estiloPC2())
        a4 = Paragraph(f"Fecha de Impresión: {datetime.today().strftime('%d/%m/%Y, %H:%M:%S')}", self.estiloPC3())

        # Agregar los elementos al documento
        self.story.append(imagen_logo)
        self.story.append(p1)
        self.story.append(a1)
        if self.rastreo.tipo == "PROFORMA-Servicio":
            self.story.append(a111)
        else:
            self.story.append(a112)
        self.story.append(a3)
        self.story.append(a4)
        self.story.append(Spacer(1, 0.2 * inch))

    def crearTabla(self):
        detalles = Detalle.objects.filter(factura=self.rastreo.factura)

        if detalles.exists():  # Verifica si existen detalles de venta
            data = [["Producto", "Cantidad", "Precio Uni", "SubTotal", "Descuento", "Total"]]

            for detalle in detalles:
                if self.rastreo.tipo == 'PROFORMA':
                    nombre_producto = detalle.id_prod.nombre
                else:
                    nombre_producto = detalle.detalle_ser

                # Aquí usamos Paragraph para que el texto se ajuste y la fila crezca si es necesario
                fila = [
                    Paragraph(nombre_producto, self.estiloCelda()),
                    detalle.cantidad,
                    f"Q.{detalle.precio:.2f}",
                    f"Q.{detalle.subtotal:.2f}",
                    f"Q.{detalle.descuento:.2f}",
                    f"Q.{detalle.total:.2f}"
                ]
                data.append(fila)

            # Agregar fila de total
            data.append(["Total de Venta", "", "", "", "", f"Q.{self.rastreo.total:.2f}"])

            # Configurar la tabla con el estilo adecuado
            table = Table(data, colWidths=[8 * cm, 2 * cm, 2 * cm, 2 * cm, 2 * cm, 2 * cm])

            table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), "CENTER"),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('FONTSIZE', (0, 0), (-1, -1), 8),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.gray),
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),  # Fondo gris para el encabezado
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
            ]))

            self.story.append(table)
        else:
            mensaje = Paragraph("No hay productos asociados a esta factura.", self.estiloPC())
            self.story.append(mensaje)

        self.story.append(Spacer(1, 0.5 * inch))
        imagen_pro = Image('Venta/proforma.jpg', width=650, height=25, hAlign='CENTER')
        self.story.append(imagen_pro)

    def estiloPC(self):
        return ParagraphStyle(name='centrado',
                              fontName="Helvetica-Bold",
                              fontSize=10,
                              alignment=1,
                              spaceAfter=7, )

    def estiloPC2(self):
        return ParagraphStyle(name='izquierda',
                              fontName="Helvetica",
                              fontSize=10,
                              alignment=0,
                              spaceAfter=7,
                              leftIndent=0)

    def estiloPC3(self):
        return ParagraphStyle(name='derecha',
                              fontName="Helvetica",
                              fontSize=10,
                              alignment=2,
                              spaceAfter=2,
                              rightIndent=-30, )

    def estiloCelda(self):
        return ParagraphStyle(name='celda',
                              fontName="Helvetica",
                              fontSize=8,
                              alignment=0,
                              spaceAfter=7,
                              wordWrap='CJK')

    def numeroPagina(self, canvas, doc):
        num = canvas.getPageNumber()
        text = "Página %s" % num
        canvas.drawRightString(200 * mm, 20 * mm, text)

        


class ProformaServicio():

    def __init__(self, v,d,c,p,t,cli):
        self.buf = BytesIO()
        self.venta = v
        self.detalle = d
        self.cantidad = c
        self.precio = p
        self.tienda = t
        self.cliente = str(cli).replace('%20',' ')
        self.total_ser = Decimal(p)*Decimal(c)
        #self.datos = Venta.objects.get(factura=v)
            
        #self.usuario = User.objects.get(id=self.rastreo.usuario.id)
        

    def run(self):
        self.doc = SimpleDocTemplate(
            self.buf, title=f"Proforma-Servicio {self.cliente}", pagesize=A4)
        self.story = []
        self.encabezado()
        self.crearTabla()
        self.doc.build(self.story, onFirstPage=self.numeroPagina,
                       onLaterPages=self.numeroPagina)
        pdf = self.buf.getvalue()
        self.buf.close()
        return pdf

    def encabezado(self):

        if self.tienda == 'Estanzuela':
            imagen_logo = Image('Venta/estanzuela.jpg', width=250,
                            height=85, hAlign='CENTER')
            p1 = Paragraph(f"TIENDA ESTANZUELA ", self.estiloPC())
        elif self.tienda == 'Teculutan':
            imagen_logo = Image('Venta/tecu.jpg', width=250,
                            height=85, hAlign='CENTER')
            p1 = Paragraph(f"TIENDA TECULUTAN ", self.estiloPC())
        else:
            imagen_logo = Image('Venta/zacapa.jpg', width=250,
                            height=85, hAlign='CENTER')
            p1 = Paragraph(f"TIENDA ZACAPA ", self.estiloPC()) 
               
        #p = Paragraph("CORPORACION SANTA ROSALIA ZACAPA", self.estiloPC())      
        #p11 = Paragraph(f"{self.tienda.direccion}", self.estiloPC())
        t1 = Paragraph(f" Lugar Y Fecha: {self.tienda},{datetime.now().strftime('%d-%m-%Y')} ", self.estiloPC2())
        a = Paragraph(f"Establecimiento {self.tienda}", self.estiloPC2())
            
        #a1_1 = Paragraph(
        #    f"Validacion Sistema {self.rastreo.token}", self.estiloPC2())
        #a3 = Paragraph(f"Fecha {self.venta}", self.estiloPC2())
        a4 = Paragraph(f"Proforma # {self.venta}-{self.cliente}", self.estiloPC3())
        
        
        self.story.append(imagen_logo)
        #self.story.append(p)
        #self.story.append(p1)
        #self.story.append(p11)
        self.story.append(t1)
        self.story.append(a)
        #self.story.append(a1)
        #self.story.append(a111)
        #self.story.append(a1_1)
        #self.story.append(a3)
        self.story.append(a4)
        self.story.append(Spacer(1, 0.2*inch))

    def crearTabla(self):
        data = [["Producto", "Cantidad", "PrecioUni","SubTotal",'Descuento', "Total"]] \
            + [[str(self.detalle).replace('%20',' '), self.cantidad, "Q."+str(self.precio), "Q."+str("0.00"), "Q."+str("0.00"), "Q."+str(self.total_ser)]]\
            + [["Total de Servicio", "", "","","", "Q."+str(self.total_ser)]]

        table = Table(data, colWidths=[9*cm, 2.1*cm, 2.1*cm, 2.1*cm, 2.1*cm, 2.1*cm, 2.1*cm])
        table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), "CENTER"),
            ('VALIGN', (-1, -1), (-1, -1), 'MIDDLE'),
            ('FONTSIZE', (0, 0), (-1, -2), 7),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.gray)
        ]))

        self.story.append(table)
        a0 = Paragraph(f"{self.cliente}",self.firma1())   
        self.story.append(a0)
        a00 = Paragraph(f"SRA",self.firma2())   
        self.story.append(a00)

    def estiloPC(self):
        return ParagraphStyle(name='centrado',
                              fontName="Helvetica-Bold",
                              fontSize=10,
                              alignment=1,
                              spaceAfter=7,)

    def estiloPC2(self):
        return ParagraphStyle(name='izquierda',
                              fontName="Helvetica",
                              fontSize=10,
                              alignment=0,
                              spaceAfter=7,
                              leftIndent=-30,
                              borderColor='#FF5733')

    def estiloPC3(self):
        return ParagraphStyle(name='derecha',
                              fontName="Helvetica",
                              fontSize=10,
                              alignment=2,
                              spaceAfter=2,
                              rightIndent=-30,)
    
    def firma1(self):
        return ParagraphStyle(name='derecha',
                              fontName="Helvetica",
                              fontSize=10,
                              alignment=2,
                              spaceAfter=2,
                              spaceBefore=110,
                              rightIndent=30,)
    
    def firma2(self):
        return ParagraphStyle(name='izquierda',
                              fontName="Helvetica",
                              fontSize=10,
                              alignment=0,
                              spaceAfter=5,
                              leftIndent=-30,)
    

    def numeroPagina(self, canvas, doc):
        num = canvas.getPageNumber()
        text = "Pagina %s" % num
        canvas.drawRightString(200*mm, 20*mm, text)         






        


class Cotizacion():

    def __init__(self, f):
        self.buf = BytesIO()
        self.rastreo = Venta.objects.get(token=f)

    def run(self):
        self.doc = SimpleDocTemplate(
            self.buf, title=f"Cotizacion {self.rastreo.factura}", pagesize=A4)
        self.story = []
        self.encabezado()
        self.crearTabla()
        self.doc.build(self.story, onFirstPage=self.numeroPagina,
                       onLaterPages=self.numeroPagina)
        pdf = self.buf.getvalue()
        self.buf.close()
        return pdf

    def encabezado(self):
        imagen_logo = Image('Venta/logo.png', width=95,
                            height=85, hAlign='RIGHT')
        p = Paragraph("LIBRERIA ROSELYN", self.estiloPC())
        p1 = Paragraph(
            "BARRIO EL CENTRO A UN COSTADO DE LA MUNICIPALIDAD", self.estiloPC())
        p11 = Paragraph("GUALAN,ZACAPA", self.estiloPC())
        p11 = Paragraph("COTIZACION", self.estiloPC())
        t1 = Paragraph(" TEL: 3276-0878 ", self.estiloPC())
        a = Paragraph(f"Cotizacion {self.rastreo.factura}", self.estiloPC2())
        n = Paragraph(f"Nit {self.rastreo.nit}", self.estiloPC2())
        a1 = Paragraph(f"Cliente {self.rastreo.nombre}", self.estiloPC2())
        d = Paragraph(f"Direccion {self.rastreo.direccion}", self.estiloPC2())
        a1_1 = Paragraph(
            f"Validacion Sistema {self.rastreo.token}", self.estiloPC2())
        a3 = Paragraph(
            f"Fecha {self.rastreo.fecha.strftime('%d/%m/%Y, %H:%M:%S')}", self.estiloPC2())
        a4 = Paragraph(
            f"Fecha de Impresion {datetime.today().strftime('%d/%m/%Y, %H:%M:%S')}", self.estiloPC3())
        self.story.append(imagen_logo)
        self.story.append(p)
        self.story.append(p1)
        self.story.append(p11)
        self.story.append(t1)
        self.story.append(a)
        self.story.append(n)
        self.story.append(d)
        self.story.append(a1)
        self.story.append(a1_1)
        self.story.append(a3)
        self.story.append(a4)
        self.story.append(Spacer(1, 0.2*inch))

    def crearTabla(self):
        data = [["Producto", "Cantidad", "Precio Unitario", "Total"]] \
            + [[x.id_inventario.nombre, x.cantidad, "Q."+str(x.precio_uni), "Q."+str(x.total)]
                for x in Detalle.objects.filter(factura=self.rastreo.factura)]\
            + [["Total de Cotizacion", "", "", "Q."+str(self.rastreo.total)]]

        table = Table(data, colWidths=[12*cm, 2.5*cm, 2.5*cm, 2.7*cm, 2.2*cm])
        table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), "CENTER"),
            ('VALIGN', (-1, -1), (-1, -1), 'MIDDLE'),
            ('FONTSIZE', (0, 0), (-1, -2), 7),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.gray)
        ]))

        self.story.append(table)

    def estiloPC(self):
        return ParagraphStyle(name='centrado',
                              fontName="Helvetica-Bold",
                              fontSize=10,
                              alignment=1,
                              spaceAfter=7,)

    def estiloPC2(self):
        return ParagraphStyle(name='izquierda',
                              fontName="Helvetica",
                              fontSize=10,
                              alignment=0,
                              spaceAfter=7,
                              leftIndent=-30,
                              borderColor='#FF5733')

    def estiloPC3(self):
        return ParagraphStyle(name='derecha',
                              fontName="Helvetica",
                              fontSize=10,
                              alignment=2,
                              spaceAfter=2,
                              rightIndent=-30,)

    def numeroPagina(self, canvas, doc):
        num = canvas.getPageNumber()
        text = "Pagina %s" % num
        canvas.drawRightString(200*mm, 20*mm, text)
