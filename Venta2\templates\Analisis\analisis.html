{% extends 'Base/base.html' %}
{% block title %}<PERSON><PERSON><PERSON>{% endblock %}

{% block content %}

{% if messages %}
{% for message in messages %}
<script>
  Swal.fire({
    "title": "Informacion Sistema",
    "text": "{{message}}",
    "icon": "{{message.tags}}"
  })
</script>
{% endfor %}
{% endif %}

<div class="content-wrapper">

  <div class="container-xxl flex-grow-1 container-p-y">

    <div class="row">
      <!-- Basic Layout -->
      <div class="col-md-10">
        <div class="card md-6">
          <div class="card-header d-flex align-items-center justify-content-between">
            <h5 class="mb-0">Listado de Analisis General</h5>
            <small class="text-muted float-end">Lista Analisis General</small>
          </div>
          <div class="card-body">
            <div class="table-responsive text-nowrap">

              <table class="table table-bordered order-table">
                <thead>
                  <tr>
                    <th>Total Ventas</th>
                    <th>Total Gastos</th>
                    <th>Utilidad</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>Q.{{ventas}}</td>
                    <td>Q.{{gastos}}</td>
                    <td>Q.{{total}}</td>
                  </tr>
                </tbody>
              </table><br>

              <table class="table table-bordered order-table">
                <thead>
                  <tr>
                    <th>Total Ventas Hoy</th>
                    <th>Total Gastos Hoy</th>
                    <th>Utilidad Hoy</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>Q.{{ventashoy}}</td>
                    <td>Q.{{gastoshoy}}</td>
                    <td>Q.{{totalhoy}}</td>
                  </tr>
                </tbody>
              </table><br>

              <table class="table table-bordered order-table">
                <thead>
                  <tr>
                    <th>Total Creditos </th>
                    <th>Pago de Creditos</th>
                    <th>Total Credito Hoy</th>
                    <th>Pago Creditos Hoy</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>Q.{{credi}}</td>
                    <td>Q.</td>
                    <td>Q.{{credihoy}}</td>
                    <td>Q.</td>
                  </tr>
                </tbody>
              </table><br>

            </div>
          </div>
        </div>
      </div>

    </div>

  </div>

</div>

<script type="text/javascript">
    (function (document) {
      'use strict';

      var LightTableFilter = (function (Arr) {

        var _input;

        function _onInputEvent(e) {
          _input = e.target;
          var tables = document.getElementsByClassName(_input.getAttribute('data-table'));
          Arr.forEach.call(tables, function (table) {
            Arr.forEach.call(table.tBodies, function (tbody) {
              Arr.forEach.call(tbody.rows, _filter);
            });
          });
        }

        function _filter(row) {
          var text = row.textContent.toLowerCase(), val = _input.value.toLowerCase();
          row.style.display = text.indexOf(val) === -1 ? 'none' : 'table-row';
        }

        return {
          init: function () {
            var inputs = document.getElementsByClassName('light-table-filter');
            Arr.forEach.call(inputs, function (input) {
              input.oninput = _onInputEvent;
            });
          }
        };
      })(Array.prototype);

      document.addEventListener('readystatechange', function () {
        if (document.readyState === 'complete') {
          LightTableFilter.init();
        }
      });

    })(document);
</script>


{% endblock %}