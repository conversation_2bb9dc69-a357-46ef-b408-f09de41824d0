# Generated by Django 4.1.7 on 2023-09-20 15:42

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('Venta', '0003_alter_detalle_fecha_alter_venta_fecha'),
    ]

    operations = [
        migrations.CreateModel(
            name='Pago',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tipo', models.CharField(max_length=75)),
                ('abono', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('estado', models.IntegerField(default=1)),
                ('fecha', models.DateField()),
                ('token', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('factura', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Venta.venta')),
                ('usuario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['factura'],
            },
        ),
    ]
