{% extends 'Base/basever.html' %}
{% block title %}Movimientos Producto en Ventas{% endblock %}

{% block content %}

<div class="content-wrapper">

    <div class="container-xxl flex-grow-1 container-p-y">

        <div class="row">
            <!-- Basic Layout -->
            <div class="col-md-12">
                <div class="card md-6">
                    <div class="card-header d-flex align-items-center justify-content-between">
                        <h5 class="mb-0">Movimientos Ventas del Producto {{prod.nombre}}</h5>
                        <small class="text-muted float-end">Movimientos Ventas del Producto {{prod.nombre}}</small>
                    </div>
                    <div class="card-body">

                    </div>&nbsp;

                    <div class="row" align="center">

                        <div class="col-md-12">

                            <label for="">Detalle del Producto</label>

                            <div style="height: 12rem; overflow-y: scroll;">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm" align="center">
                                        <thead>
                                            <tr align="center">
                                                <th>Factura</th>
                                                <th>Tipo</th>
                                                {% if prod.tipo == "FEL-Servicio" %}
                                                <th>Detalle</th>
                                                {% else %}
                                                <th>Producto</th>
                                                {% endif %}
                                                <th>Cantidad</th>
                                                <th>Precio</th>
                                                <th>Sub-Total</th>
                                                <th>Descuento</th>
                                                <th>Total</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for prod in detalles %}
                                            <tr align="center">
                                                <td>{{prod.factura}}</td>
                                                <td>{{prod.factura.tipo}}</td>
                                                {% if prod.tipo == "FEL-Servicio" %}
                                                <td>{{prod.detalle_ser}}</td>
                                                {% else %}
                                                <td>{{prod.id_prod.nombre}}</td>
                                                {% endif %}
                                                <td>{{prod.cantidad}}</td>
                                                <td>Q.{{prod.precio}}</td>
                                                <td>Q.{{prod.subtotal}}</td>
                                                <td>Q.{{prod.descuento}}</td>
                                                <td>Q.{{prod.total}}</td>
                                            </tr>
                                            {% empty %}
                                            <caption>SIN MOVIMIENTOS</caption>
                                            {% endfor %}
                                        </tbody>
                                    </table>
    
                                </div>
                            </div>

                        </div>

                    </div>

                </div>




            </div>




        </div>
    </div>





    {% endblock %}