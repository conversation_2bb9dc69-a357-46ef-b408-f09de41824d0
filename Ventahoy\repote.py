from io import BytesIO
from datetime import datetime
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib.styles import ParagraphStyle, TA_CENTER, TA_LEFT
from reportlab.lib.units import inch, mm, cm
from reportlab.lib import colors
from reportlab.platypus import (
    Paragraph,
    Table,
    SimpleDocTemplate,
    Spacer,
    TableStyle,
    Paragraph)
from reportlab.lib.styles import getSampleStyleSheet
from django.db.models import Sum
from reportlab.platypus import Image

# from .models import Persona
from Venta.models import Venta, Detalle
from Sucursal.models import Sucursal
from user.models import User


class Comprobante():

    def __init__(self, f):
        self.buf = BytesIO()
        self.rastreo = Venta.objects.get(factura=f)
        self.usuario = User.objects.get(id=self.rastreo.usuario.id)
        self.vertienda = Sucursal.objects.get(
            ubicacion=self.rastreo.usuario.tienda)
        self.tienda = Sucursal.objects.get(id=self.vertienda.id)

    def run(self):
        self.doc = SimpleDocTemplate(
            self.buf, title=f"Comprobante {self.rastreo.factura}", pagesize=A4)
        self.story = []
        self.encabezado()
        self.crearTabla()
        self.doc.build(self.story, onFirstPage=self.numeroPagina,
                       onLaterPages=self.numeroPagina)
        pdf = self.buf.getvalue()
        self.buf.close()
        return pdf

    def encabezado(self):
        imagen_logo = Image('Venta/logo.jpg', width=95,
                            height=85, hAlign='RIGHT')
        p = Paragraph("CORPORACION SANTA ROSALIA ZACAPA", self.estiloPC())
        p1 = Paragraph(f"TIENDA {self.tienda.nombre} ", self.estiloPC())
        p11 = Paragraph(f"{self.tienda.direccion}", self.estiloPC())
        t1 = Paragraph(f" TEL: {self.tienda.telefono} ", self.estiloPC())
        a = Paragraph(f"Venta {self.rastreo.factura}", self.estiloPC2())
        a0 = Paragraph(f"Nit {self.rastreo.nit}", self.estiloPC2())
        a1 = Paragraph(f"Cliente {self.rastreo.nombre}", self.estiloPC2())
        a1_1 = Paragraph(
            f"Validacion Sistema {self.rastreo.token}", self.estiloPC2())
        a3 = Paragraph(f"Fecha {self.rastreo.fecha}", self.estiloPC2())
        a4 = Paragraph(
            f"Fecha de Impresion {datetime.today().strftime('%d/%m/%Y, %H:%M:%S')}", self.estiloPC3())
        self.story.append(imagen_logo)
        self.story.append(p)
        self.story.append(p1)
        self.story.append(p11)
        self.story.append(t1)
        self.story.append(a)
        self.story.append(a0)
        self.story.append(a1)
        self.story.append(a1_1)
        self.story.append(a3)
        self.story.append(a4)
        self.story.append(Spacer(1, 0.2*inch))

    def crearTabla(self):
        data = [["Producto", "Cantidad", "PrecioUni","SubTotal",'Descuento', "Total"]] \
            + [[x.id_prod.nombre, x.cantidad, "Q."+str(x.precio), "Q."+str(x.subtotal), "Q."+str(x.descuento), "Q."+str(x.total)]
                for x in Detalle.objects.filter(factura=self.rastreo.factura)]\
            + [["Total de Venta", "", "","","", "Q."+str(self.rastreo.total)]]

        table = Table(data, colWidths=[9*cm, 2.1*cm, 2.1*cm, 2.1*cm, 2.1*cm, 2.1*cm, 2.1*cm])
        table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), "CENTER"),
            ('VALIGN', (-1, -1), (-1, -1), 'MIDDLE'),
            ('FONTSIZE', (0, 0), (-1, -2), 7),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.gray)
        ]))

        self.story.append(table)

    def estiloPC(self):
        return ParagraphStyle(name='centrado',
                              fontName="Helvetica-Bold",
                              fontSize=10,
                              alignment=1,
                              spaceAfter=7,)

    def estiloPC2(self):
        return ParagraphStyle(name='izquierda',
                              fontName="Helvetica",
                              fontSize=10,
                              alignment=0,
                              spaceAfter=7,
                              leftIndent=-30,
                              borderColor='#FF5733')

    def estiloPC3(self):
        return ParagraphStyle(name='derecha',
                              fontName="Helvetica",
                              fontSize=10,
                              alignment=2,
                              spaceAfter=2,
                              rightIndent=-30,)

    def numeroPagina(self, canvas, doc):
        num = canvas.getPageNumber()
        text = "Pagina %s" % num
        canvas.drawRightString(200*mm, 20*mm, text)


class Cotizacion():

    def __init__(self, f):
        self.buf = BytesIO()
        self.rastreo = Venta.objects.get(token=f)

    def run(self):
        self.doc = SimpleDocTemplate(
            self.buf, title=f"Cotizacion {self.rastreo.factura}", pagesize=A4)
        self.story = []
        self.encabezado()
        self.crearTabla()
        self.doc.build(self.story, onFirstPage=self.numeroPagina,
                       onLaterPages=self.numeroPagina)
        pdf = self.buf.getvalue()
        self.buf.close()
        return pdf

    def encabezado(self):
        imagen_logo = Image('Venta/logo.png', width=95,
                            height=85, hAlign='RIGHT')
        p = Paragraph("LIBRERIA ROSELYN", self.estiloPC())
        p1 = Paragraph(
            "BARRIO EL CENTRO A UN COSTADO DE LA MUNICIPALIDAD", self.estiloPC())
        p11 = Paragraph("GUALAN,ZACAPA", self.estiloPC())
        p11 = Paragraph("COTIZACION", self.estiloPC())
        t1 = Paragraph(" TEL: 3276-0878 ", self.estiloPC())
        a = Paragraph(f"Cotizacion {self.rastreo.factura}", self.estiloPC2())
        n = Paragraph(f"Nit {self.rastreo.nit}", self.estiloPC2())
        a1 = Paragraph(f"Cliente {self.rastreo.nombre}", self.estiloPC2())
        d = Paragraph(f"Direccion {self.rastreo.direccion}", self.estiloPC2())
        a1_1 = Paragraph(
            f"Validacion Sistema {self.rastreo.token}", self.estiloPC2())
        a3 = Paragraph(
            f"Fecha {self.rastreo.fecha.strftime('%d/%m/%Y, %H:%M:%S')}", self.estiloPC2())
        a4 = Paragraph(
            f"Fecha de Impresion {datetime.today().strftime('%d/%m/%Y, %H:%M:%S')}", self.estiloPC3())
        self.story.append(imagen_logo)
        self.story.append(p)
        self.story.append(p1)
        self.story.append(p11)
        self.story.append(t1)
        self.story.append(a)
        self.story.append(n)
        self.story.append(d)
        self.story.append(a1)
        self.story.append(a1_1)
        self.story.append(a3)
        self.story.append(a4)
        self.story.append(Spacer(1, 0.2*inch))

    def crearTabla(self):
        data = [["Producto", "Cantidad", "Precio Unitario", "Total"]] \
            + [[x.id_inventario.nombre, x.cantidad, "Q."+str(x.precio_uni), "Q."+str(x.total)]
                for x in Detalle.objects.filter(factura=self.rastreo.factura)]\
            + [["Total de Cotizacion", "", "", "Q."+str(self.rastreo.total)]]

        table = Table(data, colWidths=[12*cm, 2.5*cm, 2.5*cm, 2.7*cm, 2.2*cm])
        table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), "CENTER"),
            ('VALIGN', (-1, -1), (-1, -1), 'MIDDLE'),
            ('FONTSIZE', (0, 0), (-1, -2), 7),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.gray)
        ]))

        self.story.append(table)

    def estiloPC(self):
        return ParagraphStyle(name='centrado',
                              fontName="Helvetica-Bold",
                              fontSize=10,
                              alignment=1,
                              spaceAfter=7,)

    def estiloPC2(self):
        return ParagraphStyle(name='izquierda',
                              fontName="Helvetica",
                              fontSize=10,
                              alignment=0,
                              spaceAfter=7,
                              leftIndent=-30,
                              borderColor='#FF5733')

    def estiloPC3(self):
        return ParagraphStyle(name='derecha',
                              fontName="Helvetica",
                              fontSize=10,
                              alignment=2,
                              spaceAfter=2,
                              rightIndent=-30,)

    def numeroPagina(self, canvas, doc):
        num = canvas.getPageNumber()
        text = "Pagina %s" % num
        canvas.drawRightString(200*mm, 20*mm, text)
