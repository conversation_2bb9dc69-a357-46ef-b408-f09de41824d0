"""corporacion URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path,include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('',include('Login.urls')),
    path('inicio/',include('Inicio.urls')),
    path('categoria/',include('Categoria.urls')),
    path('producto/',include('Producto.urls')),
    path('cliente/',include('Cliente.urls')),
    path('venta/',include('Venta.urls')),
    path('gasto/',include('Gasto.urls')),
    path('pago/',include('PagoCredito.urls')),
    path('sucursal/', include('Sucursal.urls')),
    path('envio/', include('Envios.urls')),
    path('vale/', include('Vales.urls')),
    path('proveedor/', include('Proveedor.urls')),
    path('consignacion/', include('Traslado.urls')),
    path('usuarios/',include('user.urls')),
]

