# Generated by Django 4.1 on 2023-09-14 19:25

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Producto', '0002_alter_producto_options'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Venta',
            fields=[
                ('factura', models.BigAutoField(primary_key=True, serialize=False)),
                ('nit', models.CharField(blank=True, default='CF', max_length=15, null=True)),
                ('nombre', models.CharField(blank=True, default='Consumidor Final', max_length=250, null=True)),
                ('direccion', models.CharField(blank=True, default='Ciudad', max_length=850, null=True)),
                ('tipo', models.CharField(blank=True, max_length=250, null=True)),
                ('link', models.Char<PERSON><PERSON>(blank=True, max_length=250, null=True)),
                ('numero', models.BigIntegerField(blank=True, null=True)),
                ('serie', models.CharField(blank=True, max_length=250, null=True)),
                ('anula', models.CharField(blank=True, max_length=450, null=True)),
                ('fecha_fel', models.CharField(max_length=250)),
                ('total', models.DecimalField(blank=True, decimal_places=2, default=0.0, max_digits=10)),
                ('fecha', models.CharField(max_length=10)),
                ('estado', models.IntegerField(default=0)),
                ('token', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('usuario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['factura'],
            },
        ),
        migrations.CreateModel(
            name='Detalle',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cantidad', models.IntegerField()),
                ('precio', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total', models.DecimalField(decimal_places=2, max_digits=10)),
                ('usuario', models.CharField(max_length=200)),
                ('fecha', models.CharField(max_length=10)),
                ('estado', models.IntegerField(default=1)),
                ('token', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('factura', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Venta.venta')),
                ('id_prod', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Producto.producto')),
            ],
            options={
                'ordering': ['factura'],
            },
        ),
    ]
