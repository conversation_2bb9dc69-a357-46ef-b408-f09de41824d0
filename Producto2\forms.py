from django import forms
from .models import Producto,ProductoFactura
from Categoria.models import Categoria

TIENDA = (('Zacapa', 'Zacapa'), ('Estanzuela', 'Estanzuela'),
          ('<PERSON><PERSON><PERSON>an', 'Teculutan'),('Santa Cruz', 'Santa Cruz'))
CATE = [(c.id, c.nombre) for c in Categoria.objects.all()]


class ProductoForm(forms.ModelForm):

    class Meta:
        model = Producto
        fields = ['nombre', 'descripcion', 'stock',
                  'precio_compra', 'precio_venta', 'tienda', 'id_cate']

        widgets = {
            'nombre': forms.TextInput(attrs={'name': 'nombre', 'class': 'form-control', 'placeholder': 'Nombre de Producto', 'style': 'color: black', 'require': True, 'autofocus': True}),
            'descripcion': forms.TextInput(attrs={'name': 'descripcion', 'class': 'form-control', 'placeholder': 'Descripcion de Producto', 'style': 'color: black', 'require': False}),
            'stock': forms.TextInput(attrs={'name': 'stock', 'class': 'form-control', 'placeholder': '0', 'style': 'color: black', 'require': True}),
            'precio_compra': forms.TextInput(attrs={'name': 'precio_compra', 'class': 'form-control', 'placeholder': '0', 'style': 'color: black', 'require': True}),
            'precio_venta': forms.TextInput(attrs={'name': 'precio_venta', 'class': 'form-control', 'placeholder': '0', 'style': 'color: black', 'require': True}),
            'id_cate': forms.Select(attrs={'class': 'selectpicker form-control', 'data-style': 'btn-outline-info', 'placeholder': 'Categoria', 'style': 'color: black', 'require': True}, choices=CATE),
            'tienda': forms.Select(attrs={'class': 'selectpicker form-control', 'data-style': 'btn-outline-info', 'placeholder': 'Tienda', 'style': 'color: black', 'require': True}, choices=TIENDA),
        }


class UpdateProductoForm(forms.ModelForm):

    class Meta:
        model = Producto
        fields = ['nombre', 'descripcion', 'stock',
                  'precio_compra', 'precio_venta', 'tienda', 'id_cate']

        widgets = {
            'nombre': forms.TextInput(attrs={'name': 'nombre', 'class': 'form-control', 'placeholder': 'Nombre de Producto', 'style': 'color: black', 'require': True, 'autofocus': True}),
            'descripcion': forms.TextInput(attrs={'name': 'descripcion', 'class': 'form-control', 'placeholder': 'Descripcion de Producto', 'style': 'color: black', 'require': False}),
            'stock': forms.TextInput(attrs={'name': 'stock', 'class': 'form-control', 'placeholder': '0', 'style': 'color: black', 'require': True}),
            'precio_compra': forms.TextInput(attrs={'name': 'precio_compra', 'class': 'form-control', 'placeholder': '0', 'style': 'color: black', 'require': True}),
            'precio_venta': forms.TextInput(attrs={'name': 'precio_venta', 'class': 'form-control', 'placeholder': '0', 'style': 'color: black', 'require': True}),
            'id_cate': forms.Select(attrs={'class': 'selectpicker form-control', 'data-style': 'btn-outline-info', 'placeholder': 'Tienda', 'style': 'color: black', 'require': True}, choices=CATE),
            'tienda': forms.Select(attrs={'class': 'selectpicker form-control', 'data-style': 'btn-outline-info', 'placeholder': 'Rol', 'style': 'color: black', 'require': True}, choices=TIENDA),
        }



class UpdateNormalProductoForm(forms.ModelForm):

    class Meta:
        model = Producto
        fields = ['nombre', 'descripcion', 'stock',
                  'precio_compra', 'precio_venta', 'tienda', 'id_cate']

        widgets = {
            'nombre': forms.TextInput(attrs={'name': 'nombre', 'class': 'form-control', 'placeholder': 'Nombre de Producto', 'style': 'color: black', 'require': True, 'autofocus': True}),
            'descripcion': forms.TextInput(attrs={'name': 'descripcion', 'class': 'form-control', 'placeholder': 'Descripcion de Producto', 'style': 'color: black', 'require': False}),
            'stock': forms.TextInput(attrs={'name': 'stock', 'class': 'form-control','readonly': 'readonly', 'placeholder': '0', 'style': 'color: black', 'require': False}),
            'precio_compra': forms.TextInput(attrs={'name': 'precio_compra','readonly': 'readonly', 'class': 'form-control', 'placeholder': '0', 'style': 'color: black', 'require': False}),
            'precio_venta': forms.TextInput(attrs={'name': 'precio_venta','readonly': 'readonly', 'class': 'form-control', 'placeholder': '0', 'style': 'color: black', 'require': False}),
            'id_cate': forms.Select(attrs={'class': 'selectpicker form-control', 'data-style': 'btn-outline-info', 'placeholder': 'Tienda', 'style': 'color: black', 'require': True}, choices=CATE),
            'tienda': forms.Select(attrs={'class': 'selectpicker form-control', 'data-style': 'btn-outline-info', 'placeholder': 'Rol', 'style': 'color: black', 'require': True}, choices=TIENDA),
        }


class ProductoFacturaForm(forms.ModelForm):

    class Meta:
        model = ProductoFactura
        fields = ['factura', 'serie', 'cantidad',
                  'total', 'fecha_factura']

        widgets = {
            'factura': forms.TextInput(attrs={'name': 'nombre', 'class': 'form-control', 'placeholder': 'Numero de Factura', 'style': 'color: black', 'require': True, 'autofocus': True}),
            'serie': forms.TextInput(attrs={'name': 'descripcion', 'class': 'form-control', 'placeholder': 'Serie de Factura', 'style': 'color: black', 'require': False}),
            'cantidad': forms.TextInput(attrs={'name': 'stock', 'class': 'form-control', 'placeholder': 'Cantidad de Arituclos en Factura', 'style': 'color: black', 'require': True}),
            'total': forms.TextInput(attrs={'name': 'precio_compra', 'class': 'form-control', 'placeholder': 'Total', 'style': 'color: black', 'require': True}),
            'fecha_factura': forms.TextInput(attrs={'type':'date','name': 'precio_venta', 'class': 'form-control', 'placeholder': '0', 'style': 'color: black', 'require': True}),
        }