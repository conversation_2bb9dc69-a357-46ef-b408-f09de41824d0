from django.shortcuts import render,redirect
from datetime import datetime
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from Producto.models import Producto,ProductoFactura,DetalleFactura
from Cliente.models import Cliente
from Categoria.models import Categoria
from Gasto.models import Gasto
from Envios.models import Envio
from Proveedor.models import Proveedor
from Sucursal.models import Sucursal
from PagoCredito.models import Pago
from user.models import User
from Venta.models import Venta

@login_required
def inicio(request):
    if request.user.rol == 'admin':
        prod = Producto.objects.count()
        cli = Cliente.objects.count()
        prov = Proveedor.objects.count()
        cate = Categoria.objects.count()
        gasto = Gasto.objects.count()
        envio = Envio.objects.count()
        sucu = Sucursal.objects.count()
        pago = Pago.objects.count()
        usu = User.objects.count()
        venta = Venta.objects.count()
        ventahoy = Venta.objects.filter(fecha=datetime.today()).count()
        enviohoy = Envio.objects.filter(fecha=datetime.today()).count()
        gastohoy = Gasto.objects.filter(fecha=datetime.today()).count()
        pagohoy = Pago.objects.filter(fecha=datetime.today()).count()

        context = {'prod':prod,'cli':cli,'prov':prov,'cate':cate,'gasto':gasto,'envio':envio,'sucu':sucu,'pago':pago,'usu':usu,'venta':venta,'hoy':ventahoy,'enviohoy':enviohoy,'gastohoy':gastohoy,'pagohoy':pagohoy}

    
    else:
        prod = Producto.objects.filter(tienda=request.user.tienda).count()
        gasto = Gasto.objects.filter(tienda=request.user.tienda).count()
        envio = Envio.objects.filter(origen=request.user.tienda).count()
        usu = User.objects.filter(tienda=request.user.tienda).count()
        venta = Venta.objects.filter(tienda=request.user.tienda).count()
        ventahoy = Venta.objects.filter(fecha=datetime.today(),tienda=request.user.tienda).count()
        enviohoy = Envio.objects.filter(fecha=datetime.today(),origen=request.user.tienda).count()
        gastohoy = Gasto.objects.filter(fecha=datetime.today(),tienda=request.user.tienda).count()
             
        context = {'prod':prod,'gasto':gasto,'envio':envio,'usu':usu,'venta':venta,'hoy':ventahoy,'enviohoy':enviohoy,'gastohoy':gastohoy}

    return render(request,'Inicio/inicio.html',context)


@login_required
def consulta(request):
   
   
       
        return render(request, 'Inicio/consulta.html')
        

@login_required
def Productos(request):
   
   
       
        return render(request, 'Inicio/productos.html')
    
@login_required
def Gastos(request):
   
   
       
        return render(request, 'Inicio/gastos.html')