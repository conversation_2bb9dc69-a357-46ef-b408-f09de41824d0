# Generated by Django 4.2.4 on 2025-03-10 11:36

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('Sucursal', '0007_vales_detallevales'),
    ]

    operations = [
        migrations.CreateModel(
            name='Movimientos',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('doc', models.BigIntegerField(blank=True, default=0, null=True)),
                ('tipo', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('tienda', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('id_prod', models.IntegerField(blank=True, default=0, null=True)),
                ('prod', models.Char<PERSON>ield(blank=True, default='', max_length=255, null=True)),
                ('ultimo', models.IntegerField(blank=True, default=0, null=True)),
                ('entrada', models.IntegerField(blank=True, default=0, null=True)),
                ('salida', models.IntegerField(blank=True, default=0, null=True)),
                ('actual', models.IntegerField(blank=True, default=0, null=True)),
                ('obs', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('fecha', models.DateTimeField(auto_now_add=True)),
                ('usuario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['id'],
            },
        ),
    ]
