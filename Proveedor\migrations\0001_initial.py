# Generated by Django 4.2.6 on 2023-12-06 10:26

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Proveedor',
            fields=[
                ('nit', models.Char<PERSON>ield(max_length=15, primary_key=True, serialize=False)),
                ('nombre', models.Char<PERSON>ield(max_length=550)),
                ('direccion', models.CharField(max_length=550)),
                ('telefono', models.CharField(blank=True, default='0000-0000', max_length=9, null=True)),
                ('compras', models.IntegerField(default=0)),
                ('total_compra', models.DecimalField(decimal_places=2, default='0.00', max_digits=12)),
                ('fecha', models.<PERSON><PERSON><PERSON><PERSON>(max_length=10)),
                ('usuario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['nit'],
            },
        ),
    ]
