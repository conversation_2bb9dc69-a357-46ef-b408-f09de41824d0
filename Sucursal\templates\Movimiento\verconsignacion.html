{% extends 'Base/basever.html' %}
{% block title %}Movimientos Consignacion{% endblock %}

{% block content %}

<div class="content-wrapper">

    <div class="container-xxl flex-grow-1 container-p-y">

        <div class="row">
            <!-- Basic Layout -->
            <div class="col-md-12">
                <div class="card md-6">
                    <div class="card-header d-flex align-items-center justify-content-between">
                        <h5 class="mb-0">Movimientos Consignacion del Producto {{prod.nombre}}</h5>
                        <small class="text-muted float-end">Movimientos Consignacion del Producto {{prod.nombre}}</small>
                    </div>
                    <div class="card-body">

                    </div>&nbsp;

                    <div class="row" align="center">

                        <div class="col-md-12">

                            <label for="">Detalle del Producto</label>

                            <div style="height: 12rem; overflow-y: scroll;">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm" align="center">
                                        <thead>
                                            <tr align="center">
                                                <th>Consignacion</th>
                                                <th>Producto</th>
                                                <th>Stock</th>
                                                <th>Ingreso</th>
                                                <th>Stock</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for d in d %}
                                            <tr align="center">
                                                <td>{{d.traslado}}</td>
                                                <td>{{d.id_prod.nombre}}</td>
                                                <td>{{d.stock_antes}}</td>
                                                <td>{{d.cantidad}}</td>
                                                <td>{{d.stock_ahora}}</td>
                                            </tr>
                                            {% empty %}
                                            <caption>SIN CONSIGNACIONES</caption>
                                            {% endfor %}
                                        </tbody>
                                    </table>
    
                                </div>
                            </div>

                        </div>

                    </div>

                </div>




            </div>




        </div>
    </div>





    {% endblock %}