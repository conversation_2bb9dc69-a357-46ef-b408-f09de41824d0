from decimal import Decimal
from django.shortcuts import render, redirect
from datetime import datetime
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from Cliente.models import Cliente
from Cliente.forms import ClienteForm, UpdateClienteForm
from Venta.models import Venta
from PagoCredito.models import Pago
from PagoCredito.repote import Comprobante, Ticket
from PagoCredito.forms import PagoForm, UpdatePagoForm
from user.models import User
from django.http import HttpResponse
from django.db.models import Sum


@login_required
def pago(request, f):

    form = PagoForm()

    ventato = Venta.objects.get(factura=f)
    verfalta = Pago.objects.filter(factura=f).aggregate(adeuda=Sum('abono'))

    if verfalta['adeuda'] == None:
        verfalta['adeuda'] = Decimal(0.00)
    else:
        verfalta['adeuda']

    queda = ventato.total-verfalta['adeuda']

    if request.method == "POST":
        form = PagoForm(request.POST)
        if form.is_valid():
            venta = Venta.objects.get(factura=f)
            ver = Pago.objects.filter(factura=f).aggregate(adeu=Sum('abono'))
            cli = Cliente.objects.get(nit=venta.nit)

            if ver['adeu'] == None:
                ver['adeu'] = Decimal(0.00)
            else:
                ver['adeu']

            if venta.total == ver['adeu']:
                Venta.objects.filter(factura=venta.factura).update(estado=1)
                messages.success(
                    request, f'Credito # {venta.factura} Pagado en Su Totalidad!')
                return redirect('ListaPagoPendiente')
            elif venta.total >= form.cleaned_data['abono']:
                try:
                    p = Pago()
                    p.factura = Venta.objects.get(
                        factura=request.POST['factura'])
                    p.tipo = form.cleaned_data['tipo']
                    p.nit = venta.nit
                    p.abono = form.cleaned_data['abono']
                    p.fecha = datetime.today()
                    p.estado = 1
                    p.usuario = User.objects.get(id=request.user.id)
                    p.save()
                    # Venta.objects.filter(factura=request.POST['factura']).update(total=venta.total-p.abono)
                    Cliente.objects.filter(nit=cli.nit).update(
                        total_credito=cli.total_credito-p.abono, total_credito_pagado=cli.total_credito_pagado+p.abono)
                    messages.success(request, f'{p.factura}')
                    return redirect('NuevoPago', f)
                except:
                    messages.error(
                        request, f'No Se Pudo Ingresar Abono Credito # {p.factura}!')
                    return redirect('NuevoPago', f)
            else:
                messages.error(
                    request, f'Abono Q.{form.cleaned_data["abono"]} Es Mayor Al Adeudado Q.{venta.total}!')
                return redirect('NuevoPago', f)

    return render(request, 'PagoCredito/nuevo.html', {'form': form, 'f': f, 'v': queda})


@login_required
def listado(request):

    if request.user.rol == "admin":
        credito = Venta.objects.filter(tipo="NOTA CREDITO")
    else:
        credito = Venta.objects.filter(
            tienda=request.user.tienda, tipo="NOTA CREDITO")

    return render(request, 'PagoCredito/lista.html', {'credito': credito})


@login_required
def listadopagos(request):

    credito = Pago.objects.all()

    return render(request, 'PagoCredito/listapagos.html', {'credito': credito})


@login_required
def listadopagoscliente(request, n):

    credito = Pago.objects.filter(nit=n)

    return render(request, 'PagoCredito/listapagoscliente.html', {'credito': credito})


@login_required
def actualizar(request, t):
    pass


@login_required
def pdf(request):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        response = HttpResponse(content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="estadod-de-cuenta.pdf"'
        r = Comprobante()
        response.write(r.run())
        return response


@login_required
def pdfpagos(request, t):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        response = HttpResponse(content_type='application/pdf')
        response[
            'Content-Disposition'] = f'attachment; filename="comprobante-credito-#-{t}.pdf"'
        r = Ticket(t)
        response.write(r.run())
        return response
