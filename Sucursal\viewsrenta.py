from django.http import Http404
from django.shortcuts import render, redirect
from datetime import datetime
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from user.models import User
from django.db.models import Q
from django.db.models import <PERSON><PERSON>, <PERSON>, Min
from Venta.models import Venta, Detalle
from Producto.models import ProductoFactura, DetalleFactura, Producto, Bitacora
from Traslado.models import DetalleTraslado,Traslado
from Envios.models import DetalleEnvio
from Venta.models import Detalle
from PagoCredito.models import Pago


@login_required
def renta(request):

    b = False
    busqueda = []
    servi = 0
    suma = 0

    if request.method == 'POST':



            if request.POST['tienda'] == "":
                messages.error(request, f'Campo Tienda No Puede Estar Vacio')
                return redirect('ConsultaFecha', b)
            else:
                if request.POST['tipo'] == 'FEL':
                    if request.POST['tienda'] == 'Todas':
                        ini = request.POST['inicio']
                        final = request.POST['fin']
                        tvs = Venta.objects.filter(fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tipo=request.POST['tipo']).count()
                        
                        for bus in Venta.objects.filter(fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tipo=request.POST['tipo']).order_by('-fecha'):
                            for d in Detalle.objects.filter(factura=bus.factura,fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin']):
                                if d.detalle_ser:
                                    servi += d.total
                                else:
                                    pass    
                                for p in Producto.objects.filter(id=d.id_prod.id):
                                    prodc = p.precio_compra
                                    prodv = p.precio_venta
                                    canprod = d.cantidad
                                    uti = abs((p.precio_compra*d.cantidad)-(p.precio_venta*canprod))
                                    suma += uti 
                                    busqueda.append({
                                        'factura':bus.factura,
                                        'ie':bus.interno_estanzuela,
                                        'it':bus.interno_tecu,
                                        'iz':bus.interno_zacapa,
                                        'ist':bus.interno_rio_hondo,
                                        'cliente':bus.nombre,
                                        'fecha':bus.fecha,
                                        'cantidad':canprod,
                                        'totalv':prodv*canprod,
                                        'totalc':prodc*canprod,
                                        'id':p.nombre,
                                        'utilidad':uti,
                                        'tienda':bus.tienda
                                    })
                        
                        b = True
                        return render(request,'Rentabilidad/global.html', {'b': b, 'busqueda': busqueda,'suma':suma,'tvs':tvs,'t':request.POST['tienda'],'f':request.POST['tipo']})
                    
                    else:
                        ini = request.POST['inicio']
                        final = request.POST['fin']
                        tvs = Venta.objects.filter(fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tienda=request.POST['tienda'],tipo=request.POST['tipo']).count()
                        
                        for bus in Venta.objects.filter(fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tienda=request.POST['tienda'],tipo=request.POST['tipo']).order_by('-fecha'):
                            for d in Detalle.objects.filter(factura=bus.factura,fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin']):
                                if d.detalle_ser:
                                    servi += d.total
                                else:
                                    pass    
                                for p in Producto.objects.filter(id=d.id_prod.id):
                                    miprod = p.nombre
                                    prodc = p.precio_compra
                                    prodv = p.precio_venta
                                    canprod = d.cantidad
                                    uti = abs((p.precio_compra*d.cantidad)-(p.precio_venta*canprod))
                                    suma += uti 
                                    busqueda.append({
                                        'nombre':miprod,
                                        'factura':bus.factura,
                                        'ie':bus.interno_estanzuela,
                                        'it':bus.interno_tecu,
                                        'iz':bus.interno_zacapa,
                                        'ist':bus.interno_rio_hondo,
                                        'cliente':bus.nombre,
                                        'fecha':bus.fecha,
                                        'cantidad':canprod,
                                        'totalv':prodv*canprod,
                                        'totalc':prodc*canprod,
                                        'id':p.nombre,
                                        'utilidad':uti,
                                        'tienda':bus.tienda
                                    })
                        
                        b = True
                        return render(request,'Rentabilidad/global.html', {'b': b, 'busqueda': busqueda,'suma':suma,'tvs':tvs,'t':request.POST['tienda'],'f':request.POST['tipo']})
                    
                else:

                    if request.POST['tienda'] == 'Todas':
                        ini = request.POST['inicio']
                        final = request.POST['fin']
                        tvs = Venta.objects.filter(fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tipo='FEL-Servicio').count()
                        suma = Venta.objects.filter(fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tipo='FEL-Servicio').aggregate(s=Sum('total'))
                        for f in Venta.objects.filter(tipo='FEL-Servicio',fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin']):

                            for d in Detalle.objects.filter(factura=f.factura,fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin']):

                                busqueda.append({
                                    'factura':f.factura,
                                    'ie':f.interno_estanzuela,
                                    'it':f.interno_tecu,
                                    'iz':f.interno_zacapa,
                                    'ist':f.interno_rio_hondo,
                                    'cliente':f.nombre,
                                    'fecha':f.fecha,
                                    'cantidad':d.cantidad,
                                    'totalv':f.total,
                                    'totalc':0,
                                    'id':d.detalle_ser,
                                    'utilidad':abs(f.total-0),
                                    'tienda':f.tienda
                                })
                        
                        b = True
                        return render(request,'Rentabilidad/global.html', {'b': b, 'busqueda': busqueda,'suma':suma['s'],'tvs':tvs,'t':request.POST['tienda'],'f':request.POST['tipo']})
                    
                    else:
                        ini = request.POST['inicio']
                        final = request.POST['fin']
                        tvs = Venta.objects.filter(fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tipo=request.POST['tipo'],tienda=request.POST['tienda']).count()
                        suma = Venta.objects.filter(tipo='FEL-Servicio',tienda=request.POST['tienda'],fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin']).aggregate(s=Sum('total'))
                        for f in Venta.objects.filter(tipo='FEL-Servicio',tienda=request.POST['tienda']):

                            for d in Detalle.objects.filter(factura=f.factura,fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin']):

                                busqueda.append({
                                    'factura':f.factura,
                                    'ie':f.interno_estanzuela,
                                    'it':f.interno_tecu,
                                    'iz':f.interno_zacapa,
                                    'ist':f.interno_rio_hondo,
                                    'cliente':f.nombre,
                                    'fecha':f.fecha,
                                    'cantidad':d.cantidad,
                                    'totalv':f.total,
                                    'totalc':0,
                                    'id':d.detalle_ser,
                                    'utilidad':abs(f.total-0),
                                    'tienda':f.tienda
                                })
                        
                        b = True
                        return render(request,'Rentabilidad/global.html', {'b': b, 'busqueda': busqueda,'suma':suma['s'],'tvs':tvs,'t':request.POST['tienda'],'f':request.POST['tipo']})    
                
                
    
             


    return render(request,'Rentabilidad/global.html', {'b': b})



