from django.db import models
from user.models import User
from Producto.models import Producto
import uuid


class Sucursal(models.Model):
    nit = models.CharField(max_length=15, blank=True, null=True, default='')
    nombre = models.CharField(max_length=550, blank=False, null=False)
    direccion = models.CharField(max_length=1000, blank=False, null=False)
    telefono = models.CharField(
        max_length=9, blank=True, null=True, default='')
    telefono2 = models.CharField(
        max_length=9, blank=True, null=True, default='')
    correo = models.CharField(
        max_length=550, blank=True, null=True, default='')
    ubicacion = models.CharField(
        max_length=550, blank=True, null=True, default='')
    fecha = models.DateTimeField(blank=True, null=True, default='')
    usuario = models.ForeignKey(
        User, on_delete=models.CASCADE, blank=False, null=False)

    class Meta:
        ordering = ["id"]

    def __str__(self):
        return str(self.id)
    

class Envios(models.Model):
    correlativo = models.IntegerField(blank=False,null=False,default=0)
    tienda = models.CharField(max_length=250,blank=False,null=False)
    origen = models.CharField(max_length=250,blank=False,null=False)
    destino = models.CharField(max_length=250,blank=False,null=False)
    total = models.DecimalField(max_digits=12,decimal_places=2,blank=True,null=True,default=0.00)
    observacion = models.CharField(max_length=250,blank=False,null=False)
    fecha_envio = models.DateField(blank=False,null=False,auto_now_add=True)
    fecha = models.DateTimeField(blank=False,null=False,auto_now_add=True)
    usuario = models.ForeignKey(User,on_delete=models.CASCADE,blank=False,null=False)
    token = models.UUIDField(default=uuid.uuid4, editable=False)
    estado = models.IntegerField(blank=False,null=False,default=0)
    

    class Meta:
        ordering = ['id']

    def __str__(self):
        return str(self.correlativo)


class DetalleEnvios(models.Model):
    correlativo = models.ForeignKey(Envios,on_delete=models.CASCADE,blank=False,null=False)
    id_prod = models.ForeignKey(Producto,on_delete=models.CASCADE,blank=False,null=False)
    tienda = models.CharField(max_length=250,blank=True,null=True,default='')
    cantidad = models.IntegerField(blank=False,null=False,default=0)
    precio_compra = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False)
    precio_venta = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False)
    total = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False)
    observacion = models.CharField(max_length=250,blank=False,null=False)
    fecha = models.DateTimeField(blank=False,null=False,auto_now_add=True)
    usuario = models.ForeignKey(User,on_delete=models.CASCADE,blank=False,null=False)
    token = models.UUIDField(default=uuid.uuid4, editable=False)
    estado = models.IntegerField(blank=False,null=False,default=0)

    class Meta:
        ordering = ['id']

    def __str__(self):
        return str(self.correlativo)
    




class Vales(models.Model):
    correlativo = models.IntegerField(blank=False,null=False,default=0)
    tienda = models.CharField(max_length=250,blank=False,null=False)
    origen = models.CharField(max_length=250,blank=False,null=False)
    destino = models.CharField(max_length=250,blank=False,null=False)
    total = models.DecimalField(max_digits=12,decimal_places=2,blank=True,null=True,default=0.00)
    observacion = models.CharField(max_length=250,blank=False,null=False)
    fecha_envio = models.DateField(blank=False,null=False,auto_now_add=True)
    fecha = models.DateTimeField(blank=False,null=False,auto_now_add=True)
    usuario = models.ForeignKey(User,on_delete=models.CASCADE,blank=False,null=False)
    token = models.UUIDField(default=uuid.uuid4, editable=False)
    estado = models.IntegerField(blank=False,null=False,default=0)
    

    class Meta:
        ordering = ['id']

    def __str__(self):
        return str(self.correlativo)


class DetalleVales(models.Model):
    correlativo = models.ForeignKey(Vales,on_delete=models.CASCADE,blank=False,null=False)
    id_prod = models.ForeignKey(Producto,on_delete=models.CASCADE,blank=False,null=False)
    tienda = models.CharField(max_length=250,blank=True,null=True,default='')
    cantidad = models.IntegerField(blank=False,null=False,default=0)
    precio_compra = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False)
    precio_venta = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False)
    total = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False)
    observacion = models.CharField(max_length=250,blank=False,null=False)
    fecha = models.DateTimeField(blank=False,null=False,auto_now_add=True)
    usuario = models.ForeignKey(User,on_delete=models.CASCADE,blank=False,null=False)
    token = models.UUIDField(default=uuid.uuid4, editable=False)
    estado = models.IntegerField(blank=False,null=False,default=0)

    class Meta:
        ordering = ['id']

    def __str__(self):
        return str(self.correlativo)
    

class Movimientos(models.Model):
    doc = models.BigIntegerField(blank=True,null=True,default=0)
    tipo = models.CharField(max_length=255,blank=True,null=True,default='')
    tienda = models.CharField(max_length=255,blank=True,null=True,default='')
    id_prod = models.IntegerField(blank=True,null=True,default=0)
    prod = models.CharField(max_length=255,blank=True,null=True,default='')
    ultimo = models.IntegerField(blank=True,null=True,default=0)
    entrada = models.IntegerField(blank=True,null=True,default=0)
    salida = models.IntegerField(blank=True,null=True,default=0)
    actual = models.IntegerField(blank=True,null=True,default=0)
    obs = models.CharField(max_length=255,blank=True,null=True,default='')
    fecha = models.DateTimeField(blank=False,null=False,auto_now_add=True)
    usuario = models.ForeignKey(User,on_delete=models.CASCADE,blank=False,null=False)


    class Meta:
        ordering = ['id']

    def __str__(self):
        return str(self.doc)
    
