{% extends 'Base/base.html' %}
{% block title %}Consulta de Ventas Por Fecha{% endblock %}

{% block content %}

{% if messages %}
{% for message in messages %}
<script>
    Swal.fire({
        "title": "Informacion Sistema",
        "text": "{{message}}",
        "icon": "{{message.tags}}"
    })
</script>
{% endfor %}
{% endif %}

<div class="content-wrapper">

    <div class="container-xxl flex-grow-1 container-p-y">
      <a href="{% url 'Consulta' %}"><button class="btn btn-info">Regresar</button></a><br></br>

        <!-- Basic Layout -->
        <div class="col-md-12">
            <div class="card md-6">
                <div class="card-header d-flex align-items-center justify-content-between">
                    <h5 class="mb-0">Consulta de Rentabilidad en Todas Las Sucursales</h5>
                    <small class="text-muted float-end">Consulta de Rentabilidad en Sucursales</small>
                </div>
                <div class="card-body">
                    <form action="#" method="POST" enctype="multipart/form-data">{% csrf_token %}
                        <div class="row">
                            <div class="col-md-4">
                                <label>Tienda</label>
                                {% if user.rol == 'admin' %}
                                <select name="tienda" class="form-control" required>
                                    <option value="Todas">Todas</option>
                                    <option value="Estanzuela">Estanzuela</option>
                                    <option value="Teculutan">Teculutan</option>
                                    <option value="Zacapa">Zacapa</option>
                                    <option value="Santa Cruz">Santa Cruz</option>
                                </select>
                                {% else %}
                                <select name="tienda" class="form-control" required>
                                  <option value="{{user.tienda}}">{{user.tienda}}</option>
                              </select>
                                {% endif %}
                            </div>
                            <div class="col-md-4">
                              <label>Tipo</label>
                              <select name="tipo" class="form-control" required>
                                  <option value="FEL">Factura</option>
                                  <option value="FEL-Servicio">Fel-Servicio</option>
                              </select>
                          </div>
                            <div class="col-md-4">
                                <label>Fecha Inicio</label>
                                <input type="date" class="form-control" required name="inicio">
                            </div>
                            <div class="col-md-4">
                                <label>Fecha Fin</label>
                                <input type="date" class="form-control" required name="fin">
                            </div>
                            
                        </div><br>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary">Consultar</button>
                                <button type="reset" class="btn btn-danger">Cancelar</button>
                            </div>
                        </div>
                    
                    </form>
            </div>


        </div>

        {% if b %}

        <div class="content-wrapper">

            <div class="container-xxl flex-grow-1 container-p-y">
          
              <div class="row" style="background-color: white;">
                <!-- Basic Layout -->
                <div class="col-md-12">
                    <div class="card-header d-flex align-items-center justify-content-between">
                      <h5 class="mb-0">Listado de Detallada</h5>
                      {% if f == "FEL" %}
                      <div style="display: grid; grid-template-columns: repeat(4,1fr);"><br>
                        <p>Tienda<strong style="color: red;"> {{t}}</strong></p>
                        <p>Ventas Hechas<strong style="color: red;"> {{tvs}}</strong></p>
                        <p>Total de Utilidades<strong style="color: red;"> Q.{{suma}}</strong></p>
                      </div>
                      {% else %}
                      <div style="display: grid; grid-template-columns: repeat(4,1fr);"><br>
                        <p>Tienda<strong style="color: red;"> {{t}}</strong></p>
                        <p>Servicios Hechos<strong style="color: red;"> {{tvs}}</strong></p>
                        <p>Total de Utilidades<strong style="color: red;"> Q.{{suma}}</strong></p>
                      </div>
                      {% endif %}
                      
                    </div>
                    <div class="card-body">
                      <div class="table-responsive-sm">
          
                        <table class="table table-bordered table-sm order-table" style="display: block; height: 350px; overflow: auto;">
                          <thead>
                            <tr>
                              <th>Factura</th>
                              <th>Cliente</th>
                              <th>Fecha</th>
                              <th>Prod</th>
                              <th>Total Costo Venta</th>
                              <th>Total Costo Compra</th>
                              <th>Utilidad</th>
                              <th>Tienda</th>
                            </tr>
                          </thead>
                          <tbody >
                            {% for b in busqueda %}
                            <tr>
                              {% if b.tienda == "Estanzuela" %}
                              <td>{{b.ie}}</td>
                              {% elif b.tienda == "Teculutan" %}
                              <td>{{b.it}}</td>
                              {% elif b.tienda == "Zacapa" %}
                              <td>{{b.iz}}</td>
                              {% elif b.tienda == "Santa Cruz" %}
                              <td>{{b.ist}}</td>
                              {% else %}
                              <td>Error</td>
                              {% endif %}
                              <td>{{b.cliente}}</td>
                              <td>{{b.fecha | date:"d-m-Y" }}</td>
                              <td>{{b.id}}</td>
                              <td>Q.{{b.totalv}}</td>
                              <td>Q.{{b.totalc}}</td>
                              <td>Q.{{b.utilidad}}</td>
                              <td>{{b.tienda}}</td>
                            </tr>
                            {% empty %}
                            <caption>SIN RESULTADOS</caption>
                            {% endfor %}
                            <tr>
                              <td>TOTAL</td>
                              <td></td>
                              <td></td>
                              <td></td>
                              <td></td>
                              <td></td>
                              <td>Q.{{suma}}</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                  
                      
                    </div>
                  
                </div>
          
              </div>
          
            </div>
          
          </div>
          
      
        
        {% else %}

        {% endif %}  

    </div>

</div>



{% endblock %}