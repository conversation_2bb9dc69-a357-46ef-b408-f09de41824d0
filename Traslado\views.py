from decimal import Decimal
from django.shortcuts import render, redirect
from datetime import datetime
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from Traslado.models import Traslado,DetalleTraslado
from Traslado.forms import TrasladoForm, UpdateTrasladoForm
from Proveedor.models import Proveedor
from Producto.models import Producto,Bitacora
from user.models import User
from django.db.models import Q


@login_required
def lista(request):
     lista = Traslado.objects.all().order_by('-traslado')
     return render(request,'Traslado/lista.html',{'lista':lista})


@login_required
def nuevo(request):
    prov = Proveedor.objects.all()
    form = TrasladoForm()

    if request.method == "POST":
        form = TrasladoForm(request.POST)
        if form.is_valid():
            try:
                t = Traslado()
                t.traslado = form.cleaned_data['traslado']
                t.id_prov = Proveedor.objects.get(nit=request.POST['id_prov'])
                t.fecha = form.cleaned_data['fecha']
                t.usuario = User.objects.get(id=request.user.id)
                if request.user.rol == "admin":
                      t.tienda = request.POST['tienda']
                else:
                      t.tienda = request.user.tienda      
                t.save()
                messages.success(request,f'Traslado {t.traslado} Ingresada Correctamente!')
                return redirect('DetalleTraslado',t.traslado)
            except:
                messages.error(request,f'Traslado {t.traslado} NO Fue Ingresada!')
                return redirect('NuevoTraslado')


    return render(request,'Traslado/nuevo.html',{'form':form,'prov':prov})



@login_required
def detalle(request,t):
    tra = Traslado.objects.get(traslado=t)
    detalles = DetalleTraslado.objects.filter(traslado=t)

    if request.method == 'POST':
        
        if 'buscar' in request.POST:
            if request.user.rol == "admin":
                  
                if request.POST['tipo'] == 'nombre':
                    if Producto.objects.filter(
                                Q(nombre__icontains=request.POST['buscar']),tienda=tra.tienda).exists():
                            b = True
                            buscar = Producto.objects.filter(
                                Q(nombre__icontains=request.POST['buscar']),tienda=tra.tienda)
                    else:
                            b = False
                            buscar = "NO EXISTE ESTE PRODUCTO VERIFIQUE"
                else:
                    if Producto.objects.filter(
                                Q(id=request.POST['buscar']),tienda=tra.tienda).exists():
                            b = True
                            buscar = Producto.objects.filter(
                                Q(id=request.POST['buscar']),tienda=tra.tienda)
                    else:
                            b = False
                            buscar = "NO EXISTE ESTE PRODUCTO VERIFIQUE"            

                return render(request,'Traslado/detalletraslado.html', {'t': tra,'d':detalles,'b':b,'buscar':buscar})
            else:
                if request.POST['tipo'] == 'nombre':
                    if Producto.objects.filter(
                                Q(nombre__icontains=request.POST['buscar']),tienda=request.user.tienda).exists():
                            b = True
                            buscar = Producto.objects.filter(
                                Q(nombre__icontains=request.POST['buscar']),tienda=request.user.tienda)
                    else:
                            b = False
                            buscar = "NO EXISTE ESTE PRODUCTO VERIFIQUE"
                else:
                    if Producto.objects.filter(
                                Q(id=request.POST['buscar']),tienda=request.user.tienda).exists():
                            b = True
                            buscar = Producto.objects.filter(
                                Q(id=request.POST['buscar']),tienda=request.user.tienda)
                    else:
                            b = False
                            buscar = "NO EXISTE ESTE PRODUCTO VERIFIQUE"            

                return render(request,'Traslado/detalletraslado.html', {'t': tra,'d':detalles,'b':b,'buscar':buscar})

        elif 'agregar' in request.POST:
            #try:
                prod = Producto.objects.get(id=request.POST['id'])
                d = DetalleTraslado()
                d.traslado = Traslado.objects.get(traslado=t)
                d.id_prod = Producto.objects.get(id=prod.id)
                d.compra_antes = prod.precio_compra
                d.compra_ahora = Decimal(request.POST['nuevocompra'])
                d.venta_antes = prod.precio_venta
                d.venta_ahora = Decimal(request.POST['nuevoventa'])
                d.stock_antes = prod.stock
                d.stock_ahora = prod.stock+int(request.POST['cantidad'])
                d.cantidad = int(request.POST['cantidad'])
                d.total = d.cantidad*d.compra_ahora
                d.fecha = datetime.today()
                d.usuario = User.objects.get(id=request.user.id)
                d.estado = 1
                d.save()

                if request.user.rol == "admin":
                        # id, prod, t, d, h, i, s, hy, td, u
                        bitacora(prod.id, prod.nombre, 'Consignacion', t, prod.stock, d.cantidad,
                                 0, prod.stock+d.cantidad, prod.tienda, request.user.username)
                else:
                        # id, prod, t, d, h, i, s, hy, td, u
                        bitacora(prod.id, prod.nombre, 'Consignacion', t, prod.stock, d.cantidad,
                                 0, prod.stock+d.cantidad, request.user.tienda, request.user.username)
                        
                Producto.objects.filter(id=prod.id).update(stock=prod.stock+d.cantidad,ingreso=prod.ingreso+d.cantidad,precio_compra=d.compra_ahora,precio_venta=d.venta_ahora)
                messages.success(request,f'Se Ingreso {d.cantidad} de Producto {d.id_prod.nombre}!')
                return redirect('DetalleTraslado',t)
            #except:
                #messages.error(request,f'Error Al Ingresar {d.id_prod.nombre} Revise Datos!')
                #return redirect('DetalleTraslado',t)
        
        elif 'quitar' in request.POST:
            try:
                datos = DetalleTraslado.objects.get(id=request.POST['corr'],traslado=t) 
                prod = Producto.objects.get(id=datos.id_prod.id)
                Producto.objects.filter(id=datos.id_prod.id).update(stock=prod.stock-datos.cantidad,ingreso=prod.ingreso-datos.cantidad,precio_compra=datos.compra_antes,precio_venta=datos.venta_antes)
                
                if request.user.rol == "admin":
                        # id, prod, t, d, h, i, s, hy, td, u
                        bitacora(prod.id, prod.nombre, 'Quitado-Consignacion', t, prod.stock, 0,datos.cantidad, prod.stock-datos.cantidad, prod.tienda, request.user.username)
                        datos.delete()
                else:
                        # id, prod, t, d, h, i, s, hy, td, u
                        bitacora(prod.id, prod.nombre, 'Quitado-Consignacion', t, prod.stock, 0,datos.cantidad, prod.stock-datos.cantidad, request.user.tienda, request.user.username)
                        datos.delete()
                
                messages.success(request,f'Se Quito {datos.cantidad} de Producto {datos.id_prod.nombre} Se Actualizo Inventario!')
                return redirect('DetalleTraslado',t)
            except:
                messages.error(request,f'NO Se Pudo Quitar {datos.cantidad} de Producto {datos.id_prod.nombre} NO Actualizo Inventario!')
                return redirect('DetalleTraslado',t)

        elif 'terminar' in request.POST:
            messages.success(request,f'Ingreso de Producto en Consignacion {t} Terminada Exitosamente!')
            return redirect('NuevoTraslado')
    



    return render(request,'Traslado/detalletraslado.html',{'t':tra,'d':detalles})




@login_required
def actualizar(request, t):
    tras = Traslado.objects.get(traslado=t)
    prov = Proveedor.objects.all().order_by('nit')
    if request.method == 'GET':
        
            form = UpdateTrasladoForm(instance=tras)    
    else:
            form = UpdateTrasladoForm(request.POST, instance=tras)

            if form.is_valid():
                try:
                    tras.usuario = User.objects.get(id=request.user.id)
                    form.save()
                    messages.success(
                        request, f'Consignacion {tras.traslado} Modificada Exitosamente!')
                    return redirect('ListaTraslado')
                        
                    
                except:
                    messages.error(request, f'No Se Pudo Modificar {tras.traslado}!')
                    return redirect('ListaTraslado')
                    

    return render(request, 'Traslado/actualizar.html', {'form': form, 'prov': prov})



@login_required
def darbaja(request,t):
         
    if Traslado.objects.filter(traslado=t).exists():
        for d in DetalleTraslado.objects.filter(traslado=t,estado=1):
            prod = Producto.objects.get(id=d.id_prod.id)
            
            if request.user.rol == "admin":
                        # id, prod, t, d, h, i, s, hy, td, u
                        bitacora(d.id_prod, prod.nombre, 'Consignacion-Baja-Prod', d.traslado, prod.stock, 0,
                                 d.cantidad, prod.stock-d.cantidad, prod.tienda, request.user.username)
            else:
                        # id, prod, t, d, h, i, s, hy, td, u
                        bitacora(d.id_prod, prod.nombre, 'Consignacion-Baja-Prod', d.traslado, prod.stock, 0,
                                 d.cantidad, prod.stock-d.cantidad, request.user.tienda, request.user.username)
                        
            Producto.objects.filter(id=prod.id).update(stock=prod.stock-d.cantidad,ingreso=prod.ingreso-d.cantidad,precio_compra=d.compra_antes,precio_venta=d.venta_antes)
            DetalleTraslado.objects.filter(traslado=t,estado=1).update(estado=99)


        Traslado.objects.filter(traslado=t,estado=1).update(estado=99)    
        messages.success(request,f'Productos de Consignacion # {t} Fueron Dados de Baja Inventario de Estos Productos Vuelve a Precios y Stock Anterior!')
        return redirect('ListaTraslado')
    
    else:
        messages.error(request,f'Error Al Dar de Baja Consignacion # {t}!')
        return redirect('ListaTraslado')
    

def bitacora(id, prod, t, d, h, i, s, hy, td, u):

    b = Bitacora()
    b.id_prod = id
    b.prod = prod
    b.tipo = t
    b.doc = d
    b.habia = h
    b.ingreso = i
    b.salio = s
    b.hay = hy
    b.tienda = td
    b.fecha = str(datetime.today())
    b.usuario = u
    b.save()           