{% extends 'Base/base.html' %}
{% block title %}Detalle Venta{% endblock %}

{% block content %}

{% if messages %}
{% for message in messages %}
<script>
    Swal.fire({
        "title": "Informacion Sistema",
        "text": "{{message}}",
        "icon": "{{message.tags}}"
    })
</script>
{% endfor %}
{% endif %}



<div class="content-wrapper">

    <div class="container-xxl flex-grow-1 container-p-y">
        <form action="" method="POST" enctype="multipart/form-data">{% csrf_token %}
<div class="row">
    <div class="col-md-2">
        <label for="">Correlativo de Tienda</label>
        <input type="text" class="form-control" name="numero" required value="0">
    </div>
    <div class="col-md-4">
        <label for="">Cliente</label>
        <input type="text" class="form-control" name="cliente" required>
    </div>
    <div class="col-md-2">    
        <label for="">Cantidad</label>
        <input type="number" class="form-control" name="cantidad" required>
    </div>
    <div class="col-md-2">    
        <label for="">Precio</label>
        <input type="number" class="form-control" name="precio" required>
    </div>    
</div>  
        <div class="row">
            <div class="col-md-12">
                <label for="">Detalle de Servicio</label>
                <input type="text" class="form-control" name="detalle" required>
            </div>
        
        <div class="col-md-2">
        <label for="">Tienda</label>
        <input type="text" class="form-control" name="tienda" required readonly value="{{user.tienda}}">
        </div>
    </div>    
        
    <br>
    {% if b %}
    <a href="" class="btn btn-danger">PDF</a>
    {% else %}
    <button type="submit" class="btn btn-success">Generar</button>    
    
    {% endif %}
    </form>
      
    </div>

</div>  







    {% endblock %}