from django import forms
from .models import Gasto


class GastoForm(forms.ModelForm):
   
    class Meta:
        model = Gasto
        fields = ['nombre','cantidad','precio','total']

        widgets = { 
            'nombre': forms.TextInput(attrs={'class': 'form-control','require':True,'autofocus': True,'placeholder':'Nombre de Gasto'}),
            'cantidad': forms.TextInput(attrs={'class': 'form-control','require':True,'placeholder':'Cantidad','id':'cantidad'}),
            'precio': forms.TextInput(attrs={'class': 'form-control','require':True,'placeholder':'Precio de Gasto','id':'precio'}),
            'total': forms.TextInput(attrs={'class': 'form-control','require':True,'placeholder':'Total de Gasto','id':'total','readonly':True}),
        }


class UpdateGastoForm(forms.ModelForm):
   
    class Meta:
        model = Gasto
        fields = ['nombre','cantidad','precio','total']

        widgets = { 
            'nombre': forms.TextInput(attrs={'class': 'form-control','require':True,'autofocus': True,'placeholder':'Nombre de Gasto'}),
            'cantidad': forms.TextInput(attrs={'class': 'form-control','require':True,'placeholder':'Cantidad','id':'cantidad'}),
            'precio': forms.TextInput(attrs={'class': 'form-control','require':True,'placeholder':'Precio de Gasto','id':'precio'}),
            'total': forms.TextInput(attrs={'class': 'form-control','require':True,'placeholder':'Total de Gasto','id':'total','readonly':True}),
        }



