from decimal import Decimal
from django.shortcuts import render,redirect
from datetime import datetime
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from Venta.models import Venta,Detalle
from Gasto.models import Gasto
from Producto.models import Producto
from Cliente.models import Cliente
from user.models import User
from django.db.models import Q
import uuid
from django.db.models import Sum


@login_required
def analisisgeneral(request):
    # general
    ventas = Venta.objects.filter(Q(estado=1) & Q(tipo="FEL") | Q(tipo="PROFORMA")).aggregate(total=Sum('total'))
    gastos = Gasto.objects.all().aggregate(tota=Sum('total'))

    # hoy
    ventashoy = Venta.objects.filter(Q(estado=1) & Q(tipo="FEL") | Q(tipo="PROFORMA") & Q(fecha=datetime.today())).aggregate(totalhoy=Sum('total'))
    gastoshoy = Gasto.objects.filter(fecha=datetime.today()).aggregate(totahoy=Sum('total'))

    # notas credito
    credito = Venta.objects.filter(Q(estado=3) & Q(tipo="NOTA CREDITO")).aggregate(credit=Sum('total'))
    creditohoy = Venta.objects.filter(Q(estado=3) & Q(tipo="NOTA CREDITO") & Q(fecha=datetime.today())).aggregate(credithoy=Sum('total'))
    
    if ventas['total'] == None:
        ventas['total'] = Decimal(0.00)
    else:
        ventas['total']

    if gastos['tota'] == None:
        gastos['tota'] = Decimal(0.00)
    else:
        gastos['tota']      

    totales = ventas['total']-gastos['tota']


    if ventashoy['totalhoy'] == None:
        ventashoy['totalhoy'] = Decimal(0.00)
    else:
        ventashoy['totalhoy']

    if gastoshoy['totahoy'] == None:
        gastoshoy['totahoy'] = Decimal(0.00)
    else:
        gastoshoy['totahoy']  


    if credito['credit'] == None:
        credito['credit'] = Decimal(0.00)
    else:
        credito['credit']

    if creditohoy['credithoy'] == None:
        creditohoy['credithoy'] = Decimal(0.00)
    else:
        creditohoy['credithoy']      

    


    if ventashoy['totalhoy'] == None:
        ventashoy['totalhoy'] = Decimal(0.00)
    else:
        ventashoy['totalhoy']

    if gastoshoy['totahoy'] == None:
        gastoshoy['totahoy'] = Decimal(0.00)
    else:
        gastoshoy['totahoy']          

    totaleshoy = ventashoy['totalhoy']-gastoshoy['totahoy']

    context = {'ventas':ventas['total'],'gastos':gastos['tota'],'total':totales,'ventashoy':ventashoy['totalhoy'],'gastoshoy':gastoshoy['totahoy'],'totalhoy':totaleshoy,'credi':credito['credit'],'credihoy':creditohoy['credithoy']}

    return render(request,'Analisis/analisis.html',context)


