{% extends 'Base/base.html' %}
{% block title %}Lista Ingresos{% endblock %}

{% block content %}

{% if messages %}
{% for message in messages %}
<script>
  Swal.fire({
    "title": "Informacion Sistema",
    "text": "{{message}}",
    "icon": "{{message.tags}}"
  })
</script>
{% endfor %}
{% endif %}

<div class="content-wrapper">

  <div class="container-xxl flex-grow-1 container-p-y">

    <div class="row">
      <!-- Basic Layout -->
      <div class="col-md-14">
        <div class="card md-8">
          <div class="card-header d-flex align-items-center justify-content-between">
            <h5 class="mb-0">Listado de Ingresos</h5>
            <small class="text-muted float-end">Lista Ingresos</small>
          </div>
          <div class="card-body">
            <div class="table-responsive-sm">

              <input class="form-control col-md-3 light-table-filter" data-table="order-table" type="text"
            placeholder="Buscar.." style="border: 1px solid black;" name="buscar" autofocus="buscar"><br>
              
            <table class="table table-bordered order-table">
                <thead>
                  <tr>
                    <th>Factura</th>
                    <th>Proveedor</th>
                    <th>Intems Ingresados</th>
                    <th>Total</th>
                    <th>Fecha</th>
                    <th>Tienda</th>
                    <th>Estado</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for p in f %}
                  <tr>
                    <td>{{p.factura}}</td>
                    <td>{{p.id_prov.nombre}}</td>
                    <td>{{p.cantidad}}</td>
                    <td>Q.{{p.total}}</td>
                    <td>{{p.fecha|date:"d-m-Y"}}</td>
                    <td>{{p.tienda}}</td>
                    {% if p.estado == 1 %}
                    <td>Ingresado</td>
                    {% elif p.estado == 99 %}
                    <td>Anulado</td>
                    {% else %}
                    <td>Error!</td>
                    {% endif %}
                    <td>
                      {% if p.estado == 1 %}
                      <a href="{% url 'DetalleIngresoFactura' p.factura %}">
                        <i style="color: yellowgreen;" class='bx bxs-edit' title="Modificar"></i>
                      </a>
                      <a href="{% url 'BajaIngresoFactura' p.factura %}">
                        <i style="color: red;" class='bx bxs-trash' title="Anular/Baja"></i>
                      </a>
                      {% else %}
                      {% endif %}
                    </td>
                  </tr>
                  {% empty %}
                  <caption>SIN INGRESOS</caption>
                  {% endfor %}

                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

    </div>

  </div>

</div>

<script type="text/javascript">
  (function (document) {
      'use strict';

      var LightTableFilter = (function (Arr) {

          var _input;

          function _onInputEvent(e) {
              _input = e.target;
              var tables = document.getElementsByClassName(_input.getAttribute('data-table'));
              Arr.forEach.call(tables, function (table) {
                  Arr.forEach.call(table.tBodies, function (tbody) {
                      Arr.forEach.call(tbody.rows, _filter);
                  });
              });
          }

          function _filter(row) {
              var text = row.textContent.toLowerCase(), val = _input.value.toLowerCase();
              row.style.display = text.indexOf(val) === -1 ? 'none' : 'table-row';
          }

          return {
              init: function () {
                  var inputs = document.getElementsByClassName('light-table-filter');
                  Arr.forEach.call(inputs, function (input) {
                      input.oninput = _onInputEvent;
                  });
              }
          };
      })(Array.prototype);

      document.addEventListener('readystatechange', function () {
          if (document.readyState === 'complete') {
              LightTableFilter.init();
          }
      });

  })(document);
</script>

{% endblock %}