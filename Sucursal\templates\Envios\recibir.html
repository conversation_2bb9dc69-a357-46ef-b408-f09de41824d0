{% extends 'Base/base.html' %}
{% block title %}Listado Envios Recibidos{% endblock %}

{% block content %}

{% if messages %}
{% for message in messages %}
<script>
  let timerInterval;
Swal.fire({
  title: "Procesando Peticion!",
  html: "Espera Mientras Se Ingresa el Envio al Inventario.",
  timer: 5000,
  timerProgressBar: true,
  didOpen: () => {
    Swal.showLoading();
    const timer = Swal.getPopup().querySelector("b");
    timerInterval = setInterval(() => {
      timer.textContent = `${Swal.getTimerLeft()}`;
    }, 100);
  },
  willClose: () => {
    clearInterval(timerInterval);
  }
}).then((result) => {
  /* Read more about handling dismissals below */
  if (result.dismiss === Swal.DismissReason.timer) {
    Swal.fire({
  title: "Informacion de Sistema",
  text: "Ingreso de Envio Exitoso",
  icon: "success"
});
  }
});  
</script>

{% endfor %}
{% endif %}


<div class="content-wrapper">

  <div class="container-xxl flex-grow-1 container-p-y">

    <div class="row">
      <!-- Basic Layout -->
      <div class="col-md-12">
        <div class="card md-10">
          <div class="card-header d-flex align-items-center justify-content-between">
            <h5 class="mb-0">Listado de Envios Pendientes de Recibir</h5>
            <small class="text-muted float-end">Listado de Envios Pendientes de Recibir</small>
          </div>
          <div class="card-body">
            <div class="table-responsive text-nowrap">

              <input class="form-control col-md-3 light-table-filter" data-table="order-table" type="text"
                placeholder="Buscar.." style="border: 1px solid black;" name="buscar" autofocus="buscar"><br>

              <table class="table table-bordered order-table table-sm">
                <thead>
                  <tr>
                    <th>Envio #</th>
                    <th>Origen</th>
                    <th>Destino</th>
                    <th>Total Envio</th>
                    <th>Fecha Envio</th>
                    <th>Estado Envio</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for s in l %}
                  <tr>
                    {% if s.origen == "Estanzuela" %}
                    <td>{{s.correlativo}}.1</td>
                    {% elif s.origen == "Teculutan" %}
                    <td>{{s.correlativo}}.2</td>
                    {% elif s.origen == "Zacapa" %}
                    <td>{{s.correlativo}}.3</td>
                    {% elif s.origen == "Santa Cruz" %}
                    <td>{{s.correlativo}}.4</td>
                    {% else %}
                    <td>{{s.id}}</td>
                    {% endif %}
                    <td>{{s.origen}}</td>
                    <td>{{s.destino}}</td>
                    <td>Q.{{s.total}}</td>
                    <td>{{s.fecha|date:"d-m-Y H:m:s"}}</td>
                    {% if s.estado == 99 %}
                    <td>Realizado/Enviado</td>
                    {% elif s.estado == 0 %}
                    <td>En Proceso</td>
                    {%elif s.estado == 1 %}
                    <td>Recibido/Aplicado</td>
                    {% else %}
                    <td>Realizando Envio</td>
                    {% endif %}
                    <td>
                      {% if user.rol == "admin" %}

                      <a href="javascript:popUp('{% url 'VerEnvioTda' s.token %}')">
                        <i style="color: rgb(197, 141, 18); font-size: 23px;" class="menu-icon tf-icons bx bx-bullseye"
                          title="Ver Detalles"></i>
                      </a>
                      {% if s.estado == 99 %}
                      Recibido
                      {% elif s.estado == 1 %}
                      Recibido
                      {% else %}
                      Cancelado
                      {% endif %}
                      <a href="{% url 'PDFEnvioTda' s.token %}">
                        <i style="color: purple; font-size: 23px;" class='bx bxs-file-pdf' title="PDF"></i>
                      </a>
                    
                      {% else %}
                      
                      <a href="javascript:popUp('{% url 'VerEnvioTda' s.token %}')">
                        <i style="color: rgb(197, 141, 18); font-size: 23px;" class="menu-icon tf-icons bx bx-bullseye"
                          title="Ver Detalles"></i>
                      </a>

                      {% if s.estado == 99 %}
                      <a href="{% url 'RecibeEnvioTda' s.token %}">
                        <i style="color: green; font-size: 23px;" class='bx bxs-truck' title="Recibir Envio"></i>
                      </a>
                      {% elif s.estado == 1 %}
                      Recibido
                      {% else %}
                      Cancelado
                      {% endif %}

                      <a href="{% url 'PDFEnvioTda' s.token %}">
                        <i style="color: purple; font-size: 23px;" class='bx bxs-file-pdf' title="PDF"></i>
                      </a>

                      {% endif %}
                    </td>
                  </tr>
                  {% empty %}
                  <caption>SIN ENVIOS</caption>
                  {% endfor %}

                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

    </div>

  </div>

</div>

<script type="text/javascript">
  function popUp(URL) {
    window.open(URL, 'Ver Envio', 'toolbar=0,scrollbars=0,location=0,statusbar=0,menubar=0,resizable=1,width=1050,height=500,left = 590,top = 150');
  }
</script>

{% endblock %}