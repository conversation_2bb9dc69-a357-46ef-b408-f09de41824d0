{% extends 'Base/base.html' %}
{% block title %}Detalle Envio{% endblock %}

{% block content %}

{% if messages %}
{% for message in messages %}
<script>
    Swal.fire({
        "title": "Informacion Sistema",
        "text": "{{message}}",
        "icon": "{{message.tags}}"
    })
</script>
{% endfor %}
{% endif %}



<div class="content-wrapper">

    <div class="container-xxl flex-grow-1 container-p-y">

        <div class="row">
            <!-- Basic Layout -->
            <div class="col-md-12">
                <div class="card md-6">
                    <div class="card-header d-flex align-items-center justify-content-between">
                        <h5 class="mb-0">Detalles de Envio</h5>
                        {% if user.tienda == "Estanzuela" %}
                        <small class="text-muted float-end">Envio Actual {{e.correlativo}}.1</small>
                        {% elif user.tienda == "Teculutan" %}
                        <small class="text-muted float-end">Envio Actual {{e.correlativo}}.2</small>
                        {% elif user.tienda == "Zacapa" %}
                        <small class="text-muted float-end">Envio Actual {{e.correlativo}}.3</small>
                        {% else %}
                        <small class="text-muted float-end">Envio Actual {{e.correlativo}}.4</small>
                        {% endif %}
                    </div>
                    <div class="card-body">

                        <label for="DatosCliente" class="form-label label-venta">
                            <h4>DATOS DE
                                ENVIO</h4>
                        </label>
                        <form action="#" method="POST">{% csrf_token %}
                            <div class="row">
                                <div class="col-md-4">
                                    <label for="" class="form-label">Correlativo Envio:</label>
                                    {% if user.tienda == "Estanzuela" %}
                                    <input type="text" class="form-control" value="{{e.correlativo}}.1" readonly>
                                    {% elif user.tienda == "Teculutan" %}
                                    <input type="text" class="form-control" value="{{e.correlativo}}.2" readonly>
                                    {% elif user.tienda == "Zacapa" %}
                                    <input type="text" class="form-control" value="{{e.correlativo}}.3" readonly>
                                    {% else %}
                                    <input type="text" class="form-control" value="{{e.correlativo}}.4" readonly>
                                    {% endif %}
                                </div>
                                <div class="col-md-4">
                                    <label for="" class="form-label">Sucursal Origen:</label>
                                    <input type="text" class="form-control" value="{{e.origen}}" readonly>
                                </div>
                                <div class="col-md-4">
                                    <label for="" class="form-label">Sucursal Destino:</label>
                                    <input type="text" class="form-control" value="{{e.destino}}" readonly>
                                </div>
                                <div class="col-md-12"><br>
                                    <label for="" class="form-label">Observaciones:</label>
                                    <input type="text" class="form-control" name="obs" value="{{e.observacion}}" readonly placeholder="Observaciones">
                                </div>

                                <div class="col-md-12"><br>

                                    <div class="col-md" style="text-align: center;">
                                        <button class="btn btn-info" name="terminar">Finalizar</button>
                                        <button name="descartar" class="btn btn-danger">Descartar</button>
                                    </div><br>

                                </div>
                            </div>
                        </form>

                    </div>&nbsp;

                    <div class="row" style="border-bottom: 2px solid black;" align="center">



                        <div class="col-md-6" style="border-right: 2px solid black;">
                            <label for="">Datos de Venta</label><br>
                            <div class="table-responsive">
                                <table class="table table-bordered table-sm" align="center">
                                    <thead>
                                        <tr align="center">
                                            <th>Envio</th>
                                            <th>Total</th>
                                            <th>Fecha</th>
                                            <th>Verificacion</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr align="center">
                                            {% if user.tienda == "Estanzuela" %}
                                            <td>{{e.correlativo}}.1</td>
                                            {% elif user.tienda == "Teculutan" %}
                                            <td>{{e.correlativo}}.2</td>
                                            {% elif user.tienda == "Zacapa" %}
                                            <td>{{e.correlativo}}.3</td>
                                            {% else %}
                                            <td>{{e.correlativo}}.4</td>
                                            {% endif %}
                                            {% if t == None %}
                                            <td>Q.0.00</td>
                                            {% else %}
                                            <td>Q.{{t}}</td>
                                            {% endif %}
                                            <td>{{e.fecha| date:"d-m-Y H:m:s"}}</td>
                                            {% if tk %}
                                            <td class="table-success" align="center"><i style="font-size: 30px;"
                                                    class='bx bxs-check-shield'></i></td>
                                            {% else %}
                                            <td class="table-danger" align="center"><i style="font-size: 30px;"
                                                    class='bx bxs-bug'></i></td>
                                            {% endif %}
                                        </tr>

                                    </tbody>
                                </table>

                            </div>

                        </div>

                        <div class="col-md-6">
                            <label for="">Busqueda de Productos</label><br>
                            <label for="Buscar">Buscar</label>
                            <form action="#" method="POST">{% csrf_token %}
                                <input type="text" name="buscar" class="form-control" autofocus="buscar"
                                    placeholder="Agregar/Buscar">
                            </form><br>
                            {% if b %}
                            <div style="overflow-y: scroll; max-height: 11rem;">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm">
                                        <thead>
                                            <tr>
                                                <th scope="col">Prod</th>
                                                <th scope="col">Stock</th>
                                                <th scope="col">Precio</th>
                                                <th scope="col">Cant</th>
                                                <th scope="col">Tienda</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for bus in bus %}
                                            <tr>
                                                <td>{{bus.nombre}}</td>
                                                <td>{{bus.stock}}</td>
                                                <td>Q{{bus.precio_venta}}</td>
                                                {% if bus.stock == 0 %}
                                                <td class="table-danger">0</td>
                                                {% else %}
                                                <form action="#" method="POST">{% csrf_token %}
                                                    <input type="hidden" value="{{bus.id}}" name="id">
                                                    <td>
                                                        <div class="row">
                                                            <div class="col-md-6">
                                                                <input type="text" name="cantidad" placeholder="0"
                                                                    size="5">
                                                            </div>
                                                            <div class="col-md-6">
                                                                <button name="agregar" class="btn btn-sm btn-success"><i
                                                                        style="color: white;"
                                                                        class="bx bx-plus-circle"></i></button>
                                                            </div>
                                                        </div>

                                                    </td>
                                                </form>
                                                {% endif %}
                                                <td>{{bus.tienda}}</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            {% else %}
                            <caption>SIN RESULTADOS</caption>
                            {% endif %}




                        </div>
                    </div>



                    <div class="row" align="center">

                        <div class="col-md-12">

                            <label for="">Productos Agregados</label>

                            <div style="height: 12rem; overflow-y: scroll;">
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th scope="col">Cod</th>
                                                <th scope="col">Producto</th>
                                                <th scope="col">Cant</th>
                                                <th scope="col">Precio Uni</th>
                                                <th scope="col">Total</th>
                                                <th scope="col">Tienda</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for d in d %}
                                            <tr>
                                                <th scope="row">{{d.id_prod.id}}</th>
                                                <td>{{d.id_prod.nombre}}</td>
                                                <td>{{d.cantidad}}</td>
                                                <td>Q{{d.precio_venta}}</td>
                                                <td>Q.{{d.total}}</td>
                                                <td>{{d.tienda}}</td>
                                                <form action="#" method="POST">{% csrf_token %}
                                                    <input type="hidden" name="corr" value="{{d.id}}">
                                                    <input type="hidden" name="id" value="{{d.id_inventario}}">
                                                    <td><button name="quitar" class="btn btn-sm btn-danger"><i
                                                                style="color: white;" class="bx bx-x"></i></button>
                                                    </td>
                                                </form>

                                            </tr>
                                            {% empty %}
                                            <caption>SIN PRODUCTOS</caption>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                        </div>

                    </div>

                </div>




            </div>




        </div>
    </div>





    {% endblock %}