{% extends 'Base/base.html' %}
{% block title %}Modificar Gasto{% endblock %}

{% block content %}

{% if messages %}
{% for message in messages %}
<script>
    Swal.fire({
        "title": "Informacion Sistema",
        "text": "{{message}}",
        "icon": "{{message.tags}}"
    })
</script>
{% endfor %}
{% endif %}

<div class="content-wrapper">

    <div class="container-xxl flex-grow-1 container-p-y">

        <div class="row">
            <!-- Basic Layout -->
            <div class="col-md-10">
                <div class="card md-6">
                    <div class="card-header d-flex align-items-center justify-content-between">
                        <h5 class="mb-0">Formulario Modificar Gasto</h5>
                        <small class="text-muted float-end">Gastos</small>
                    </div>
                    <div class="card-body">
                        <form action="#" method="POST" enctype="multipart/form-data">{% csrf_token %}
                            <div class="row">
                                <div class="col-md-4">
                                    <label>Nombre de Gasto</label>
                                    {{form.nombre}}
                                </div>
                                <div class="col-md-4">
                                    <label>Cantidad</label>
                                    {{form.cantidad}}
                                </div>
                                <div class="col-md-4">
                                    <label>Precio de Gasto</label>
                                    {{form.precio}}
                                </div>
                            </div><br>

                            <div class="row">

                                <div class="col-md-4">
                                    <label>Total de Gasto</label>
                                    {{form.total}}
                                </div>
                                <div class="col-md-4">
                                    <label>Fecha</label>
                                    <input type="text" class="form-control" readonly value="{% now 'd-m-Y' %}" />
                                </div>
                                <div class="col-md-4">
                                    <label>Usuario</label>
                                    <input type="text" class="form-control" readonly value="{{user.username}}" />
                                </div>

                            </div><br>


                            <div class="row">
                                <div class="col-md-4">
                                    <button type="submit" class="btn btn-primary">Guardar</button>
                                    <button type="reset" class="btn btn-danger">Cancelar</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

        </div>

    </div>

</div>


<script>
    let total = document.getElementById("total")
    let cantidad = document.getElementById("cantidad")
    let precio = document.getElementById("precio")

    precio.addEventListener("change", () => {
        total.value = parseFloat(cantidad.value, 2) * parseFloat(precio.value, 2)

    })

</script>


{% endblock %}