from django.db import models
from user.models import User
from Producto.models import Producto
import uuid

class Venta(models.Model):
    factura = models.BigAutoField(primary_key=True,blank=False,null=False)
    nit = models.CharField(max_length=15,blank=True,null=True,default='CF')
    nombre = models.CharField(max_length=250,blank=True,null=True)
    direccion = models.CharField(max_length=850,blank=True,null=True)
    tipo = models.CharField(max_length=250,blank=True,null=True)# tipo de venta
    link = models.CharField(max_length=250,blank=True,null=True)# link pdf factura fel
    numero = models.BigIntegerField(blank=True,null=True)
    serie = models.CharField(max_length=250,blank=True,null=True)
    anula = models.CharField(max_length=450,blank=True,null=True)
    fecha_fel = models.CharField(max_length=250,blank=False,null=False)
    total = models.DecimalField(max_digits=10,decimal_places=2,blank=True,default=0.00)
    fecha = models.DateField(blank=False,null=False)
    estado = models.IntegerField(blank=False,null=False,default=0)
    usuario = models.ForeignKey(User,on_delete=models.CASCADE,blank=False,null=False)
    token = models.UUIDField(default=uuid.uuid4, editable=False)
    tienda = models.CharField(max_length=250, blank=True, null=True, default='')
    interno_estanzuela = models.IntegerField(blank=True,null=True,default=0)
    interno_tecu = models.IntegerField(blank=True,null=True,default=0)
    interno_zacapa = models.IntegerField(blank=True,null=True,default=0)
    interno_rio_hondo = models.IntegerField(blank=True,null=True,default=0)# nuevo
    pro_estanzuela = models.IntegerField(blank=True,null=True,default=0)
    pro_tecu = models.IntegerField(blank=True,null=True,default=0)
    pro_zacapa = models.IntegerField(blank=True,null=True,default=0)
    pro_rio_hondo = models.IntegerField(blank=True,null=True,default=0)# nuevo

    class Meta:
       ordering = ["factura"]


    def __str__(self):
        return f"{str(self.factura)}"

        
class Detalle(models.Model):
    factura = models.ForeignKey(Venta,on_delete=models.CASCADE,blank=False,null=False)
    id_prod = models.ForeignKey(Producto,on_delete=models.CASCADE,blank=False,null=False)
    cantidad = models.IntegerField(blank=False,null=False)
    precio = models.DecimalField(max_digits=10,decimal_places=2,blank=False,null=False)
    total = models.DecimalField(max_digits=10,decimal_places=2,blank=False)
    subtotal = models.DecimalField(max_digits=10,decimal_places=2,blank=True,default=0.00)
    descuento = models.DecimalField(max_digits=10,decimal_places=2,blank=True,default=0.00)
    usuario = models.CharField(max_length=200,blank=False,null=False)
    fecha = models.DateField(blank=False,null=False)
    estado = models.IntegerField(blank=False,null=False,default=1)
    token = models.UUIDField(default=uuid.uuid4, editable=False)
    detalle_ser = models.CharField(max_length=2200,blank=True,null=True,default='')


    class Meta:
       ordering = ["factura"]


    def __str__(self):
        return f"{str(self.factura)}"        
