{% extends 'Base/base.html' %}
{% block title %}Detalle Producto{% endblock %}

{% block content %}

{% if messages %}
{% for message in messages %}
<script>
    Swal.fire({
        "title": "Informacion Sistema",
        "text": "{{message}}",
        "icon": "{{message.tags}}"
    })
</script>
{% endfor %}
{% endif %}



<div class="content-wrapper">

    <div class="container-xxl flex-grow-1 container-p-y">

        <div class="row">
            <!-- Basic Layout -->
            <div class="col-md-12">
                <div class="card md-6">
                    <div class="card-header d-flex align-items-center justify-content-between">
                        <h5 class="mb-0">Consulta Gastos</h5>
                        <small class="text-muted float-end"></small>
                    </div>
                    <div class="card-body">

                        
            
            <div class="col-md-2 mb-4 order-0">
                <div class="card">
                    <div class="card-body">
                        <div class="card-title d-flex align-items-start justify-content-between">
                            <div class="avatar flex-shrink-0">
                                <i style="font-size: 30px;" class='bx bx-money'></i>
                            </div>

                        </div>
                        <span class="fw-semibold d-block mb-1">Gastos</span>
                        <h3 class="card-title mb-2">
                            Q.{{gasto}}
                        </h3>

                    </div>
                </div>
            </div>


            <div class="col-md-2 mb-4 order-0">
                <div class="card">
                    <div class="card-body">
                        <div class="card-title d-flex align-items-start justify-content-between">
                            <div class="avatar flex-shrink-0">
                                <i style="font-size: 30px;" class='bx bx-money'></i>
                            </div>

                        </div>
                        <span class="fw-semibold d-block mb-1">Gastos Hoy</span>
                        <h3 class="card-title mb-2">
                            Q.{{gastohoy}}
                        </h3>

                    </div>
                </div>
            </div>
    
</div>


    </div>


    {% endblock %}