# Generated by Django 4.1.7 on 2023-11-20 12:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Sucursal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nit', models.CharField(blank=True, default='', max_length=15, null=True)),
                ('nombre', models.Char<PERSON>ield(max_length=550)),
                ('direccion', models.CharField(max_length=1000)),
                ('telefono', models.CharField(blank=True, default='', max_length=9, null=True)),
                ('telefono2', models.CharField(blank=True, default='', max_length=9, null=True)),
                ('correo', models.Char<PERSON>ield(blank=True, default='', max_length=550, null=True)),
                ('fecha', models.DateTimeField(blank=True, default='', null=True)),
                ('usuario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['id'],
            },
        ),
    ]
