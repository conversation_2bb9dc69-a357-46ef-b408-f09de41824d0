from decimal import Decimal
from django.shortcuts import render, redirect
from datetime import datetime
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from Venta.models import Venta, Detalle
from Venta.forms import VentaForm, UpdateVentaForm
from Venta.repote import Comp<PERSON>bante
from Producto.models import Producto
from Cliente.models import Cliente
from user.models import User
from django.db.models import Q
import uuid
from django.db.models import Sum

import emisor
import receptor
import InfileFel
from django.http import HttpResponse


@login_required
def listado(request):

    if request.user.rol == "admin":
        datos = Venta.objects.all().order_by('-factura')
    else:
        datos = Venta.objects.filter(
            tienda=request.user.tienda).order_by('-factura')

    return render(request, 'Venta/lista.html', {'venta': datos})


@login_required
def ver(request, t):
    ver = Venta.objects.get(token=t)
    datos = Detalle.objects.filter(token=t)
    return render(request, 'Venta/ver.html', {'ver': ver, 'detalle': datos})


@login_required
def actualizar(request, id):
    ven = Venta.objects.get(id=id)
    if request.method == 'GET':
        form = UpdateVentaForm(instance=ven)
    else:
        form = UpdateVentaForm(request.POST, instance=ven)

        if form.is_valid():
            try:
                ven.fecha = str(datetime.today().strftime('%Y-%m-%d'))
                ven.usuario = User.objects.get(id=request.user.id)
                form.save()
                messages.success(
                    request, f'Venta Numero {ven.factura} Modificada Exitosamente!')
                return redirect('ListaVenta')
            except:
                messages.error(
                    request, f'No Se Pudo Modificar Venta Numero {ven.factura}!')
                return redirect('ListaVenta')

    return render(request, 'Venta/actualizar.html', {'form': form})


@login_required
def anular(request, id):

    venta = Venta.objects.get(factura=id)

    if venta.tipo == "FEL":
        
        miventa = Venta.objects.get(factura=id)
        midetalle = Detalle.objects.filter(factura=id)
        ver = Cliente.objects.filter(nit=miventa.nit).exists()

        if ver:
            micliente = Cliente.objects.get(nit=miventa.nit)
            for d in midetalle:
                prod = Producto.objects.get(id=d.id_prod.id)
                Producto.objects.filter(id=d.id_prod.id).update(
                    stock=prod.stock+d.cantidad, salio=prod.salio-d.cantidad)

            Cliente.objects.filter(nit=micliente.nit).update(
                compras_contado=micliente.compras_contado+1, total_contado=micliente.total_contado+miventa.total)
            Venta.objects.filter(factura=id).update(estado=2)
            Detalle.objects.filter(factura=id).update(estado=2)
            messages.warning(
                request, f'Venta Anulada Productos Vuelven a Inventario!')
            return redirect('ListaVenta')
        else:
            for d in midetalle:
                prod = Producto.objects.get(id=d.id_prod.id)
                Producto.objects.filter(id=d.id_prod.id).update(
                    stock=prod.stock+d.cantidad, salio=prod.salio-d.cantidad)

            Venta.objects.filter(factura=id).update(estado=2)
            Detalle.objects.filter(factura=id).update(estado=2)
            messages.warning(
                request, f'Venta Anulada Productos Vuelven a Inventario!')
            return redirect('ListaVenta')

    elif venta.tipo == "PROFORMA":

        miventa = Venta.objects.get(factura=id)
        midetalle = Detalle.objects.filter(factura=id)
        ver = Cliente.objects.filter(nit=miventa.nit).exists()

        if ver:
            micliente = Cliente.objects.get(nit=miventa.nit)
            for d in midetalle:
                prod = Producto.objects.get(id=d.id_prod.id)
                Producto.objects.filter(id=d.id_prod.id).update(
                    stock=prod.stock+d.cantidad, salio=prod.salio-d.cantidad)

            Cliente.objects.filter(nit=micliente.nit).update(
                compras_contado=micliente.compras_contado+1, total_contado=micliente.total_contado+miventa.total)
            Venta.objects.filter(factura=id).update(estado=2)
            Detalle.objects.filter(factura=id).update(estado=2)
            messages.warning(
                request, f'Venta Anulada Productos Vuelven a Inventario!')
            return redirect('ListaVenta')
        else:
            for d in midetalle:
                prod = Producto.objects.get(id=d.id_prod.id)
                Producto.objects.filter(id=d.id_prod.id).update(
                    stock=prod.stock+d.cantidad, salio=prod.salio-d.cantidad)

            Venta.objects.filter(factura=id).update(estado=2)
            Detalle.objects.filter(factura=id).update(estado=2)
            messages.warning(
                request, f'Venta Anulada Productos Vuelven a Inventario!')
            return redirect('ListaVenta')

    elif venta.tipo == "NOTA CREDITO":

        miventa = Venta.objects.get(factura=id)
        midetalle = Detalle.objects.filter(factura=id)
        ver = Cliente.objects.filter(nit=miventa.nit).exists()

        if ver:
            micliente = Cliente.objects.get(nit=miventa.nit)
            for d in midetalle:
                prod = Producto.objects.get(id=d.id_prod.id)
                Producto.objects.filter(id=d.id_prod.id).update(
                    stock=prod.stock+d.cantidad,salio=prod.salio-d.cantidad)

            Cliente.objects.filter(nit=micliente.nit).update(
                compras_credito=micliente.compras_credito+1, total_credito=micliente.total_credito+miventa.total)
            Venta.objects.filter(factura=id).update(estado=2)
            Detalle.objects.filter(factura=id).update(estado=2)
            messages.warning(
                request, f'Nota de Credito Anulada Productos Vuelven a Inventario!')
            return redirect('ListaVenta')
        else:
            for d in midetalle:
                prod = Producto.objects.get(id=d.id_prod.id)
                Producto.objects.filter(id=d.id_prod.id).update(
                    stock=prod.stock+d.cantidad,salio=prod.salio-d.cantidad)

            Venta.objects.filter(factura=id).update(estado=2)
            Detalle.objects.filter(factura=id).update(estado=2)
            messages.warning(
                request, f'Nota de Credito Anulada Productos Vuelven a Inventario!')
            return redirect('ListaVenta')

    else:
        pass


@login_required
def nueva(request):
    form = VentaForm()
    if request.method == "POST":
        form = VentaForm(request.POST)
        if form.is_valid():
            if Cliente.objects.filter(nit=form.cleaned_data['nit']).exists():
                c = Cliente.objects.get(nit=form.cleaned_data['nit'])
                v = Venta()
                v.nit = c.nit
                v.nombre = c.nombre
                v.direccion = c.direccion
                v.tipo = form.cleaned_data['tipo']
                v.total = 0.00
                v.token = uuid.uuid4()
                v.tienda = request.user.tienda
                v.estado = 0
                v.fecha = datetime.today()
                v.usuario = User.objects.get(id=request.user.id)
                v.save()
                messages.success(request, f'{v.nombre} Ingresado!')
                return redirect('Detalle', v.token)
            else:
                v = Venta()
                v.nit = 'CF'
                v.nombre = 'Consumidor Final'
                v.direccion = 'Ciudad'
                v.tipo = form.cleaned_data['tipo']
                v.total = 0.00
                v.token = uuid.uuid4()
                v.tienda = request.user.tienda
                v.estado = 0
                v.fecha = datetime.today()
                v.usuario = User.objects.get(id=request.user.id)
                v.save()
                messages.success(request, f'{v.nombre} Ingresado!')
                return redirect('Detalle', v.token)

    return render(request, 'Venta/nueva.html', {'form': form})


@login_required
def detalle(request, t):

    venta = Venta.objects.get(token=t)
    det = Detalle.objects.filter(token=t)
    vercli = Cliente.objects.filter(nit=venta.nit).exists()

    if vercli:
        micli = Cliente.objects.get(nit=venta.nit)
        Venta.objects.filter(token=t).update(
            nit=micli.nit, nombre=micli.nombre, direccion=micli.direccion)
    else:
        Venta.objects.filter(token=t).update(
            nit='CF', nombre='Consumidor Final', direccion='Ciudad')

    # verificacion de token para validar venta
    if str(t) == str(venta.token):
        tok = True
    else:
        tok = False

    if request.method == "POST":

        if tok:

            if 'buscar' in request.POST:

                if request.user.rol == "admin":
                    if request.POST['buscar'] == "":
                        messages.error(
                            request, f'Campo Busqueda No Puede Estar Vacio')
                        return redirect('Detalle', t)
                    else:
                        querys = (Q(id__icontains=request.POST['buscar'].strip()) | Q(
                            nombre__icontains=request.POST['buscar'].strip()))
                        busqueda = Producto.objects.filter(
                            querys)
                        return render(request, 'Venta/detalle.html', {'venta': venta, 'tok': tok, 'b': busqueda, 'd': det})
                else:
                    if request.POST['buscar'] == "":
                        messages.error(
                            request, f'Campo Busqueda No Puede Estar Vacio')
                        return redirect('Detalle', t)
                    else:
                        querys = (Q(id__icontains=request.POST['buscar'].strip()) | Q(
                            nombre__icontains=request.POST['buscar'].strip()))
                        busqueda = Producto.objects.filter(
                            querys, tienda=request.user.tienda)
                        return render(request, 'Venta/detalle.html', {'venta': venta, 'tok': tok, 'b': busqueda, 'd': det})

            elif 'agregarservicio' in request.POST:

                        d = Detalle()
                        d.factura = Venta.objects.get(factura=venta.factura)
                        d.id_prod = Producto.objects.get(id=4505)
                        d.cantidad = int(request.POST['cantidadservicio'])
                        d.precio = Decimal(request.POST['precioservicio'])
                        d.subtotal = d.precio*d.cantidad
                        d.descuento = int(0)
                        d.total = d.subtotal-d.descuento
                        d.fecha = datetime.today()
                        d.usuario = User.objects.get(id=request.user.id)
                        d.token = t
                        d.save()
                        
                        Venta.objects.filter(factura=venta.factura, token=t).update(
                            total=venta.total+((int(request.POST['cantidadservicio'])*d.precio)-d.descuento))
                        messages.success(
                            request, f'Agregando {request.POST["cantidadservicio"]}')
                        return redirect('Detalle', t)


            
            elif 'agregar' in request.POST:

                ver = Producto.objects.get(id=request.POST['id'])

                if request.POST['descuento'] == "":
                    des = Decimal(0.00)
                else:
                    des = Decimal(request.POST['descuento'])    


                if ver.stock >= int(request.POST['cantidad']):

                    if Detalle.objects.filter(id_prod=ver.id, token=t).exists():

                        endetalle = Detalle.objects.get(
                            id_prod=ver.id, token=t)

                        Detalle.objects.filter(id_prod=ver.id, token=t).update(cantidad=endetalle.cantidad+int(
                            request.POST['cantidad']), descuento=des,total=endetalle.total+(ver.precio_venta*int(request.POST['cantidad']))-des,subtotal=endetalle.total+(ver.precio_venta*int(request.POST['cantidad'])))

                        Producto.objects.filter(id=ver.id).update(
                            stock=ver.stock-int(request.POST['cantidad']), salio=ver.salio+int(request.POST['cantidad']))

                        Venta.objects.filter(factura=venta.factura, token=t).update(
                            total=venta.total+((int(request.POST['cantidad'])*ver.precio_venta)-des))

                        messages.success(
                            request, f'Agregando {request.POST["cantidad"]} Unidad de {ver.nombre}')
                        return redirect('Detalle', t)
                    else:

                        d = Detalle()
                        d.factura = Venta.objects.get(factura=venta.factura)
                        d.id_prod = Producto.objects.get(id=ver.id)
                        d.cantidad = int(request.POST['cantidad'])
                        d.precio = ver.precio_venta
                        d.subtotal = d.precio*d.cantidad
                        d.descuento = des
                        d.total = d.subtotal-d.descuento
                        d.fecha = datetime.today()
                        d.usuario = User.objects.get(id=request.user.id)
                        d.token = t
                        d.save()
                        Producto.objects.filter(id=ver.id).update(
                            stock=ver.stock-int(request.POST['cantidad']), salio=ver.salio+int(request.POST['cantidad']))
                        Venta.objects.filter(factura=venta.factura, token=t).update(
                            total=venta.total+((int(request.POST['cantidad'])*ver.precio_venta)-d.descuento))
                        messages.success(
                            request, f'Agregando {request.POST["cantidad"]} Unidad de {ver.nombre}')
                        return redirect('Detalle', t)

                else:
                    messages.error(
                        request, f'Producto {ver.nombre} No Tiene Existencia!')
                    return redirect('Detalle', t)

            elif 'quitar' in request.POST:
                idetalle = Detalle.objects.get(
                    id=request.POST['corr'], token=t)
                elprod = Producto.objects.get(id=idetalle.id_prod.id)
                Venta.objects.filter(factura=venta.factura, token=t).update(
                    total=venta.total-((idetalle.cantidad*idetalle.precio)-idetalle.descuento))
                Producto.objects.filter(id=idetalle.id_prod.id).update(
                    stock=elprod.stock+idetalle.cantidad, salio=elprod.salio-idetalle.cantidad)
                idetalle.delete()
                messages.success(
                    request, f'Se Quitado {idetalle.cantidad} de {elprod.nombre}!')
                return redirect('Detalle', t)
            
            elif 'quitarservicio' in request.POST:
                idetalle = Detalle.objects.get(
                    id=request.POST['corr'], token=t)
                Venta.objects.filter(factura=venta.factura, token=t).update(
                    total=venta.total-((idetalle.cantidad*idetalle.precio)-idetalle.descuento))
                idetalle.delete()
                messages.success(
                    request, f'Se Quitado {idetalle.cantidad}!')
                return redirect('Detalle', t)

            elif 'terminar' in request.POST:
                miventa = Venta.objects.get(token=t)

                if venta.tipo == "FEL":
                    if request.user.tienda == "Estanzuela":
                        clientenoexiste(
                            request.POST['nit'], request.POST['nombre'], request.POST['direccion'], t)
                        miventa = Venta.objects.get(token=t)
                        micliente = Cliente.objects.get(nit=miventa.nit)
                        Cliente.objects.filter(nit=micliente.nit).update(
                            compras_contado=micliente.compras_contado+1, total_contado=micliente.total_contado+miventa.total)
                        fel(request, t)
                        messages.info(request,venta.link)
                        return redirect('NuevaVenta')
                    elif request.user.tienda == "Teculutan":
                        clientenoexiste(
                            request.POST['nit'], request.POST['nombre'], request.POST['direccion'], t)
                        miventa = Venta.objects.get(token=t)
                        micliente = Cliente.objects.get(nit=miventa.nit)
                        Cliente.objects.filter(nit=micliente.nit).update(
                            compras_contado=micliente.compras_contado+1, total_contado=micliente.total_contado+miventa.total)
                        fel2(request, t)
                        messages.info(request,venta.link)
                        return redirect('NuevaVenta')
                    else:
                        clientenoexiste(
                            request.POST['nit'], request.POST['nombre'], request.POST['direccion'], t)
                        miventa = Venta.objects.get(token=t)
                        micliente = Cliente.objects.get(nit=miventa.nit)
                        Cliente.objects.filter(nit=micliente.nit).update(
                            compras_contado=micliente.compras_contado+1, total_contado=micliente.total_contado+miventa.total)
                        fel3(request, t)
                        messages.info(request,venta.link)
                        return redirect('NuevaVenta')

                if venta.tipo == "FEL-Servicio":
                    if request.user.tienda == "Estanzuela":
                        clientenoexiste(
                            request.POST['nit'], request.POST['nombre'], request.POST['direccion'], t)
                        miventa = Venta.objects.get(token=t)
                        micliente = Cliente.objects.get(nit=miventa.nit)
                        Cliente.objects.filter(nit=micliente.nit).update(
                            compras_contado=micliente.compras_contado+1, total_contado=micliente.total_contado+miventa.total)
                        fel(request, t)
                        messages.info(request,venta.link)
                        return redirect('NuevaVenta')
                    elif request.user.tienda == "Teculutan":
                        clientenoexiste(
                            request.POST['nit'], request.POST['nombre'], request.POST['direccion'], t)
                        miventa = Venta.objects.get(token=t)
                        micliente = Cliente.objects.get(nit=miventa.nit)
                        Cliente.objects.filter(nit=micliente.nit).update(
                            compras_contado=micliente.compras_contado+1, total_contado=micliente.total_contado+miventa.total)
                        fel2(request, t)
                        messages.info(request,venta.link)
                        return redirect('NuevaVenta')
                    else:
                        clientenoexiste(
                            request.POST['nit'], request.POST['nombre'], request.POST['direccion'], t)
                        miventa = Venta.objects.get(token=t)
                        micliente = Cliente.objects.get(nit=miventa.nit)
                        Cliente.objects.filter(nit=micliente.nit).update(
                            compras_contado=micliente.compras_contado+1, total_contado=micliente.total_contado+miventa.total)
                        fel3(request, t)
                        messages.info(request,venta.link)
                        return redirect('NuevaVenta')    

                elif venta.tipo == "PROFORMA":
                    miventa = Venta.objects.get(token=t)
                    micliente = Cliente.objects.get(nit=miventa.nit)
                    Cliente.objects.filter(nit=micliente.nit).update(
                        compras_contado=micliente.compras_contado+1, total_contado=micliente.total_contado+miventa.total)
                    Venta.objects.filter(token=t).update(
                        nit=request.POST['nit'], nombre=request.POST['nombre'], direccion=request.POST['direccion'], estado=1)
                    clientenoexiste(
                        request.POST['nit'], request.POST['nombre'], request.POST['direccion'], t)
                    messages.success(request, f'{venta.factura}')
                    return redirect('NuevaVenta')

                elif venta.tipo == "NOTA CREDITO":
                    miventa = Venta.objects.get(token=t)
                    micliente = Cliente.objects.get(nit=miventa.nit)
                    Cliente.objects.filter(nit=micliente.nit).update(
                        compras_credito=micliente.compras_credito+1, total_credito=micliente.total_credito+miventa.total)
                    Venta.objects.filter(token=t).update(
                        nit=request.POST['nit'], nombre=request.POST['nombre'], direccion=request.POST['direccion'], estado=3)
                    clientenoexiste(
                        request.POST['nit'], request.POST['nombre'], request.POST['direccion'], t)
                    messages.success(request, f'{venta.factura}')
                    return redirect('NuevaVenta')

                else:
                    pass

            elif 'descartar' in request.POST:

                # obetenemos la venta
                miventa = Venta.objects.get(token=t)

                # obtener productos en detalle
                midetalle = Detalle.objects.filter(token=t)

                # obtenemos el cliente para restar lo comprado
                ver = Cliente.objects.filter(nit=miventa.nit).exists()

                if ver:
                    micliente = Cliente.objects.get(nit=miventa.nit)
                    # regresamos el inventario a los productos
                    for d in midetalle:
                        # lista productos
                        prod = Producto.objects.get(id=d.id_prod.id)
                        Producto.objects.filter(id=d.id_prod.id).update(
                            stock=prod.stock+d.cantidad, ingreso=prod.ingreso+d.cantidad, salio=prod.salio-d.cantidad)

                    if miventa.tipo == "FEL":
                        midetalle.delete()
                        miventa.delete()
                        messages.warning(
                            request, f'Venta FEL Descartada Productos Vuelven a Inventario!')
                        return redirect('NuevaVenta')
                    
                    elif miventa.tipo == "FEL-Servicio":
                        midetalle.delete()
                        miventa.delete()
                        messages.warning(
                            request, f'Venta FEL-Servicio Descartada!')
                        return redirect('NuevaVenta')

                    elif miventa.tipo == "PROFORMA":
                        midetalle.delete()
                        miventa.delete()
                        messages.warning(
                            request, f'Venta Descartada Productos Vuelven a Inventario!')
                        return redirect('NuevaVenta')

                    elif miventa.tipo == "NOTA CREDITO":
                        midetalle.delete()
                        miventa.delete()
                        messages.warning(
                            request, f'Venta Descartada Productos Vuelven a Inventario!')
                        return redirect('NuevaVenta')

                    else:
                        midetalle.delete()
                        miventa.delete()
                else:

                    for d in midetalle:
                        # lista productos
                        prod = Producto.objects.get(id=d.id_prod.id)
                        Producto.objects.filter(id=d.id_prod.id).update(
                            stock=prod.stock+d.cantidad, ingreso=prod.ingreso+d.cantidad, salio=prod.salio-d.cantidad)

                    if miventa.tipo == "FEL":
                        midetalle.delete()
                        miventa.delete()
                        messages.warning(
                            request, f'Venta FEL Descartada Productos Vuelven a Inventario!')
                        return redirect('NuevaVenta')
                    
                    elif miventa.tipo == "FEL-Servicio":
                        midetalle.delete()
                        miventa.delete()
                        messages.warning(
                            request, f'Venta FEL-Servicio Descartada!')
                        return redirect('NuevaVenta')

                    elif miventa.tipo == "PROFORMA":
                        midetalle.delete()
                        miventa.delete()
                        messages.warning(
                            request, f'Venta Descartada Productos Vuelven a Inventario!')
                        return redirect('NuevaVenta')

                    elif miventa.tipo == "NOTA CREDITO":
                        midetalle.delete()
                        miventa.delete()
                        messages.warning(
                            request, f'Venta Descartada Productos Vuelven a Inventario!')
                        return redirect('NuevaVenta')

                    else:
                        midetalle.delete()
                        miventa.delete()

        else:
            messages.error(
                request, f'Venta Numero {venta.factura} Cancelada Por Falta de Verificacion!')
            Venta.objects.filter(token=t).update(
                estado=99, tipo="Venta Corrupta")
            return redirect('NuevaVenta')

    return render(request, 'Venta/detalle.html', {'venta': venta, 'tok': tok, 'd': det})


def clientenoexiste(nit, nombre, dir, t):

    venta = Venta.objects.get(token=t)
    if venta.tipo == "NOTA CREDITO":
        c = Cliente()
        c.nit = nit
        c.nombre = nombre
        c.direccion = dir
        c.tel = '0000-0000'
        c.compras_contado = 0
        c.total_contado = 0.00
        c.compras_credito = 1
        c.total_credito = venta.total
        c.total_credito_pagado = 0.00
        c.fecha = str(datetime.today().strftime('%Y-%m-%d'))
        c.usuario = User.objects.get(id=venta.usuario.id)
        c.save()
    else:
        c = Cliente()
        c.nit = nit
        c.nombre = nombre
        c.direccion = dir
        c.tel = '0000-0000'
        c.compras_contado = 1
        c.total_contado = venta.total
        c.compras_credito = 0
        c.total_credito = 0.00
        c.total_credito_pagado = 0.00
        c.fecha = str(datetime.today().strftime('%Y-%m-%d'))
        c.usuario = User.objects.get(id=venta.usuario.id)
        c.save()


def pdf(request, f):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        response = HttpResponse(content_type='application/pdf')
        response[
            'Content-Disposition'] = f'attachment; filename="comprobante-venta-#-{f}.pdf"'
        r = Comprobante(f)
        response.write(r.run())
        return response


################# FEL #############################

cont = 0


def fel(request, t):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        factura = Venta.objects.filter(token=t)
        factura_ver = Venta.objects.get(token=t)
        detalle = Detalle.objects.filter(token=t)
        cd = Detalle.objects.filter(token=t).aggregate(
            cantidad=Sum('cantidad'))  # total de la venta
        total = Detalle.objects.filter(token=t).aggregate(
            tot=Sum('total'))  # total de la venta
        miventa = Detalle.objects.filter(token=t).aggregate(
            t=Sum('total'))  # total de la venta
        for f in factura:
            pass

        for d in detalle:
            pass

            Venta.objects.filter(token=t).update(
                nit=request.POST["nit"], nombre=request.POST["nombre"], direccion=request.POST["direccion"], estado=1)
            # miventa = Venta.objects.filter(factura=id).values_list('nit','direccion','negocio')
            # facturar(request,id)

            # crear el DTE a Certificar

            dte_fel_a_certificar = InfileFel.fel_dte()  # aki mandamos el correlativo

            # crear el emisor
            emisor_fel = emisor.emisor()

            # crear el receptor
            receptor_fel = receptor.receptor()

            # crear los totales
            total_fel = InfileFel.totales()

            # totales impuestos
            totales_impuestos = InfileFel.total_impuesto()

            for datoscliente in Venta.objects.filter(token=t):
                print(datoscliente.nit, datoscliente.nombre,
                      datoscliente.direccion, t)

            clientenoexiste(datoscliente.nit,
                            datoscliente.nombre, datoscliente.direccion, t)

            # setear direccion del emisor
            emisor_fel.set_direccion('KILOMETRO 141 5 RUTA A ESQUIPULAS ZONA 4 ESTANZUELA ZACAPA \n TEL. 5774-7328',
                                     '19001', 'Zacapa', 'guatemala', 'GT')  # Cabecera de Negocio

            # setear datos del emisor fel
            # Parametros:  afiliacion_iva, codigo_establecimiento, correo_emisor, nit_emisor, nombre_comercial, nombre_emisor
            emisor_fel.set_datos_emisor('GEN', '1', '<EMAIL>', '96638303',
                                        'SERVICIO AGRICOLA SANTA ROSALIA', 'SERVICIO AGRICOLA SANTA ROSALIA')  # Cabecera de Negocio

            # setear direccion del receptor
            receptor_fel.set_direccion(
                datoscliente.direccion, '19001', 'Zacapa', 'guatemala', 'GT')

            # setear datos del receptor
            receptor_fel.set_datos_receptor(
                '<EMAIL>', datoscliente.nit, datoscliente.nombre)

            # identificador unico del dte del cliente
            dte_fel_a_certificar.set_clave_unica(f'202132{id}')  # no importa

            # setear datos generales
            dte_fel_a_certificar.set_datos_generales(
                'GTQ', f'{datoscliente.fecha}T00:00:00-06:00', 'FACT')  # fecha de venta

            # agregar los datos del emisor
            dte_fel_a_certificar.set_datos_emisor(emisor_fel)
            # agregar los datos del receptor
            dte_fel_a_certificar.set_datos_receptor(receptor_fel)

            # agregar las frases
            dte_fel_a_certificar.frase_fel.set_frase('1', '1')
            dte_fel_a_certificar.frase_fel.set_frase('1', '2')
            # dte_fel_a_certificar.frase_fel.set_frase('1','1','20185687029123456789','2018-10-11')

            num = 1
            acutotal = 0
            acuiva = 0
            miventa = 0
            for item in Detalle.objects.filter(token=t):

                num = num+1

                item_1 = InfileFel.item()

                item_1_impuesto = InfileFel.impuesto()

                # llenar el item con los datos necesarios
                item_1.set_numero_linea(num)  # esto falta
                item_1.set_bien_o_servicio('B')
                item_1.set_cantidad(item.cantidad)
                item_1.set_unidad_medida('UND')  # no se puede cambiar
                item_1.set_descripcion(item.id_prod.nombre)
                item_1.set_precio_unitario(item.precio)
                item_1.set_precio(item.cantidad*item.precio)
                item_1.set_descuento(0)
                item_1.set_total(item.total)

                grav = round((item.total/112)*100, 2)
                iva = round((grav*12)/100, 2)

                # llenar los impuestos del item
                item_1_impuesto.set_monto_impuesto(iva)
                item_1_impuesto.set_monto_gravable(grav)
                item_1_impuesto.set_codigo_unidad_gravable(1)
                item_1_impuesto.set_nombre_corto('IVA')
                item_1.set_impuesto(item_1_impuesto)

                acutotal = acutotal + grav
                acuiva = acuiva + iva
                miventa = miventa + item.total

                # Agregar el item 1
                dte_fel_a_certificar.agregar_item(item_1)

                # agregar el gran total

            total_fel.set_gran_total(miventa)

            # agregar datos del gran total de impuestos

            totales_impuestos.set_nombre_corto('IVA')
            totales_impuestos.set_total_monto_impuesto(acuiva)

            total_fel.set_total_impuestos(totales_impuestos)

            # agregar los totales
            dte_fel_a_certificar.agregar_totales(total_fel)

            # agregar adendas al gusto
            fel_adenda = InfileFel.adenda()
            fel_adenda.nombre = "factura_ruta"
            fel_adenda.valor = "s"
            dte_fel_a_certificar.agregar_adenda(fel_adenda)
            print("#########################################################")

            # contingencia
            # dte_fel_a_certificar.set_acceso('123123')

            # agregando el tipo de personeria
            # dte_fel_a_certificar.set_tipo_personeria('2')

            # realizar el llamado a la certificada
            certificacion_fel = dte_fel_a_certificar.certificar()
            if (certificacion_fel["resultado"]):
                print("UUID:" + certificacion_fel["uuid"])
                print("FECHA:" + certificacion_fel["fecha"])
                print("SERIE:" + certificacion_fel["serie"])
                print("numero:" + str(certificacion_fel["numero"]))
                total = Detalle.objects.all().filter(token=t).aggregate(
                    tot=Sum('total'))  # total de la venta
                messages.info(
                    request, f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}")
                Venta.objects.filter(token=t).update(nit=request.POST["nit"], nombre=request.POST["nombre"], direccion=request.POST["direccion"], fecha_fel=str(
                    certificacion_fel['fecha']), estado=1, link=f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}", anula=certificacion_fel['uuid'], serie=certificacion_fel['serie'], numero=certificacion_fel['numero'], total=total['tot'])
                # Venta.objects.filter(factura=id).update(link=f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}")
                #messages.info(request,factura_ver.link)
                return redirect('NuevaVenta')
                
            else:
                print("No pudo ser certificada")
                print("Descripcion: " + certificacion_fel["descripcion"])

                for error_fel in certificacion_fel["descripcion_errores"]:
                    print("Mensaje Error: " + error_fel["fuente"])
                    print("fuente: " + error_fel["mensaje_error"])
                    print("categoria: " + error_fel["categoria"])
                    print("numeral: " + error_fel["numeral"])
                    print("validacion: " + error_fel["validacion"])

        else:
            pass

        ###################################################################################################################################

    # return render(request,"PagoApp/pagoefectivo.html",{'factura':factura,'detalle':detalle,'cd':cd,'total':total,'id':id})
        


def fel2(request, t):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        factura = Venta.objects.filter(token=t)
        factura_ver = Venta.objects.get(token=t)
        detalle = Detalle.objects.filter(token=t)
        cd = Detalle.objects.filter(token=t).aggregate(
            cantidad=Sum('cantidad'))  # total de la venta
        total = Detalle.objects.filter(token=t).aggregate(
            tot=Sum('total'))  # total de la venta
        miventa = Detalle.objects.filter(token=t).aggregate(
            t=Sum('total'))  # total de la venta
        for f in factura:
            pass

        for d in detalle:
            pass

            Venta.objects.filter(token=t).update(
                nit=request.POST["nit"], nombre=request.POST["nombre"], direccion=request.POST["direccion"], estado=1)
            # miventa = Venta.objects.filter(factura=id).values_list('nit','direccion','negocio')
            # facturar(request,id)

            # crear el DTE a Certificar

            dte_fel_a_certificar = InfileFel.fel_dte()  # aki mandamos el correlativo

            # crear el emisor
            emisor_fel = emisor.emisor()

            # crear el receptor
            receptor_fel = receptor.receptor()

            # crear los totales
            total_fel = InfileFel.totales()

            # totales impuestos
            totales_impuestos = InfileFel.total_impuesto()

            for datoscliente in Venta.objects.filter(token=t):
                print(datoscliente.nit, datoscliente.nombre,
                      datoscliente.direccion, t)

            clientenoexiste(datoscliente.nit,
                            datoscliente.nombre, datoscliente.direccion, t)

            # setear direccion del emisor
            emisor_fel.set_direccion('SANTA ROSALIA MOTOS \n TEL.5053-8711',
                                     '19001', 'Zacapa', 'guatemala', 'GT')  # Cabecera de Negocio

            # setear datos del emisor fel
            # Parametros:  afiliacion_iva, codigo_establecimiento, correo_emisor, nit_emisor, nombre_comercial, nombre_emisor
            emisor_fel.set_datos_emisor('GEN', '2', '<EMAIL>', '96638303',
                                        'BARRIO EL CALVARIO ZONA 1 TECULUTAN', 'SANTA ROSALIA MOTOS')  # Cabecera de Negocio

            # setear direccion del receptor
            receptor_fel.set_direccion(
                datoscliente.direccion, '19001', 'Zacapa', 'guatemala', 'GT')

            # setear datos del receptor
            receptor_fel.set_datos_receptor(
                '<EMAIL>', datoscliente.nit, datoscliente.nombre)

            # identificador unico del dte del cliente
            dte_fel_a_certificar.set_clave_unica(f'202032{id}')  # no importa

            # setear datos generales
            dte_fel_a_certificar.set_datos_generales(
                'GTQ', f'{datoscliente.fecha}T00:00:00-06:00', 'FACT')  # fecha de venta

            # agregar los datos del emisor
            dte_fel_a_certificar.set_datos_emisor(emisor_fel)
            # agregar los datos del receptor
            dte_fel_a_certificar.set_datos_receptor(receptor_fel)

            # agregar las frases escenario y tipo
            dte_fel_a_certificar.frase_fel.set_frase('1', '1')
            dte_fel_a_certificar.frase_fel.set_frase('1', '2')
            # dte_fel_a_certificar.frase_fel.set_frase('1','1','20185687029123456789','2018-10-11')

            num = 1
            acutotal = 0
            acuiva = 0
            miventa = 0
            for item in Detalle.objects.filter(token=t):

                num = num+1

                item_1 = InfileFel.item()

                item_1_impuesto = InfileFel.impuesto()

                # llenar el item con los datos necesarios
                item_1.set_numero_linea(num)  # esto falta
                item_1.set_bien_o_servicio('B')
                item_1.set_cantidad(item.cantidad)
                item_1.set_unidad_medida('UND')  # no se puede cambiar
                item_1.set_descripcion(item.id_prod.nombre)
                item_1.set_precio_unitario(item.precio)
                item_1.set_precio(item.cantidad*item.precio)
                item_1.set_descuento(0)
                item_1.set_total(item.total)

                grav = round((item.total/112)*100, 2)
                iva = round((grav*12)/100, 2)

                # llenar los impuestos del item
                item_1_impuesto.set_monto_impuesto(iva)
                item_1_impuesto.set_monto_gravable(grav)
                item_1_impuesto.set_codigo_unidad_gravable(1)
                item_1_impuesto.set_nombre_corto('IVA')
                item_1.set_impuesto(item_1_impuesto)

                acutotal = acutotal + grav
                acuiva = acuiva + iva
                miventa = miventa + item.total

                # Agregar el item 1
                dte_fel_a_certificar.agregar_item(item_1)

                # agregar el gran total

            total_fel.set_gran_total(miventa)

            # agregar datos del gran total de impuestos

            totales_impuestos.set_nombre_corto('IVA')
            totales_impuestos.set_total_monto_impuesto(acuiva)

            total_fel.set_total_impuestos(totales_impuestos)

            # agregar los totales
            dte_fel_a_certificar.agregar_totales(total_fel)

            # agregar adendas al gusto
            fel_adenda = InfileFel.adenda()
            fel_adenda.nombre = "factura_ruta"
            fel_adenda.valor = "s"
            dte_fel_a_certificar.agregar_adenda(fel_adenda)
            print("#########################################################")

            # contingencia
            # dte_fel_a_certificar.set_acceso('123123')

            # agregando el tipo de personeria
            # dte_fel_a_certificar.set_tipo_personeria('2')

            # realizar el llamado a la certificada
            certificacion_fel = dte_fel_a_certificar.certificar()
            if (certificacion_fel["resultado"]):
                print("UUID:" + certificacion_fel["uuid"])
                print("FECHA:" + certificacion_fel["fecha"])
                print("SERIE:" + certificacion_fel["serie"])
                print("numero:" + str(certificacion_fel["numero"]))
                total = Detalle.objects.all().filter(token=t).aggregate(
                    tot=Sum('total'))  # total de la venta
                messages.info(
                    request, f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}")
                Venta.objects.filter(token=t).update(nit=request.POST["nit"], nombre=request.POST["nombre"], direccion=request.POST["direccion"], fecha_fel=str(
                    certificacion_fel['fecha']), estado=1, link=f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}", anula=certificacion_fel['uuid'], serie=certificacion_fel['serie'], numero=certificacion_fel['numero'], total=total['tot'])
                # Venta.objects.filter(factura=id).update(link=f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}")
                #messages.info(request,factura_ver.link)
                return redirect('NuevaVenta')
                
            else:
                print("No pudo ser certificada")
                print("Descripcion: " + certificacion_fel["descripcion"])

                for error_fel in certificacion_fel["descripcion_errores"]:
                    print("Mensaje Error: " + error_fel["fuente"])
                    print("fuente: " + error_fel["mensaje_error"])
                    print("categoria: " + error_fel["categoria"])
                    print("numeral: " + error_fel["numeral"])
                    print("validacion: " + error_fel["validacion"])

        else:
            pass

        ###################################################################################################################################

    # return render(request,"PagoApp/pagoefectivo.html",{'factura':factura,'detalle':detalle,'cd':cd,'total':total,'id':id})


def fel3(request, t):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        factura = Venta.objects.filter(token=t)
        factura_ver = Venta.objects.get(token=t)
        detalle = Detalle.objects.filter(token=t)
        cd = Detalle.objects.filter(token=t).aggregate(
            cantidad=Sum('cantidad'))  # total de la venta
        total = Detalle.objects.filter(token=t).aggregate(
            tot=Sum('total'))  # total de la venta
        miventa = Detalle.objects.filter(token=t).aggregate(
            t=Sum('total'))  # total de la venta
        for f in factura:
            pass

        for d in detalle:
            pass

            Venta.objects.filter(token=t).update(
                nit=request.POST["nit"], nombre=request.POST["nombre"], direccion=request.POST["direccion"], estado=1)
            # miventa = Venta.objects.filter(factura=id).values_list('nit','direccion','negocio')
            # facturar(request,id)

            # crear el DTE a Certificar

            dte_fel_a_certificar = InfileFel.fel_dte()  # aki mandamos el correlativo

            # crear el emisor
            emisor_fel = emisor.emisor()

            # crear el receptor
            receptor_fel = receptor.receptor()

            # crear los totales
            total_fel = InfileFel.totales()

            # totales impuestos
            totales_impuestos = InfileFel.total_impuesto()

            for datoscliente in Venta.objects.filter(token=t):
                print(datoscliente.nit, datoscliente.nombre,
                      datoscliente.direccion, t)

            clientenoexiste(datoscliente.nit,
                            datoscliente.nombre, datoscliente.direccion, t)

            # setear direccion del emisor
            emisor_fel.set_direccion(f'BARRIO LA ESTACION LOCAL 2 ZACAPA  \n TEL.3040-8780',
                                     '19001', 'Zacapa', 'guatemala', 'GT')  # Cabecera de Negocio

            # setear datos del emisor fel
            # Parametros:  afiliacion_iva, codigo_establecimiento, correo_emisor, nit_emisor, nombre_comercial, nombre_emisor
            emisor_fel.set_datos_emisor('GEN', '3', '<EMAIL>', '96638303',
                                        'SANTA ROSALIA MOTOS', 'SANTA ROSALIA MOTOS')  # Cabecera de Negocio

            # setear direccion del receptor
            receptor_fel.set_direccion(
                datoscliente.direccion, '19001', 'Zacapa', 'guatemala', 'GT')

            # setear datos del receptor
            receptor_fel.set_datos_receptor(
                '<EMAIL>', datoscliente.nit, datoscliente.nombre)

            # identificador unico del dte del cliente
            dte_fel_a_certificar.set_clave_unica(f'202032{id}')  # no importa

            # setear datos generales
            dte_fel_a_certificar.set_datos_generales(
                'GTQ', f'{datoscliente.fecha}T00:00:00-06:00', 'FACT')  # fecha de venta

            # agregar los datos del emisor
            dte_fel_a_certificar.set_datos_emisor(emisor_fel)
            # agregar los datos del receptor
            dte_fel_a_certificar.set_datos_receptor(receptor_fel)

            # agregar las frases
            dte_fel_a_certificar.frase_fel.set_frase('1', '1')
            dte_fel_a_certificar.frase_fel.set_frase('1', '2')
            # dte_fel_a_certificar.frase_fel.set_frase('1','1','20185687029123456789','2018-10-11')

            num = 1
            acutotal = 0
            acuiva = 0
            miventa = 0
            for item in Detalle.objects.filter(token=t):

                num = num+1

                item_1 = InfileFel.item()

                item_1_impuesto = InfileFel.impuesto()

                # llenar el item con los datos necesarios
                item_1.set_numero_linea(num)  # esto falta
                item_1.set_bien_o_servicio('B')
                item_1.set_cantidad(item.cantidad)
                item_1.set_unidad_medida('UND')  # no se puede cambiar
                item_1.set_descripcion(item.id_prod.nombre)
                item_1.set_precio_unitario(item.precio)
                item_1.set_precio(item.cantidad*item.precio)
                item_1.set_descuento(0)
                item_1.set_total(item.total)

                grav = round((item.total/112)*100, 2)
                iva = round((grav*12)/100, 2)

                # llenar los impuestos del item
                item_1_impuesto.set_monto_impuesto(iva)
                item_1_impuesto.set_monto_gravable(grav)
                item_1_impuesto.set_codigo_unidad_gravable(1)
                item_1_impuesto.set_nombre_corto('IVA')
                item_1.set_impuesto(item_1_impuesto)

                acutotal = acutotal + grav
                acuiva = acuiva + iva
                miventa = miventa + item.total

                # Agregar el item 1
                dte_fel_a_certificar.agregar_item(item_1)

                # agregar el gran total

            total_fel.set_gran_total(miventa)

            # agregar datos del gran total de impuestos

            totales_impuestos.set_nombre_corto('IVA')
            totales_impuestos.set_total_monto_impuesto(acuiva)

            total_fel.set_total_impuestos(totales_impuestos)

            # agregar los totales
            dte_fel_a_certificar.agregar_totales(total_fel)

            # agregar adendas al gusto
            fel_adenda = InfileFel.adenda()
            fel_adenda.nombre = "factura_ruta"
            fel_adenda.valor = "s"
            dte_fel_a_certificar.agregar_adenda(fel_adenda)
            print("#########################################################")

            # contingencia
            # dte_fel_a_certificar.set_acceso('123123')

            # agregando el tipo de personeria
            # dte_fel_a_certificar.set_tipo_personeria('2')

            # realizar el llamado a la certificada
            certificacion_fel = dte_fel_a_certificar.certificar()
            if (certificacion_fel["resultado"]):
                print("UUID:" + certificacion_fel["uuid"])
                print("FECHA:" + certificacion_fel["fecha"])
                print("SERIE:" + certificacion_fel["serie"])
                print("numero:" + str(certificacion_fel["numero"]))
                total = Detalle.objects.all().filter(token=t).aggregate(
                    tot=Sum('total'))  # total de la venta
                messages.info(
                    request, f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}")
                Venta.objects.filter(token=t).update(nit=request.POST["nit"], nombre=request.POST["nombre"], direccion=request.POST["direccion"], fecha_fel=str(
                    certificacion_fel['fecha']), estado=1, link=f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}", anula=certificacion_fel['uuid'], serie=certificacion_fel['serie'], numero=certificacion_fel['numero'], total=total['tot'])
                # Venta.objects.filter(factura=id).update(link=f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}")
                #messages.info(request,factura_ver.link)
                return redirect('NuevaVenta')
                
            else:
                print("No pudo ser certificada")
                print("Descripcion: " + certificacion_fel["descripcion"])

                for error_fel in certificacion_fel["descripcion_errores"]:
                    print("Mensaje Error: " + error_fel["fuente"])
                    print("fuente: " + error_fel["mensaje_error"])
                    print("categoria: " + error_fel["categoria"])
                    print("numeral: " + error_fel["numeral"])
                    print("validacion: " + error_fel["validacion"])

        else:
            pass

        ###################################################################################################################################

    # return render(request,"PagoApp/pagoefectivo.html",{'factura':factura,'detalle':detalle,'cd':cd,'total':total,'id':id})




def anularfel(request,t):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        for datoscliente in Venta.objects.filter(token=t):
                print(datoscliente.nit,datoscliente.nombre,datoscliente.direccion)
        
        dte_fel_a_anular = InfileFel.fel_dte()

        #realizar el llamado a la certificada
        certificacion_fel = dte_fel_a_anular.anular(str(datoscliente.fecha_fel),'96638303',str(datoscliente.fecha_fel),datoscliente.nit,datoscliente.anula,datoscliente.serie)
        if (certificacion_fel["resultado"]):
            print ("UUID:" + certificacion_fel["uuid"])
            print ("FECHA:"+ certificacion_fel["fecha"])
            print ("SERIE:"+ certificacion_fel["serie"])
            print ("numero:"+ str(certificacion_fel["numero"]))
        else:
            print("No pudo ser certificada")  
            print("Descripcion: " +  certificacion_fel["descripcion"] )

            for error_fel in certificacion_fel["descripcion_errores"]:
                print("Mensaje Error: " +  error_fel["fuente"] )
                print("fuente: " +  error_fel["mensaje_error"] )
                print("categoria: " +  error_fel["categoria"] )
                print("numeral: " +  error_fel["numeral"] )
                print("validacion: " +  error_fel["validacion"] )

        total = Detalle.objects.all().filter(token=t).aggregate(tot=Sum('total'))#total de la venta
        Venta.objects.filter(token=t).update(fecha_fel=datoscliente.fecha_fel,estado=2,total=total['tot'])
        anular(request,datoscliente.factura)
        messages.success(request,'Anulacion FEL Exitosa')
        return redirect('ListaVenta')       