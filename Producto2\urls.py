from Producto import views, viewsexcel,viewsprodfactura
from django.urls import path

urlpatterns = [
    path('',views.nuevo,name="NuevoProducto"),
    path('listadoproductos/',views.listado,name="ListaProducto"),
    path('listadoproductoszacapa/', views.listadozacapa,
         name="ListaProductoZacapa"),
    path('listadoproductosestanzuela/', views.listadoestanzuela,
         name="ListaProductoEstanzuela"),
    path('listadoproductosteculutan/', views.listadoteculutan,
         name="ListaProductoTeculutan"),
    path('listadoproductosstacruz/', views.listadostacruz,
         name="ListaProductoStaCruz"),     
    path('listadoproductosteculutan/', views.listadotienda,
         name="ListaProductoEmpleado"),
    path('modificarproducto/<int:id>',views.actualizar,name="UpdateProducto"),
    path('eliminarproducto/<int:id>',views.eliminar,name="DeleteProducto"),
    path('exceltodo/<str:t>', views.excel, name='Excel'),
    path('excelgeneral/', viewsexcel.exceltodo, name='ExcelTodo'),
    
    path('excelstacruz/', views.excelstacruz, name='ExcelStaCruz'),

    path('ingresofactura/',viewsprodfactura.nuevo,name='IngresoFactura'),
    path('listaingresos/',viewsprodfactura.lista,name='ListaIngresoFactura'),
    path('detalleingresofactura/<int:f>',viewsprodfactura.detalle,name='DetalleIngresoFactura'),
    path('bajaingresofactura/<int:f>',viewsprodfactura.darbaja,name='BajaIngresoFactura'),
]
