# Generated by Django 4.1.7 on 2024-01-16 11:31

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('Producto', '0007_detallefactura_estado_productofactura_estado'),
    ]

    operations = [
        migrations.CreateModel(
            name='Vale',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('origen', models.CharField(max_length=550)),
                ('destino', models.CharField(max_length=550)),
                ('total', models.DecimalField(blank=True, decimal_places=2, default=0.0, max_digits=12, null=True)),
                ('fecha', models.DateTimeField()),
                ('token', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('estado', models.IntegerField(default=0)),
                ('obs', models.CharField(blank=True, default='', max_length=1550, null=True)),
                ('usuario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['id'],
            },
        ),
        migrations.CreateModel(
            name='DetalleVale',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cantidad', models.IntegerField()),
                ('precio', models.DecimalField(decimal_places=2, max_digits=12)),
                ('total', models.DecimalField(decimal_places=2, max_digits=12)),
                ('fecha', models.DateTimeField()),
                ('token', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('estado', models.IntegerField(default=0)),
                ('envio', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Vales.vale')),
                ('producto', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Producto.producto')),
                ('usuario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['envio'],
            },
        ),
    ]
