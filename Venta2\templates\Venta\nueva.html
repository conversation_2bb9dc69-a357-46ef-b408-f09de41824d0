{% extends 'Base/base.html' %}
{% block title %}Nueva Venta{% endblock %}

{% block content %}

{% if user.rol == "admin" %}
<div class="col-md-6">
    {% if messages %}
    {% for message in messages %}

    {% if message.tags == "success" %}
    <script>
        Swal.fire({
            "title": "Informacion Sistema",
            "text": "Venta Finalizada Ver Comprobante en Tienda Que Se Realizo Venta!",
            "icon": "{{message.tags}}"
        })
    </script>
    {% else %}
    <script>
        Swal.fire({
            "title": "Informacion Sistema",
            "text": "Venta Descartada!",
            "html": "{{message}}",
            "icon": "{{message.tags}}"
        })
    </script>
    {% endif %}

    {% endfor %}
    {% endif %}
</div>
{% else %}
<div class="col-md-6">
    {% if messages %}
    {% for message in messages %}

    {% if message.tags == "success" %}
    <script>
        Swal.fire({
            "title": "Informacion Sistema",
            "text": "Venta Finalizada!",
            "html": "<a href='{% url 'PDF' message %}'' class='btn btn-danger'>PDF</a>",
            "icon": "{{message.tags}}"
        })
    </script>
    {% elif message.tags == "info" %}
    <script>
        Swal.fire({
            "title": "Informacion Sistema",
            "text": "Factura",
            "html": "<a href='{{message}}'' class='btn btn-danger' target='_blank'>FEL</a>",
            "icon": "{{message.tags}}"
        })
    </script>
    {% else %}
    <script>
        Swal.fire({
            "title": "Informacion Sistema",
            "text": "Venta Descartada!",
            "html": "{{message}}",
            "icon": "{{message.tags}}"
        })
    </script>
    {% endif %}

    {% endfor %}
    {% endif %}
</div>
{% endif %}


<div class="content-wrapper">

    <div class="container-xxl flex-grow-1 container-p-y">

        <div class="row">
            <!-- Basic Layout -->
            <div class="col-md-10">
                <div class="card md-6">
                    <div class="card-header d-flex align-items-center justify-content-between">
                        <h5 class="mb-0">Formulario Nueva Venta</h5>
                        <small class="text-muted float-end">Venta</small>
                    </div>
                    <div class="card-body">
                        <form action="#" method="POST" enctype="multipart/form-data">{% csrf_token %}
                            <div class="row mb-3">
                                <label class="col-sm-2 col-form-label" for="basic-default-name">Nit</label>
                                <div class="col-sm-10">
                                    {{form.nit}}
                                </div>
                            </div>
                            <!--<div class="row mb-3">
                                <label class="col-sm-2 col-form-label" for="basic-default-name">Nombre</label>
                                <div class="col-sm-10">
                                    {{form.nombre}}
                                </div>
                            </div>
                            <div class="row mb-3">
                                <label class="col-sm-2 col-form-label" for="basic-default-name">Direccion</label>
                                <div class="col-sm-10">
                                    {{form.direccion}}
                                </div>
                            </div>-->
                            <div class="row mb-3">
                                <label class="col-sm-2 col-form-label" for="basic-default-name">Tipo de Venta</label>
                                <div class="col-sm-10">
                                    {{form.tipo}}
                                </div>
                            </div>
                            <div class="row justify-content-end">
                                <div class="col-sm-10">
                                    <button type="submit" class="btn btn-primary">Iniciar</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

        </div>

    </div>

</div>

{% endblock %}