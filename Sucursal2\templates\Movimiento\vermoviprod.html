{% extends 'Base/basever.html' %}
{% block title %}Kardex{% endblock %}

{% block content %}
<div class="content-wrapper">
  <div class="container">

    <div class="row">
      <div class="col-md-12">
        <div class="container">

          <div class="row">
            <div class="col md-3" style="margin-top: 15px; font-size: 14px;">
              <label for="">Codigo</label><br>
              <strong class="text-danger">{{ p.id }}</strong>
            </div>
            <div class="col md-3" style="margin-top: 15px; font-size: 14px;">
              <label for="">Producto</label><br>
              <strong class="text-danger">{{ p.nombre }}</strong>
            </div>
            <div class="col md-3" style="margin-top: 15px; font-size: 14px;">
              <label for="">Tienda</label><br>
              <strong class="text-danger">{{ p.tienda }}</strong>
            </div>
            <div class="col md-3" style="margin-top: 15px; font-size: 14px;">
              <label for="">Stock</label><br>
              <strong class="text-danger">{{ p.stock }}</strong>
            </div>
          </div>

          <div class="row">
            <div class="col md-3" style="margin-top: 15px; font-size: 14px;">
              <label for="">Precio Compra</label><br>
              <strong class="text-danger">Q.{{ p.precio_compra }}</strong>
            </div>
            <div class="col md-3" style="margin-top: 15px; font-size: 14px;">
              <label for="">Precio Venta</label><br>
              <strong class="text-danger">Q.{{ p.precio_venta }}</strong>
            </div>
            <div class="col md-3" style="margin-top: 15px; font-size: 14px;">
              <label for="">Mejor Venta</label><br>
              <strong class="text-danger">Q.{{ mx }}</strong>
            </div>
            <div class="col md-3" style="margin-top: 15px; font-size: 14px;">
              <label for="">Peor Venta</label><br>
              <strong class="text-danger">Q.{{ mn }}</strong>
            </div>
            <div class="col md-3" style="margin-top: 15px; font-size: 14px;">
              <label for="">En Venta</label><br>
              <strong class="text-danger">{{ v }} veces</strong>
            </div>
          </div><br>

          <form action="#" method="POST">{% csrf_token %}
            <div class="row">
              <div class="col-md-3">
                <label for="">Fecha Inicio</label>
                <input type="date" class="form-control" name="inicio">
              </div>
              <div class="col-md-3">
                <label for="">Fecha Fin</label>
                <input type="date" class="form-control" name="fin">
              </div>
              <div class="col-md-3"><br>
                <button type="submit" class="btn btn-info btn-sm">Filtrar</button>
              </div>
            </div><br>
          </form>

        </div>

        <div class="table-responsive-sm" style="width: 100%; height: 500px; overflow: auto;">
          <table class="table table-bordered table-sm order-table">
            <thead>
              <tr>
                <th>Prod</th>
                <th>Fecha</th>
                <th># Doc</th>
                <th>Tipo</th>
                <th>Anterior</th>
                <th>Ingreso</th>
                <th>Salida</th>
                <th>Saldo</th>
                <th>Tienda</th>
                <th>Usuario</th>
              </tr>
            </thead>
            <tbody style="font-size: 12px;">
              {% for m in prod %}
              <tr>
                <td>{{ m.prod }}</td>
                <td>{{ m.fecha }}</td>
                {% if m.tienda == 'Estanzuela' %}
                <td>{{ m.doc }}.1</td>
                {% elif m.tienda == 'Teculutan' %}
                <td>{{ m.doc }}.2</td>
                {% elif m.tienda == 'Zacapa' %}
                <td>{{ m.doc }}.3</td>
                {% else %}
                <td>{{ m.doc }}</td>
                {% endif %}
                <td>{{ m.tipo }}</td>
                <td>{{ m.habia }}</td>
                <td>{{ m.ingreso }}</td>
                <td>{{ m.salio }}</td>
                <td>{{ m.hay }}</td>

                <td>{{ m.tienda }}</td>
                <td>{{ m.usuario }}</td>
              </tr>
              {% empty %}
              <tr><td colspan="10" class="text-center">SIN MOVIMIENTOS</td></tr>
              {% endfor %}
            </tbody>
          </table>
        </div>

      </div>
    </div>
  </div>
</div>

{% endblock %}
