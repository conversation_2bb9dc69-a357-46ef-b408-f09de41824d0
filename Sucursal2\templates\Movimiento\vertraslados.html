{% extends 'Base/basever.html' %}
{% block title %}Movimientos Producto en Traslados{% endblock %}

{% block content %}

<div class="content-wrapper">

    <div class="container-xxl flex-grow-1 container-p-y">

        <div class="row">
            <!-- Basic Layout -->
            <div class="col-md-12">
                <div class="card md-6">
                    <div class="card-header d-flex align-items-center justify-content-between">
                        <h5 class="mb-0">Movimientos Traslados del Producto {{prod.nombre}}</h5>
                        <small class="text-muted float-end">Movimientos Traslados del Producto {{prod.nombre}}</small>
                    </div>
                    <div class="card-body">

                    </div>&nbsp;

                    <div class="row" align="center">

                        <div class="col-md-12">

                            <label for="">Detalle del Producto</label>

                            <div style="height: 12rem; overflow-y: scroll;">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm" align="center">
                                        <thead>
                                            <tr align="center">
                                                <th>Traslado</th>
                                                <th>Producto</th>
                                                <th>Stock</th>
                                                <th>Ingreso</th>
                                                <th>Stock</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for prod in detalles %}
                                            <tr align="center">
                                                <td>{{prod.id}}</td>
                                                <td>{{prod.id_prod.nombre}}</td>
                                                <td>{{prod.stock_antes}}</td>
                                                <td>{{prod.cantidad}}</td>
                                                <td>{{prod.stock_ahora}}</td>
                                            </tr>
                                            {% empty %}
                                            <caption>SIN MOVIMIENTOS</caption>
                                            {% endfor %}
                                        </tbody>
                                    </table>
    
                                </div>
                            </div>

                        </div>

                    </div>

                </div>




            </div>




        </div>
    </div>





    {% endblock %}