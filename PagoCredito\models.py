from django.db import models
from Venta.models import Venta
from user.models import User
import uuid


class Pago(models.Model):
    factura = models.ForeignKey(Venta,on_delete=models.CASCADE,blank=False,null=False)
    tipo = models.CharField(max_length=75,blank=False,null=False)
    nit = models.CharField(max_length=15,blank=True,null=True,default='')
    abono = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False,default=0.00)
    estado = models.IntegerField(blank=False,null=False,default=1)
    fecha = models.DateField(blank=False,null=False)
    usuario = models.ForeignKey(User,on_delete=models.CASCADE,blank=False,null=False)
    token = models.UUIDField(default=uuid.uuid4, editable=False)

    class Meta:
       ordering = ["factura"]


    def __str__(self):
        return self.factura

