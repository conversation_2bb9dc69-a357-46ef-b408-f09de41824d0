from django.http import Http404
from django.shortcuts import render, redirect
from datetime import datetime
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from user.models import User
from django.db.models import Q
from django.db.models import <PERSON><PERSON>, <PERSON>, Min
from Venta.models import Venta, Detalle
from Producto.models import ProductoFactura, DetalleFactura, Producto, Bitacora
from Traslado.models import DetalleTraslado,Traslado
from Envios.models import DetalleEnvio
from Venta.models import Detalle
from Vales.models import DetalleVale
from Sucursal.models import Movimientos, Vales, Envios, DetalleEnvios, DetalleVales


@login_required
def historial(request, id):
   print(id)
   p = Producto.objects.get(id=id)
   bitacora_movimientos = Bitacora.objects.filter(id_prod=id).order_by('fecha')
   movimientos_adicionales = Movimientos.objects.filter(id_prod=id).order_by('fecha')

            
        


   return render(request, 'Movimiento/verhistorial.html',{'bita':bitacora_movimientos,'mov':movimientos_adicionales})   


@login_required
def ver(request, t):
    ver = Venta.objects.get(token=t)
    datos = Detalle.objects.filter(token=t)
    return render(request, 'Movimiento/ver2.html', {'ver': ver, 'detalle': datos})


@login_required
def movi(request):

    b = False

    if request.method == 'POST':

        if request.POST['tienda'] == "":
            messages.error(request, f'Campo Tienda No Puede Estar Vacio')
            return redirect('ConsultaFecha', b)
        else:
            if request.POST['tienda'] == 'Todas':
                ini = request.POST['inicio']
                final = request.POST['fin']
                busqueda = Venta.objects.filter(
                    fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin']).order_by("-fecha")
                conteo = Venta.objects.filter(
                    fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin']).count()
                total = Venta.objects.filter(
                    fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'], estado=1).aggregate(t=Sum('total'))
                b = True
                return render(request, 'Movimiento/consulta_fecha.html', {'b': b, 'venta': busqueda, 'c': conteo, 't': total['t'], 'i': ini, 'f': final})
            else:
                ini = request.POST['inicio']
                final = request.POST['fin']
                busqueda = Venta.objects.filter(
                    fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'], tienda=request.POST['tienda']).order_by("-fecha")
                conteo = Venta.objects.filter(
                    fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'], tienda=request.POST['tienda']).count()
                total = Venta.objects.filter(
                    fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'], estado=1, tienda=request.POST['tienda']).aggregate(t=Sum('total'))
                b = True
                return render(request, 'Movimiento/consulta_fecha.html', {'b': b, 'venta': busqueda, 'c': conteo, 't': total['t'], 'i': ini, 'f': final})


    return render(request, 'Movimiento/consulta_fecha.html', {'b': b})



@login_required
def consignacion(request):

    b = False

    if request.method == 'POST':

        if request.POST['tienda'] == "":
            messages.error(request, f'Campo Tienda No Puede Estar Vacio')
            return redirect('ConsultaFecha', b)
        else:
            if request.POST['tienda'] == 'Todas':
                ini = request.POST['inicio']
                final = request.POST['fin']
                bitacora_movimientos = Bitacora.objects.filter(fecha__gte=ini,fecha__lte=final,tipo='Consignacion')
                movimientos_adicionales = Movimientos.objects.filter(fecha__gte=ini,fecha__lte=final,tipo='Consignacion')
                b = True
                return render(request, 'Movimiento/consulta_consignacion.html', {'b': b, 'bitacora': bitacora_movimientos,'movimientos':movimientos_adicionales,
                                                                                  'i': ini, 'f': final})
            else:
                ini = request.POST['inicio']
                final = request.POST['fin']
                bitacora_movimientos = Bitacora.objects.filter(fecha__gte=ini,fecha__lte=final,tipo='Consignacion')
                movimientos_adicionales = Movimientos.objects.filter(fecha__gte=ini,fecha__lte=final,tipo='Consignacion')
                
                b = True
                return render(request, 'Movimiento/consulta_consignacion.html', {'b': b, 'bitacora': bitacora_movimientos,'movimientos':movimientos_adicionales, 
                                                                                 'i': ini, 'f': final})

    return render(request, 'Movimiento/consulta_consignacion.html', {'b': b})




@login_required
def compras(request):
    b = False

    if request.method == 'POST':
        tienda = request.POST.get('tienda', '')
        tipo = request.POST.get('tipo', '')
        inicio = request.POST.get('inicio', '')
        fin = request.POST.get('fin', '')

        if not tienda:
            messages.error(request, 'Campo Tienda No Puede Estar Vacio')
            return redirect('ConsultaFecha', b)

        if request.user.rol == "admin":
            if tienda == 'Todas':
                    #bitacora_movimientos = Bitacora.objects.filter(fecha__gte=inicio,fecha__lte=fin,tipo='Compra').order_by('-fecha')
                    #movimientos_adicionales = Movimientos.objects.filter(fecha__gte=inicio,fecha__lte=fin,tipo='Compra').order_by('-fecha')
                    ver = ProductoFactura.objects.filter(fecha__gte=inicio,fecha__lte=fin).order_by('-fecha')
                    

                    b = True
                    return render(request, 'Movimiento/consulta_compra_fecha.html', {
                        'b': b,
                        'i': inicio, 'f': fin, 'tipo': 'Compras','ver': ver,
                        
                    })


            else:
                    #bitacora_movimientos = Bitacora.objects.filter(fecha__gte=inicio,fecha__lte=fin,tipo='Compra',tienda=tienda).order_by('-fecha')
                    #movimientos_adicionales = Movimientos.objects.filter(fecha__gte=inicio,fecha__lte=fin,tipo='Compra',tienda=tienda).order_by('-fecha')
                    ver = ProductoFactura.objects.filter(fecha__gte=inicio,fecha__lte=fin,tienda=tienda).order_by('-fecha')
                    b = True
                    return render(request, 'Movimiento/consulta_compra_fecha.html', {
                        'b': b,
                        'i': inicio, 'f': fin, 'tipo': 'Compras','ver': ver,
                        
                    })

        else:  # Usuario normal
                    tienda_usuario = request.user.tienda

                    #bitacora_movimientos = Bitacora.objects.filter(fecha__gte=inicio,fecha__lte=fin,tipo='Compra',tienda=request.user.tienda)
                    #movimientos_adicionales = Movimientos.objects.filter(fecha__gte=inicio,fecha__lte=fin,tipo='Compra',tienda=request.user.tienda)
                    ver = ProductoFactura.objects.filter(fecha__gte=inicio,fecha__lte=fin,tienda=request.user.tienda).order_by('-fecha')
                    b = True
                    return render(request, 'Movimiento/consulta_compra_fecha.html', {
                        'b': b,
                        'i': inicio, 'f': fin, 'tipo': 'Compras','ver': ver,
                        
                    })

    return render(request, 'Movimiento/consulta_compra_fecha.html', {'b': b})



@login_required
def movi_prod(request):

    b = False
    if request.method == 'POST':

        # busqueda = Producto.objects.filter(
        #    Q(nombre__icontains=request.POST['buscar']), tienda=request.POST['tienda'])
        if request.POST['tienda'] == 'Todas':
            busqueda = Producto.objects.all()
            b = True
        else:
            busqueda = Producto.objects.filter(tienda=request.POST['tienda'])
            b = True

        return render(request, 'Movimiento/consulta_prod_mov.html', {'b': b, 'prod': busqueda})

    return render(request, 'Movimiento/consulta_prod_mov.html', {'b': b})

# ver inventario



@login_required
def ver_consignacion(request, id):

    t = Traslado.objects.get(traslado=id)
    detalle = DetalleTraslado.objects.filter(traslado=id)

    return render(request, 'Movimiento/verconsignacion.html', {'t': t, 'd': detalle})

@login_required
def ver_inventario(request, id):

    prod = Producto.objects.get(id=id)
    if prod.stock == (prod.ingreso-prod.salio):
        cuadre = 'Cuadrado'
        return render(request, 'Movimiento/verinventario.html', {'prod': prod, 'cuadre': cuadre})
    else:
        cuadre = 'No Cuadrado'
        return render(request, 'Movimiento/verinventario.html', {'prod': prod, 'cuadre': cuadre})

# ver compras


@login_required
def ver_compras(request, id):
    try:
        prod = Producto.objects.get(id=id)
        detalles = DetalleFactura.objects.filter(id_prod=id)
        return render(request, 'Movimiento/vercompras.html', {'prod': prod, 'detalle': detalles})
    except:
        prod = {}
        detalles = {}
        return render(request, 'Movimiento/vercompras.html', {'prod': prod, 'detalle': detalles})


# ver traslados
@login_required
def ver_traslados(request, id):
    try:
        prod = Producto.objects.get(id=id)
        detalles = DetalleTraslado.objects.filter(id_prod=id)
        return render(request, 'Movimiento/vertraslados.html', {'prod': prod, 'detalle': detalles})
    except:
        prod = {}
        detalles = {}
        return render(request, 'Movimiento/vertraslados.html', {'prod': prod, 'detalle': detalles})

# ver envios


@login_required
def ver_envios(request, id):
    try:
        prod = Producto.objects.get(id=id)
        detalles = DetalleEnvio.objects.filter(id_prod=id)
        return render(request, 'Movimiento/verenvios.html', {'prod': prod, 'detalle': detalles})
    except:
        prod = {}
        detalles = {}
        return render(request, 'Movimiento/verenvios.html', {'prod': prod, 'detalle': detalles})


# ver ventas
@login_required
def ver_ventas(request, id):
    try:
        prod = Producto.objects.get(id=id)
        detalles = Detalle.objects.filter(id_prod=id)
        return render(request, 'Movimiento/verventas.html', {'prod': prod, 'detalle': detalles})
    except:
        prod = {}
        detalles = {}
        return render(request, 'Movimiento/verventas.html', {'prod': prod, 'detalle': detalles})



@login_required
def ver_movi_prod(request, id):
    try:
        prod = Producto.objects.get(id=id)
    except Producto.DoesNotExist:
        return render(request, '404.html')

    # Consultas iniciales
    bitacora_movimientos = Bitacora.objects.filter(id_prod=id).order_by('fecha')
    movimientos_adicionales = Movimientos.objects.filter(id_prod=id).order_by('fecha')
    vales = DetalleVales.objects.filter(id_prod=id).order_by('fecha')
    envios = DetalleEnvios.objects.filter(id_prod=id).order_by('fecha')
    traslados = DetalleTraslado.objects.filter(id_prod=id).order_by('fecha')
    total_vendido = Detalle.objects.filter(id_prod=prod).aggregate(total=Sum('cantidad'))['total'] or 0

    vendido_fecha = None
    inicio = fin = None


    if request.method == 'POST':
        inicio = request.POST.get('inicio')
        fin = request.POST.get('fin')

        if inicio and fin:
            try:
                inicio_date = datetime.strptime(inicio, '%Y-%m-%d')
                fin_date = datetime.strptime(fin, '%Y-%m-%d')

                bitacora_movimientos = bitacora_movimientos.filter(fecha__range=[inicio_date, fin_date])
                movimientos_adicionales = movimientos_adicionales.filter(fecha__range=[inicio_date, fin_date])
                vales = vales.filter(fecha__range=[inicio_date, fin_date])
                envios = envios.filter(fecha__range=[inicio_date, fin_date])

                vendido_fecha = Detalle.objects.filter(
                    id_prod=prod,
                    fecha__range=[inicio_date, fin_date]
                ).aggregate(total=Sum('cantidad'))['total'] or 0

            except ValueError:
                pass

    v = Detalle.objects.filter(id_prod=id).count()
    mx = Detalle.objects.filter(id_prod=id).aggregate(tx=Max('total'))['tx']
    mn = Detalle.objects.filter(id_prod=id).aggregate(tm=Min('total'))['tm']

    return render(request, 'Movimiento/vermoviprod.html', {
        'bitacoras': bitacora_movimientos,
        'movimientos': movimientos_adicionales,
        'detallevales': vales,
        'detalleenvios': envios,
        'traslados':traslados,
        'p': prod,
        'mx': mx,
        'mn': mn,
        'total_vendido': total_vendido,
        'v': v,
        'vendido_fecha': vendido_fecha,
        'inicio': inicio,
        'fin': fin
    })

 


@login_required
def ver_movi_prod_deta_fac(request, f):
    ver = ProductoFactura.objects.get(factura=f)
    datos = DetalleFactura.objects.filter(factura=f)
    return render(request, 'Movimiento/verfacturadetalleprod.html', {'ver': ver, 'detalle': datos})
    
    

#############  NUEVA FUNCION DE MOVIMIENTOS DE ENVIOS ############################  
    
from django.contrib.auth.decorators import login_required
from django.shortcuts import render
from .models import Envios

@login_required
def movi_envios(request):
    datos = []
    b = False
    tienda_seleccionada = None

    if request.method == 'POST':
        tienda_seleccionada = request.POST.get('tienda', '')

        if tienda_seleccionada == 'Todas':
            datos = Envios.objects.all().order_by('-correlativo')
            b = True
        elif tienda_seleccionada:
            datos = Envios.objects.filter(destino=tienda_seleccionada).order_by('-correlativo')
            b = True

    context = {
        'datos': datos,
        'b': b,
        'tienda': tienda_seleccionada or '',
    }

    return render(request, 'Envios/consulta_prod_env.html', context)




@login_required
def ver_movi_envios(request, t):
    try:
        e = Envios.objects.get(token=t)
        d = DetalleEnvios.objects.filter(token=t)
        tk = True
    except Envios.DoesNotExist:
        e = None
        d = []
        tk = False

    return render(request, 'Envios/verenvios.html', {'e': e, 'd': d, 'tk': tk})
 
