from django import forms
from .models import Vale, DetalleVale
from user.models import User

TIENDA = (
    ('Zacapa', 'Zacapa'),
    ('Estanzuela', 'Estanzuela'),
    ('Teculutan', 'Teculutan'),
)
ESTADO = (
    ('0', 'En Proceso'),
    ('1', 'Terminada'),
    ('2', 'Cancelada'),
)


class ValeForm(forms.ModelForm):

    class Meta:
        model = Vale
        fields = ['destino', 'obs']

        widgets = {
            'destino': forms.TextInput(attrs={'class': 'form-control', 'data-style': 'btn-outline-info', 'require': True, 'placeholder': 'Destino'}),
            'obs': forms.TextInput(attrs={'class': 'form-control', 'require': True, 'autofocus': True, 'placeholder': 'Observaciones'}),
        }


class UpdateValeForm(forms.ModelForm):

    class Meta:
        model = Vale
        fields = ['origen', 'destino', 'total', 'obs', 'estado']

        widgets = {
            'origen': forms.TextInput(attrs={'class': 'form-control', 'require': True, 'autofocus': True, 'placeholder': 'Sucursal de Origen'}),
            'destino': forms.TextInput(attrs={'class': 'form-control', 'data-style': 'btn-outline-info', 'require': True, 'placeholder': 'Destino'}),
            'total': forms.TextInput(attrs={'class': 'form-control', 'require': True, 'autofocus': True, 'placeholder': 'Total'}),
            'obs': forms.TextInput(attrs={'class': 'form-control', 'require': True, 'autofocus': True, 'placeholder': 'Observaciones'}),
            'estado': forms.Select(attrs={'class': 'selectpicker form-control', 'data-style': 'btn-outline-info', 'type': 'checkbox', 'require': False}, choices=ESTADO),
        }
