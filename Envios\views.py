from django.shortcuts import get_object_or_404, render, redirect
from datetime import datetime
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from Envios.models import Envio, DetalleEnvio
from Envios.forms import EnvioForm, UpdateEnvioForm
from user.models import User
import uuid
from django.db.models import Q
from Producto.models import Producto, Bitacora
from django.http import HttpResponse
from .repote import Comprobante


@login_required
def nuevo(request):
    form = EnvioForm()
    if request.method == "POST":
        form = EnvioForm(request.POST)
        if form.is_valid():

            if request.user.tienda == 'Estanzuela':
            
                if Envio.objects.order_by('envio_estan').last() == 0:
                    ultimo = 202
                else:
                    antes = Envio.objects.order_by('envio_estan').last()
                    ultimo = antes.envio_estan+1         
                try:
                    e = Envio()
                    e.origen = str(request.POST['origen']).capitalize()
                    e.destino = str(request.POST['destino']).capitalize()
                    e.estado = 0
                    e.obs = form.cleaned_data['obs']
                    e.total = 0.00
                    e.fecha = datetime.today()
                    e.usuario = User.objects.get(id=request.user.id)
                    e.token = uuid.uuid4()
                    e.envio_estan = ultimo 
                    e.save()
                    messages.success(request, f'Envio Iniciado!')
                    return redirect('DetalleEnvio', e.token)
                except:
                    messages.error(
                        request, f'No Se Pudo Iniciar Envio!')
                    return redirect('NuevoEnvio')

            elif request.user.tienda == 'Teculutan':
                try:
                    en = Envio.objects.filter(origen="Teculutan").order_by('envio_tecu').last()
                except:
                    en = 0

                if  en == 0:
                    ultimo = 1
                else:
                    antes = Envio.objects.filter(origen="Teculutan").order_by('envio_tecu').last()
                    ultimo = antes.envio_tecu+1  
                try:
                    e = Envio()
                    e.origen = str(request.POST['origen']).capitalize()
                    e.destino = str(request.POST['destino']).capitalize()
                    e.estado = 0
                    e.obs = form.cleaned_data['obs']
                    e.total = 0.00
                    e.fecha = datetime.today()
                    e.usuario = User.objects.get(id=request.user.id)
                    e.token = uuid.uuid4()
                    e.envio_tecu = ultimo 
                    e.save()
                    messages.success(request, f'Envio Iniciado!')
                    return redirect('DetalleEnvio', e.token)
                except:
                    messages.error(request, f'No Se Pudo Iniciar Envio!')
                    return redirect('NuevoEnvio') 

            elif request.user.tienda == 'Zacapa':
                try:
                    en = Envio.objects.filter(origen="Zacapa").order_by('envio_zacapa').last()
                except:
                    en = 0

                if  en == 0:
                    ultimo = 1
                else:
                    antes = Envio.objects.filter(origen="Zacapa").order_by('envio_zacapa').last()
                    ultimo = antes.envio_zacapa+1
                       
                try:
                    e = Envio()
                    e.origen = str(request.POST['origen']).capitalize()
                    e.destino = str(request.POST['destino']).capitalize()
                    e.estado = 0
                    e.obs = form.cleaned_data['obs']
                    e.total = 0.00
                    e.fecha = datetime.today()
                    e.usuario = User.objects.get(id=request.user.id)
                    e.token = uuid.uuid4()
                    e.envio_zacapa = ultimo 
                    e.save()
                    messages.success(request, f'Envio Iniciado!')
                    return redirect('DetalleEnvio', e.token)
                except:
                    messages.error(
                        request, f'No Se Pudo Iniciar Envio!')
                    return redirect('NuevoEnvio')

            else:
                try:
                    en = Envio.objects.filter(origen="Santa Cruz").order_by('envio_stacruz').last()
                except:
                    en = 0

                if  en == 0:
                    ultimo = 1
                else:
                    antes = Envio.objects.filter(origen="Santa cruz").order_by('envio_stacruz').last()
                    ultimo = antes.envio_stacruz+1

                try:
                    e = Envio()
                    e.origen = str(request.POST['origen']).capitalize()
                    e.destino = str(request.POST['destino']).capitalize()
                    e.estado = 0
                    e.obs = form.cleaned_data['obs']
                    e.total = 0.00
                    e.fecha = datetime.today()
                    e.usuario = User.objects.get(id=request.user.id)
                    e.token = uuid.uuid4()
                    e.envio_stacruz = ultimo 
                    e.save()
                    messages.success(request, f'Envio Iniciado!')
                    return redirect('DetalleEnvio', e.token)
                except:
                    messages.error(
                        request, f'No Se Pudo Iniciar Envio!')
                    return redirect('NuevoEnvio')      
                   

    return render(request, 'Envios/nuevo.html', {'form': form})


@login_required
def detalle(request, t):

    venta = Envio.objects.get(token=t)
    det = DetalleEnvio.objects.filter(token=t)

    # verificacion de token para validar venta
    if str(t) == str(venta.token):
        tok = True
    else:
        tok = False

    if request.method == "POST":

        if tok:

            if 'buscar' in request.POST:

                if request.POST['buscar'] == "":
                    messages.error(
                        request, f'Campo Busqueda No Puede Estar Vacio')
                    return redirect('DetalleEnvio', t)
                else:
                    if request.user.rol == "admin":
                        querys = (Q(id__icontains=request.POST['buscar'].strip()) | Q(
                            nombre__icontains=request.POST['buscar'].strip()))
                        busqueda = Producto.objects.filter(querys)
                        return render(request, 'Envios/detalle.html', {'e': venta, 'tk': tok, 'b': busqueda, 'd': det})
                    else:
                        querys = (Q(id__icontains=request.POST['buscar'].strip()) | Q(
                            nombre__icontains=request.POST['buscar'].strip()))
                        busqueda = Producto.objects.filter(
                            querys, tienda=request.user.tienda)
                        return render(request, 'Envios/detalle.html', {'e': venta, 'tk': tok, 'b': busqueda, 'd': det})

            elif 'agregar' in request.POST:

                ver = Producto.objects.get(id=request.POST['id'])

                if ver.stock >= int(request.POST['cantidad']):

                    if DetalleEnvio.objects.filter(producto=ver.id, token=t).exists():

                        endetalle = DetalleEnvio.objects.get(
                            producto=ver.id, token=t)

                        DetalleEnvio.objects.filter(producto=ver.id, token=t).update(cantidad=endetalle.cantidad+int(
                            request.POST['cantidad']), total=endetalle.total+(ver.precio_venta*int(request.POST['cantidad'])))

                        Producto.objects.filter(id=ver.id).update(
                            stock=ver.stock-int(request.POST['cantidad']), salio=ver.salio+int(request.POST['cantidad']))

                        Envio.objects.filter(token=t).update(
                            total=venta.total+(int(request.POST['cantidad'])*ver.precio_venta))

                        messages.success(
                            request, f'Agregando {request.POST["cantidad"]} Unidad de {ver.nombre}')

                        if request.user.rol == "admin":
                            # id, prod, t, d, h, i, s, hy, td, u
                            bitacora(ver.id, ver.nombre, 'Envio', venta.id, ver.stock, 0, int(
                                request.POST['cantidad']), (ver.stock-int(request.POST['cantidad'])), ver.tienda, request.user.username)
                        else:
                            # id, prod, t, d, h, i, s, hy, td, u
                            bitacora(ver.id, ver.nombre, 'Envio', venta.id, ver.stock, 0, int(
                                request.POST['cantidad']), (ver.stock-int(request.POST['cantidad'])), request.user.tienda, request.user.username)

                        return redirect('DetalleEnvio', t)
                    else:

                        d = DetalleEnvio()
                        d.envio = Envio.objects.get(id=venta.id)
                        d.producto = Producto.objects.get(id=ver.id)
                        d.cantidad = int(request.POST['cantidad'])
                        d.precio = ver.precio_venta
                        d.total = d.precio*d.cantidad
                        d.fecha = datetime.today()
                        d.usuario = User.objects.get(id=request.user.id)
                        d.token = t
                        d.save()
                        Producto.objects.filter(id=ver.id).update(
                            stock=ver.stock-int(request.POST['cantidad']), salio=ver.salio+int(request.POST['cantidad']))
                        Envio.objects.filter(token=t).update(
                            total=venta.total+(int(request.POST['cantidad'])*ver.precio_venta))
                        if request.user.rol == "admin":

                            # id, prod, t, d, h, i, s, hy, td, u
                            bitacora(ver.id, ver.nombre, 'Envio', venta.id, ver.stock, 0, int(
                                request.POST['cantidad']), (ver.stock-int(request.POST['cantidad'])), ver.tienda, request.user.username)
                        else:
                            # id, prod, t, d, h, i, s, hy, td, u
                            bitacora(ver.id, ver.nombre, 'Envio', venta.id, ver.stock, 0, int(
                                request.POST['cantidad']), (ver.stock-int(request.POST['cantidad'])), request.user.tienda, request.user.username)
                        messages.success(
                            request, f'Agregando {request.POST["cantidad"]} Unidad de {ver.nombre}')
                        return redirect('DetalleEnvio', t)

                else:
                    messages.error(
                        request, f'Producto {ver.nombre} No Tiene Existencia!')
                    return redirect('DetalleEnvio', t)

            elif 'quitar' in request.POST:
                idetalle = DetalleEnvio.objects.get(
                    id=request.POST['corr'], token=t)
                elprod = Producto.objects.get(id=idetalle.producto.id)
                Envio.objects.filter(token=t).update(
                    total=venta.total-(idetalle.cantidad*idetalle.precio))
                Producto.objects.filter(id=idetalle.producto.id).update(
                    stock=elprod.stock+idetalle.cantidad, salio=elprod.salio-idetalle.cantidad)

                if request.user.rol == "admin":
                    # id, prod, t, d, h, i, s, hy, td, u
                    bitacora(elprod.id, elprod.nombre, 'Quitado de Envio', venta.id, elprod.stock, idetalle.cantidad,
                             0, elprod.stock+idetalle.cantidad, elprod.tienda, request.user.username)
                else:
                    # id, prod, t, d, h, i, s, hy, td, u
                    bitacora(elprod.id, elprod.nombre, 'Quitado de Envio', venta.id, elprod.stock, idetalle.cantidad,
                             0, elprod.stock+idetalle.cantidad, request.user.tienda, request.user.username)

                idetalle.delete()
                messages.success(
                    request, f'Se Quitado {idetalle.cantidad} de {elprod.nombre}!')
                return redirect('DetalleEnvio', t)

            elif 'terminar' in request.POST:
                miventa = Envio.objects.get(token=t)
                Envio.objects.filter(token=t).update(
                    estado=2, obs=request.POST['obs'])
                messages.success(request, f'{miventa.id}')
                return redirect('NuevoEnvio')

            elif 'descartar' in request.POST:

                # obetenemos la venta
                miventa = Envio.objects.get(token=t)

                # obtener productos en detalle
                midetalle = DetalleEnvio.objects.filter(token=t)

                # obtenemos el cliente para restar lo comprado
                ver = Envio.objects.filter(token=t).exists()

                if ver:
                    # regresamos el inventario a los productos
                    for d in midetalle:
                        # lista productos
                        prod = Producto.objects.get(id=d.producto.id)
                        Producto.objects.filter(id=d.producto.id).update(
                            stock=prod.stock+d.cantidad, ingreso=prod.ingreso+d.cantidad, salio=prod.salio-d.cantidad)

                        if request.user.rol == "admin":
                            # id, prod, t, d, h, i, s, hy, td, u
                            bitacora(prod.id, prod.nombre, 'Descartado de Envio', miventa.id, prod.stock, d.cantidad,
                                     0, prod.stock+d.cantidad, prod.tienda, request.user.username)
                        else:
                            # id, prod, t, d, h, i, s, hy, td, u
                            bitacora(prod.id, prod.nombre, 'Descartado de Envio', miventa.id, prod.stock, d.cantidad,
                                     0, prod.stock+d.cantidad, request.user.tienda, request.user.username)

                            midetalle.delete()

                    miventa.delete()

                    messages.warning(
                        request, f'Envio Descartado Productos Vuelven a Inventario!')
                    return redirect('NuevoEnvio')
                    
                else:

                    messages.warning(
                        request, f'Envio Descartado Productos Vuelven a Inventario!')
                    return redirect('NuevoEnvio')

        else:
            messages.error(
                request, f'Venta Numero {venta.id} Cancelada Por Falta de Verificacion!')
            Envio.objects.filter(token=t).update(
                estado=99, tipo="Envio Corrupto")
            return redirect('NuevoEnvio')

    return render(request, 'Envios/detalle.html', {'e': venta, 'tk': tok, 'd': det})

@login_required
def recibir_envio(request,id):

    for e in DetalleEnvio.objects.filter(envio=id):
        for p in Producto.objects.filter(nombre=e.producto.nombre):
            Producto.objects.filter(nombre=e.producto.nombre).update(stock=p.stock+e.cantidad,ingreso=p.ingreso+e.cantidad)
    Envio.objects.filter(id=id).update(estado=1)        
    messages.success(request,f"Actualizacion Exitosa!")        
    return redirect('ListaEnvios')
    #return render(request, 'Envios/recibir.html', {'e': e})




@login_required
def listado(request):
    
    if request.user.tienda == 'Estanzuela':
        datos = Envio.objects.filter(origen='Estanzuela').order_by('id')
    elif request.user.tienda == 'Teculutan':
        datos = Envio.objects.filter(origen='Teculutan').order_by('id')
    elif request.user.tienda == 'Zacapa':
        datos = Envio.objects.filter(origen='Zacapa').order_by('id')
    elif request.user.tienda == 'Santa Cruz':
        datos = Envio.objects.filter(origen='Santa cruz').order_by('id')       
    else:
        datos = Envio.objects.all().order_by('id') 
        
    return render(request, 'Envios/lista.html', {'envios': datos})


@login_required
def ver(request, t):
    v = Envio.objects.get(token=t)
    d = DetalleEnvio.objects.filter(token=t)

    # validacion de token

    if str(t) == str(v.token):
        tok = True
    else:
        tok = False

    return render(request, 'Envios/ver.html', {'tk': tok, 'e': v, 'd': d})


@login_required
def actualizar(request, id):
    envio = Envio.objects.get(id=id)
    if request.method == 'GET':
        form = UpdateEnvioForm(instance=envio)
    else:
        form = UpdateEnvioForm(request.POST, instance=envio)

        if form.is_valid():
            try:
                envio.fecha = str(datetime.today().strftime('%Y-%m-%d'))
                envio.usuario = User.objects.get(id=request.user.id)
                form.save()
                messages.success(
                    request, f'Envio # {envio.id} Modificado Exitosamente!')
                return redirect('ListaEnvios')
            except:
                messages.error(
                    request, f'No Se Pudo Modificar Envio # {envio.id}!')
                return redirect('ListaEnvios')

    return render(request, 'Envios/actualizar.html', {'form': form})


@login_required
def eliminar(request, id):
    envio = get_object_or_404(Envio, id=id)

    # Verificar si ya está anulado
    if envio.estado == 99:
        messages.warning(request, f'El envío # {envio.id} ya fue anulado previamente.')
        return redirect('ListaEnvios')

    detalles = DetalleEnvio.objects.filter(token=envio.token)
    if not detalles.exists():
        messages.warning(request, f'El envío # {envio.id} no tiene productos asociados y no se puede anular.')
        return redirect('ListaEnvios')

    try:
        for detalle in detalles:
            producto_destino = Producto.objects.filter(
                id=detalle.producto.id,
                tienda=envio.destino
            ).first()

            producto_origen = Producto.objects.filter(
                id=detalle.producto.id,
                tienda=envio.origen
            ).first()

            if producto_destino:
                # Restar del stock de destino
                producto_destino.stock = max(producto_destino.stock - detalle.cantidad, 0)
                producto_destino.salio = max(producto_destino.salio - detalle.cantidad, 0)
                producto_destino.save()

            if producto_origen:
                # Sumar al stock de origen
                producto_origen.stock += detalle.cantidad
                producto_origen.save()
            else:
                # Crear producto en origen si no existía
                Producto.objects.create(
                    id_cate=detalle.producto.id_cate,
                    nombre=detalle.producto.nombre,
                    descripcion=detalle.producto.descripcion,
                    stock=detalle.cantidad,
                    precio_compra=detalle.producto.precio_compra,
                    precio_venta=detalle.producto.precio_venta,
                    tienda=envio.origen,
                    usuario=request.user
                )

            # Registrar bitácora
            tienda_bitacora = request.user.tienda if request.user.rol != "admin" else producto_origen.tienda
            bitacora(
                producto_origen.id if producto_origen else detalle.producto.id,
                detalle.producto.nombre,
                'Envio Anulado',
                envio.id,
                producto_origen.stock if producto_origen else detalle.cantidad,
                detalle.cantidad,
                0,
                producto_origen.stock if producto_origen else detalle.cantidad,
                tienda_bitacora,
                request.user.username
            )

        # Marcar envío como anulado
        envio.estado = 99
        envio.save()

        messages.success(request, f'El envío # {envio.id} ha sido anulado y los productos regresaron a la tienda origen.')

    except Exception as e:
        messages.error(request, f'Error al anular el envío: {str(e)}')

    return redirect('ListaEnvios')



def pdf(request, f):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        response = HttpResponse(content_type='application/pdf')
        response[
            'Content-Disposition'] = f'attachment; filename="comprobante-envio-#.pdf"'
        r = Comprobante(f)
        response.write(r.run())
        return response


def bitacora(id, prod, t, d, h, i, s, hy, td, u):

    b = Bitacora()
    b.id_prod = id
    b.prod = prod
    b.tipo = t
    b.doc = d
    b.habia = h
    b.ingreso = i
    b.salio = s
    b.hay = hy
    b.tienda = td
    b.fecha = str(datetime.today())
    b.usuario = u
    b.save()
