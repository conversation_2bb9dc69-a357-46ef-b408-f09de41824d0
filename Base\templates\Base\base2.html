<!DOCTYPE html>
{% load static %}
<html lang="en" class="light-style layout-menu-fixed" dir="ltr" data-theme="theme-default" data-assets-path="../assets/"
  data-template="vertical-menu-template-free">

<head>
  <meta charset="utf-8" />
  <meta name="viewport"
    content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />

  <title>{% block title %}{% endblock %}</title>

  <meta name="description" content="" />

  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="{% static 'Base/assets/img/favicon/favicon.ico' %}" />

  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link
    href="https://fonts.googleapis.com/css2?family=Public+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap"
    rel="stylesheet" />

  <!-- Icons. Uncomment required icon fonts -->
  <link rel="stylesheet" href="{% static 'Base/assets/vendor/fonts/boxicons.css' %}" />

  <!-- Core CSS -->
  <link rel="stylesheet" href="{% static 'Base/assets/vendor/css/core.css' %}" class="template-customizer-core-css" />
  <link rel="stylesheet" href="{% static 'Base/assets/vendor/css/theme-default.css' %}"
    class="template-customizer-theme-css" />
  <link rel="stylesheet" href="{% static 'Base/assets/css/demo.css' %}" />

  <!-- Vendors CSS -->
  <link rel="stylesheet" href="{% static 'Base/assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.css' %}" />

  <link rel="stylesheet" href="{% static 'Base/assets/vendor/libs/apex-charts/apex-charts.css' %}" />

  <!-- Page Notificaciones -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

  <!-- Helpers -->
  <script src="{% static 'Base/assets/vendor/js/helpers.js' %}"></script>

  <!--! Template customizer & Theme config files MUST be included after core stylesheets and helpers.js in the <head> section -->
  <!--? Config:  Mandatory theme config file contain global vars & default theme options, Set your preferred theme option in this file.  -->
  <script src="{% static 'Base/assets/js/config.js' %}"></script>
</head>

<body>
  <!-- Layout wrapper -->
  <div class="layout-wrapper layout-content-navbar">
    <div class="layout-container">
      <!-- Menu -->

      <aside id="layout-menu" class="layout-menu menu-vertical menu bg-menu-theme">
        <div class="app-brand demo">
          <a href="{% url 'Inicio' %}" class="app-brand-link">
            <span class="app-brand-logo demo">
              <svg width="25" viewBox="0 0 25 42" version="1.1" xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink">
                <defs>
                  <path
                    d="M13.7918663,0.358365126 L3.39788168,7.44174259 C0.566865006,9.69408886 -0.379795268,12.4788597 0.557900856,15.7960551 C0.68998853,16.2305145 1.09562888,17.7872135 3.12357076,19.2293357 C3.8146334,19.7207684 5.32369333,20.3834223 7.65075054,21.2172976 L7.59773219,21.2525164 L2.63468769,24.5493413 C0.445452254,26.3002124 0.0884951797,28.5083815 1.56381646,31.1738486 C2.83770406,32.8170431 5.20850219,33.2640127 7.09180128,32.5391577 C8.347334,32.0559211 11.4559176,30.0011079 16.4175519,26.3747182 C18.0338572,24.4997857 18.6973423,22.4544883 18.4080071,20.2388261 C17.963753,17.5346866 16.1776345,15.5799961 13.0496516,14.3747546 L10.9194936,13.4715819 L18.6192054,7.984237 L13.7918663,0.358365126 Z"
                    id="path-1"></path>
                  <path
                    d="M5.47320593,6.00457225 C4.05321814,8.216144 4.36334763,10.0722806 6.40359441,11.5729822 C8.61520715,12.571656 10.0999176,13.2171421 10.8577257,13.5094407 L15.5088241,14.433041 L18.6192054,7.984237 C15.5364148,3.11535317 13.9273018,0.573395879 13.7918663,0.358365126 C13.5790555,0.511491653 10.8061687,2.3935607 5.47320593,6.00457225 Z"
                    id="path-3"></path>
                  <path
                    d="M7.50063644,21.2294429 L12.3234468,23.3159332 C14.1688022,24.7579751 14.397098,26.4880487 13.008334,28.506154 C11.6195701,30.5242593 10.3099883,31.790241 9.07958868,32.3040991 C5.78142938,33.4346997 4.13234973,34 4.13234973,34 C4.13234973,34 2.75489982,33.0538207 2.37032616e-14,31.1614621 C-0.55822714,27.8186216 -0.55822714,26.0572515 -4.05231404e-15,25.8773518 C0.83734071,25.6075023 2.77988457,22.8248993 3.3049379,22.52991 C3.65497346,22.3332504 5.05353963,21.8997614 7.50063644,21.2294429 Z"
                    id="path-4"></path>
                  <path
                    d="M20.6,7.13333333 L25.6,13.8 C26.2627417,14.6836556 26.0836556,15.9372583 25.2,16.6 C24.8538077,16.8596443 24.4327404,17 24,17 L14,17 C12.8954305,17 12,16.1045695 12,15 C12,14.5672596 12.1403557,14.1461923 12.4,13.8 L17.4,7.13333333 C18.0627417,6.24967773 19.3163444,6.07059163 20.2,6.73333333 C20.3516113,6.84704183 20.4862915,6.981722 20.6,7.13333333 Z"
                    id="path-5"></path>
                </defs>
                <g id="g-app-brand" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                  <g id="Brand-Logo" transform="translate(-27.000000, -15.000000)">
                    <g id="Icon" transform="translate(27.000000, 15.000000)">
                      <g id="Mask" transform="translate(0.000000, 8.000000)">
                        <mask id="mask-2" fill="white">
                          <use xlink:href="#path-1"></use>
                        </mask>
                        <use fill="#696cff" xlink:href="#path-1"></use>
                        <g id="Path-3" mask="url(#mask-2)">
                          <use fill="#696cff" xlink:href="#path-3"></use>
                          <use fill-opacity="0.2" fill="#FFFFFF" xlink:href="#path-3"></use>
                        </g>
                        <g id="Path-4" mask="url(#mask-2)">
                          <use fill="#696cff" xlink:href="#path-4"></use>
                          <use fill-opacity="0.2" fill="#FFFFFF" xlink:href="#path-4"></use>
                        </g>
                      </g>
                      <g id="Triangle"
                        transform="translate(19.000000, 11.000000) rotate(-300.000000) translate(-19.000000, -11.000000) ">
                        <use fill="#696cff" xlink:href="#path-5"></use>
                        <use fill-opacity="0.2" fill="#FFFFFF" xlink:href="#path-5"></use>
                      </g>
                    </g>
                  </g>
                </g>
              </svg>
            </span>
            <span class=" menu-text-center fw-bolder xl"> Corporacion Santa Rosalia</span>
          </a>

          <a href="javascript:void(0);" class="layout-menu-toggle menu-link text-large ms-auto d-block d-xl-none">
            <i class="bx bx-chevron-left bx-sm align-middle"></i>
          </a>
        </div>

        <div class="menu-inner-shadow"></div>

        {% if user.rol == "admin" %}
        <ul class="menu-inner py-1">
          <!-- Dashboard -->
          <li class="menu-item active">
            <a href="{% url 'Inicio' %}" class="menu-link">
              <i class="menu-icon tf-icons bx bx-home-circle"></i>
              <div data-i18n="Analytics">Inicio</div>
            </a>
          </li>

          <!-- Layouts -->
          <li class="menu-item">
            <a href="javascript:void(0);" class="menu-link menu-toggle">
              <i class="menu-icon tf-icons bx bx-layout"></i>
              <div data-i18n="Layouts">Sucursales</div>
            </a>

            <ul class="menu-sub">
              <li class="menu-item">
                <a href="{% url 'NuevaSucursal' %}" class="menu-link">
                  <div data-i18n="Without menu">Nueva Sucursal</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaSucursal' %}" class="menu-link">
                  <div data-i18n="Without menu">Lista Sucursales</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="#" class="menu-link">
                  <div data-i18n="Without menu">Estadisticas</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ConsultaFecha' %}" class="menu-link">
                  <div data-i18n="Without navbar">Consulta Ventas Por Fecha
                  </div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ConsultaCompra' %}" class="menu-link">
                  <div data-i18n="Without navbar">Consulta Compras Por Fecha</div>
                </a>
              </li>
            </ul>
          </li>

          <li class="menu-item">
            <a href="javascript:void(0);" class="menu-link menu-toggle">
              <i class="menu-icon tf-icons bx bx-layout"></i>
              <div data-i18n="Layouts">Usuarios</div>
            </a>

            <ul class="menu-sub">
              <li class="menu-item">
                <a href="{% url 'NuevoUser' %}" class="menu-link">
                  <div data-i18n="Without menu">Nuevo Usuario</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaUser' %}" class="menu-link">
                  <div data-i18n="Without menu">Lista Usuarios</div>
                </a>
              </li>
            </ul>
          </li>

          <li class="menu-header small text-uppercase">
            <span class="menu-header-text">Administrativos</span>
          </li>
          <li class="menu-item">
            <a href="javascript:void(0);" class="menu-link menu-toggle">
              <i class="menu-icon tf-icons bx bx-dock-top"></i>
              <div data-i18n="Account Settings">Categorias</div>
            </a>
            <ul class="menu-sub">
              <li class="menu-item">
                <a href="{% url 'NuevaCategoria' %}" class="menu-link">
                  <div data-i18n="Account">Nueva Categoria</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaCategoria' %}" class="menu-link">
                  <div data-i18n="Notifications">Listado Categorias</div>
                </a>
              </li>
            </ul>
          </li>
          <li class="menu-item">
            <a href="javascript:void(0);" class="menu-link menu-toggle">
              <i class="menu-icon tf-icons bx bx-user"></i>
              <div data-i18n="Account Settings">Proveedor</div>
            </a>
            <ul class="menu-sub">
              <li class="menu-item">
                <a href="{% url 'NuevoProveedor' %}" class="menu-link">
                  <div data-i18n="Account">Nuevo Proveedor</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaProveedor' %}" class="menu-link">
                  <div data-i18n="Notifications">Listado Proveedor</div>
                </a>
              </li>
            </ul>
          </li>
          <li class="menu-item">
            <a href="javascript:void(0);" class="menu-link menu-toggle">
              <i class="menu-icon tf-icons bx bx-lock-open-alt"></i>
              <div data-i18n="Authentications">Productos</div>
            </a>
            <ul class="menu-sub">
              <li class="menu-item">
                <a href="{% url 'NuevoProducto' %}" class="menu-link">
                  <div data-i18n="Basic">Nuevo Producto</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'IngresoFactura' %}" class="menu-link">
                  <div data-i18n="Basic">Ingreso Factura</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'NuevoTraslado' %}" class="menu-link">
                  <div data-i18n="Basic">Nueva Consignacion</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaIngresoFactura' %}" class="menu-link">
                  <div data-i18n="Basic">Lista Ingresos Por Factura</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaTraslado' %}" class="menu-link">
                  <div data-i18n="Basic">Lista Consignaciones</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaProducto' %}" class="menu-link">
                  <div data-i18n="Basic">Listado Productos General</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaProductoZacapa' %}" class="menu-link">
                  <div data-i18n="Basic">Listado Productos Zacapa</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaProductoEstanzuela' %}" class="menu-link">
                  <div data-i18n="Basic">Listado Productos Estanzuela</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaProductoTeculutan' %}" class="menu-link">
                  <div data-i18n="Basic">Listado Productos Teculutan</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaProductoStaCruz' %}" class="menu-link">
                  <div data-i18n="Basic">Listado Productos Santa Cruz</div>
                </a>
              </li>
            </ul>
          </li>
          <li class="menu-item">
            <a href="javascript:void(0);" class="menu-link menu-toggle">
              <i class="menu-icon tf-icons bx bx-user"></i>
              <div data-i18n="Misc">Clientes</div>
            </a>
            <ul class="menu-sub">
              <li class="menu-item">
                <a href="{% url 'NuevoCliente' %}" class="menu-link">
                  <div data-i18n="Error">Nuevo Cliente</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaCliente' %}" class="menu-link">
                  <div data-i18n="Under Maintenance">Listado Clientes</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="#" class="menu-link">
                  <div data-i18n="Under Maintenance">Analisis de Clientes</div>
                </a>
              </li>
            </ul>
          </li>
          <li class="menu-item">
            <a href="javascript:void(0);" class="menu-link menu-toggle">
              <i class="menu-icon tf-icons bx bx-car"></i>
              <div data-i18n="Misc">Envios</div>
            </a>
            <ul class="menu-sub">
              <li class="menu-item">
                <a href="{% url 'NuevoEnvioTda' %}" class="menu-link">
                  <div data-i18n="Error">Nuevo Envio</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaEnvios' %}" class="menu-link">
                  <div data-i18n="Under Maintenance">Listado Envios</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="#" class="menu-link">
                  <div data-i18n="Under Maintenance">Analisis de Envios</div>
                </a>
              </li>
            </ul>
          </li>
          <li class="menu-item">
            <a href="javascript:void(0);" class="menu-link menu-toggle">
              <i class="menu-icon tf-icons bx bx-car"></i>
              <div data-i18n="Misc">Vales</div>
            </a>
            <ul class="menu-sub">
              <li class="menu-item">
                <a href="{% url 'NuevoVale' %}" class="menu-link">
                  <div data-i18n="Error">Nuevo Vale</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaVale' %}" class="menu-link">
                  <div data-i18n="Under Maintenance">Listado Vales</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="#" class="menu-link">
                  <div data-i18n="Under Maintenance">Analisis de Vales</div>
                </a>
              </li>
            </ul>
          </li>
          <li class="menu-item">
            <a href="javascript:void(0);" class="menu-link menu-toggle">
              <i class="menu-icon tf-icons bx bx-wallet"></i>
              <div data-i18n="Misc">Gastos</div>
            </a>
            <ul class="menu-sub">
              <li class="menu-item">
                <a href="{% url 'NuevoGasto' %}" class="menu-link">
                  <div data-i18n="Error">Nuevo Gasto</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaGasto' %}" class="menu-link">
                  <div data-i18n="Under Maintenance">Listado Gastos</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="#" class="menu-link">
                  <div data-i18n="Under Maintenance">Analisis de Gasto</div>
                </a>
              </li>
            </ul>
          </li>
          <li class="menu-item">
            <a href="javascript:void(0);" class="menu-link menu-toggle">
              <i class="menu-icon tf-icons bx bx-cart"></i>
              <div data-i18n="Misc">Ventas</div>
            </a>
            <ul class="menu-sub">
              <li class="menu-item">
                <a href="{% url 'NuevaVenta' %}" class="menu-link">
                  <div data-i18n="Error">Nueva Venta</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaVenta' %}" class="menu-link">
                  <div data-i18n="Under Maintenance">Listado Ventas</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'AnalisisVentas' %}" class="menu-link">
                  <div data-i18n="Under Maintenance">Analisis de Ventas</div>
                </a>
              </li>
            </ul>
          </li>
          <li class="menu-item">
            <a href="javascript:void(0);" class="menu-link menu-toggle">
              <i class="menu-icon tf-icons bx bx-notepad"></i>
              <div data-i18n="Misc">Pagos</div>
            </a>
            <ul class="menu-sub">
              <li class="menu-item">
                <a href="{% url 'ListaPagoPendiente' %}" class="menu-link">
                  <div data-i18n="Error">Nuevo Pago</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'TodosPagos' %}" class="menu-link">
                  <div data-i18n="Under Maintenance">Listado Pagos Creditos</div>
                </a>
              </li>
            </ul>
          </li>
          
        </ul>


        {% else %}

        <ul class="menu-inner py-1">
          <!-- Dashboard -->
          <li class="menu-item active">
            <a href="{% url 'Inicio' %}" class="menu-link">
              <i class="menu-icon tf-icons bx bx-home-circle"></i>
              <div data-i18n="Analytics">Inicio</div>
            </a>
          </li>

          <li class="menu-header small text-uppercase">
            <span class="menu-header-text">Administrativos</span>
          </li>
          <li class="menu-item">
            <a href="javascript:void(0);" class="menu-link menu-toggle">
              <i class="menu-icon tf-icons bx bx-dock-top"></i>
              <div data-i18n="Account Settings">Categorias</div>
            </a>
            <ul class="menu-sub">
              <li class="menu-item">
                <a href="{% url 'NuevaCategoria' %}" class="menu-link">
                  <div data-i18n="Account">Nueva Categoria</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaCategoria' %}" class="menu-link">
                  <div data-i18n="Notifications">Listado Categorias</div>
                </a>
              </li>
            </ul>
          </li>
          <li class="menu-item">
            <a href="javascript:void(0);" class="menu-link menu-toggle">
              <i class="menu-icon tf-icons bx bx-user"></i>
              <div data-i18n="Account Settings">Proveedor</div>
            </a>
            <ul class="menu-sub">
              <li class="menu-item">
                <a href="{% url 'NuevoProveedor' %}" class="menu-link">
                  <div data-i18n="Account">Nuevo Proveedor</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaProveedor' %}" class="menu-link">
                  <div data-i18n="Notifications">Listado Proveedor</div>
                </a>
              </li>
            </ul>
          </li>
          <li class="menu-item">
            <a href="javascript:void(0);" class="menu-link menu-toggle">
              <i class="menu-icon tf-icons bx bx-lock-open-alt"></i>
              <div data-i18n="Authentications">Productos</div>
            </a>
            <ul class="menu-sub">
              <li class="menu-item">
                <a href="{% url 'NuevoProducto' %}" class="menu-link">
                  <div data-i18n="Basic">Nuevo Producto</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'IngresoFactura' %}" class="menu-link">
                  <div data-i18n="Basic">Ingreso Factura</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'NuevoTraslado' %}" class="menu-link">
                  <div data-i18n="Basic">Nueva Consignacion</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaIngresoFactura' %}" class="menu-link">
                  <div data-i18n="Basic">Lista Ingresos Por Factura</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaTraslado' %}" class="menu-link">
                  <div data-i18n="Basic">Lista Consignaciones</div>
                </a>
              </li>
              {% if user.tienda == "Zacapa" %}
              <li class="menu-item">
                <a href="{% url 'ListaProductoZacapa' %}" class="menu-link">
                  <div data-i18n="Basic">Listado Productos Zacapa</div>
                </a>
              </li>
              {% elif user.tienda == "Estanzuela" %}
              <li class="menu-item">
                <a href="{% url 'ListaProductoEstanzuela' %}" class="menu-link">
                  <div data-i18n="Basic">Listado Productos Estanzuela</div>
                </a>
              </li>
              {% elif user.tienda == "Santa Cruz" %}
              <li class="menu-item">
                <a href="{% url 'ListaProductoStaCruz' %}" class="menu-link">
                  <div data-i18n="Basic">Listado Productos Santa Cruz</div>
                </a>
              </li>
              {% else %}
              <li class="menu-item">
                <a href="{% url 'ListaProductoTeculutan' %}" class="menu-link">
                  <div data-i18n="Basic">Listado Productos Teculutan</div>
                </a>
              </li>
              {% endif %}
            </ul>
          </li>
          <li class="menu-item">
            <a href="javascript:void(0);" class="menu-link menu-toggle">
              <i class="menu-icon tf-icons bx bx-user"></i>
              <div data-i18n="Misc">Clientes</div>
            </a>
            <ul class="menu-sub">
              <li class="menu-item">
                <a href="{% url 'NuevoCliente' %}" class="menu-link">
                  <div data-i18n="Error">Nuevo Cliente</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaCliente' %}" class="menu-link">
                  <div data-i18n="Under Maintenance">Listado Clientes</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="#" class="menu-link">
                  <div data-i18n="Under Maintenance">Analisis de Clientes</div>
                </a>
              </li>
            </ul>
          </li>
          <li class="menu-item">
            <a href="javascript:void(0);" class="menu-link menu-toggle">
              <i class="menu-icon tf-icons bx bx-car"></i>
              <div data-i18n="Misc">Envios</div>
            </a>
            <ul class="menu-sub">
              <li class="menu-item">
                <a href="{% url 'NuevoEnvioTda' %}" class="menu-link">
                  <div data-i18n="Error">Nuevo Envio</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaEnvioTda' %}" class="menu-link">
                  <div data-i18n="Under Maintenance">Listado Envios</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'RecibiendoEnvioTda' %}" class="menu-link">
                  <div data-i18n="Under Maintenance">Recibir Envios</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="#" class="menu-link">
                  <div data-i18n="Under Maintenance">Analisis de Envios</div>
                </a>
              </li>
            </ul>
          </li>
          <li class="menu-item">
            <a href="javascript:void(0);" class="menu-link menu-toggle">
              <i class="menu-icon tf-icons bx bx-car"></i>
              <div data-i18n="Misc">Vales</div>
            </a>
            <ul class="menu-sub">
              <li class="menu-item">
                <a href="{% url 'NuevoValesTda' %}" class="menu-link">
                  <div data-i18n="Error">Nuevo Vale</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaValeTda' %}" class="menu-link">
                  <div data-i18n="Under Maintenance">Listado Vales</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'RecibiendoValeTda' %}" class="menu-link">
                  <div data-i18n="Under Maintenance">Recibir Vales</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="#" class="menu-link">
                  <div data-i18n="Under Maintenance">Analisis de Vales</div>
                </a>
              </li>
            </ul>
          </li>
          <li class="menu-item">
            <a href="javascript:void(0);" class="menu-link menu-toggle">
              <i class="menu-icon tf-icons bx bx-wallet"></i>
              <div data-i18n="Misc">Gastos</div>
            </a>
            <ul class="menu-sub">
              <li class="menu-item">
                <a href="{% url 'NuevoGasto' %}" class="menu-link">
                  <div data-i18n="Error">Nuevo Gasto</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaGasto' %}" class="menu-link">
                  <div data-i18n="Under Maintenance">Listado Gastos</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="#" class="menu-link">
                  <div data-i18n="Under Maintenance">Analisis de Gasto</div>
                </a>
              </li>
            </ul>
          </li>
          
          <li class="menu-item">
            <a href="javascript:void(0);" class="menu-link menu-toggle">
              <i class="menu-icon tf-icons bx bx-cart"></i>
              <div data-i18n="Misc">Ventas</div>
            </a>
            <ul class="menu-sub">
              <li class="menu-item">
                <a href="{% url 'NuevaVenta' %}" class="menu-link">
                  <div data-i18n="Error">Nueva Venta</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'ListaVenta' %}" class="menu-link">
                  <div data-i18n="Under Maintenance">Listado Ventas</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'AnalisisVentas' %}" class="menu-link">
                  <div data-i18n="Under Maintenance">Analisis de Ventas</div>
                </a>
              </li>
            </ul>
          </li>
          <li class="menu-item">
            <a href="javascript:void(0);" class="menu-link menu-toggle">
              <i class="menu-icon tf-icons bx bx-notepad"></i>
              <div data-i18n="Misc">Pagos</div>
            </a>
            <ul class="menu-sub">
              <li class="menu-item">
                <a href="{% url 'ListaPagoPendiente' %}" class="menu-link">
                  <div data-i18n="Error">Nuevo Pago</div>
                </a>
              </li>
              <li class="menu-item">
                <a href="{% url 'TodosPagos' %}" class="menu-link">
                  <div data-i18n="Under Maintenance">Listado Pagos Creditos</div>
                </a>
              </li>
            </ul>
          </li>





        </ul>


        {% endif%}
      </aside>
      <!-- Layout container -->
      <div class="layout-page">
        <!-- Navbar -->

        <nav
          class="layout-navbar container-xxl navbar navbar-expand-xl navbar-detached align-items-center bg-navbar-theme"
          id="layout-navbar">
          <div class="layout-menu-toggle navbar-nav align-items-xl-center me-3 me-xl-0 d-xl-none">
            <a class="nav-item nav-link px-0 me-xl-4" href="javascript:void(0)">
              <i class="bx bx-menu bx-sm"></i>
            </a>
          </div>

          <div class="navbar-nav-right d-flex align-items-center" id="navbar-collapse">
            <!-- Search -->
            <div class="navbar-nav align-items-center">
              <div class="nav-item d-flex align-items-center">

              <li class="nav-item lh-1 me-3">
                <a class="github-button" href="#"
                  data-icon="octicon-user" data-size="large" data-show-count="true"
                  aria-label="Star themeselection/sneat-html-admin-template-free on GitHub">
                  {% if user.tienda == None %}
                  Tienda Todas
                  {% else %}
                  Tienda {{user.tienda}}
                  {% endif %}
                </a>
              </li>
              </div>
            </div>
            <!-- /Search -->

            <ul class="navbar-nav flex-row align-items-center ms-auto">
              <!-- Place this tag where you want the button to render. -->
              <li class="nav-item lh-1 me-3">
                <a class="github-button" href="#"
                  data-icon="octicon-star" data-size="large" data-show-count="true"
                  aria-label="Star themeselection/sneat-html-admin-template-free on GitHub">
                  Hola! <p style="text-transform: uppercase;">{{user.username}}</p>
                </a>
              </li>
              
              <!-- User -->
              <li class="nav-item navbar-dropdown dropdown-user dropdown">
                <a class="nav-link dropdown-toggle hide-arrow" href="javascript:void(0);" data-bs-toggle="dropdown">
                  <div class="avatar avatar-online">
                    <img src="{% static 'Base/assets/img/avatars/1.png' %}" alt class="w-px-40 h-auto rounded-circle" />
                  </div>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                  <li>
                    <a class="dropdown-item" href="#">
                      <div class="d-flex">
                        <div class="flex-shrink-0 me-3">
                          <div class="avatar avatar-online">
                            <img src="{% static 'Base/assets/img/avatars/1.png' %}" alt
                              class="w-px-40 h-auto rounded-circle" />
                          </div>
                        </div>
                        <div class="flex-grow-1">
                          <span class="fw-semibold d-block">{{user.first_name}}</span>
                          <small class="text-muted">{{user.rol}}</small>
                        </div>
                      </div>
                    </a>
                  </li>                  
                  <li>
                    <div class="dropdown-divider"></div>
                  </li>
                  <li>
                    <a class="dropdown-item" href="{% url 'UpdatePass' user.id %}">
                      <i class="bx bx-key me-2"></i>
                      <span class="align-middle">Cambio Password</span>
                    </a>
                  </li>
                  <li>
                    <a class="dropdown-item" href="{% url 'Salir' %}">
                      <i class="bx bx-power-off me-2"></i>
                      <span class="align-middle">Cerrar Sesion</span>
                    </a>
                  </li>
                </ul>
              </li>
              <!--/ User -->
            </ul>
          </div>
        </nav>

        <!-- / Navbar -->
        <!-- / Menu -->

        {% block content %}
        {% endblock %}
        <!-- Footer -->
        <footer class="content-footer footer bg-footer-theme">
          <div class="container-xxl d-flex flex-wrap justify-content-between py-2 flex-md-row flex-column">
            <div class="mb-2 mb-md-0">
              ©
              <script>
                document.write(new Date().getFullYear());
              </script>
              , Diseñado Por
              <a href="#" class="footer-link fw-bolder">MultiServicios Sagastume</a>
            </div>

        </footer>
        <!-- / Footer -->
      </div>
      <!-- / Layout page -->



    </div>

    <!-- Overlay -->
    <div class="layout-overlay layout-menu-toggle"></div>
  </div>
  <!-- / Layout wrapper -->



  <!-- Core JS -->
  <!-- build:js assets/vendor/js/core.js -->
  <script src="{% static 'Base/assets/vendor/libs/jquery/jquery.js' %}"></script>
  <script src="{% static 'Base/assets/vendor/libs/popper/popper.js' %}"></script>
  <script src="{% static 'Base/assets/vendor/js/bootstrap.js' %}"></script>
  <script src="{% static 'Base/assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.js' %}"></script>

  <script src="{% static 'Base/assets/vendor/js/menu.js' %}"></script>
  <!-- endbuild -->

  <!-- Vendors JS -->
  <script src="{% static 'Base/assets/vendor/libs/apex-charts/apexcharts.js' %}"></script>

  <!-- Main JS -->
  <script src="{% static 'Base/assets/js/main.js' %}"></script>

  <!-- Page JS -->
  <script src="{% static 'Base/assets/js/dashboards-analytics.js' %}"></script>

  <!-- Place this tag in your head or just before your close body tag. -->
  <script async defer src="https://buttons.github.io/buttons.js"></script>
</body>

</html>