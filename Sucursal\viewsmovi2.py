from django.http import Http404
from django.shortcuts import render, redirect
from datetime import datetime
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from user.models import User
from django.db.models import Q
from django.db.models import <PERSON><PERSON>, <PERSON>, Min
from Venta.models import Venta, Detalle
from Producto.models import ProductoFactura, DetalleFactura, Producto, Bitacora
from Traslado.models import DetalleTraslado,Traslado
from Envios.models import DetalleEnvio
from Venta.models import Detalle
from Vales.models import DetalleVale
from Sucursal.models import Movimientos


@login_required
def historial(request, id):
    
    b = False
    global datos
    datos = []
    global ultimo 
    ultimo = 0
    p = Producto.objects.get(id=id)
    
    for m in Movimientos.objects.filter(id_prod=id).order_by('-fecha'):
        datos.append({
                'factura':m.doc,
                'tipo':m.tipo,
                'habia':m.ultimo,
                'salio':m.salida,
                'ingreso':m.entrada,
                'hay':m.actual,
                'tienda':m.tienda
            })
            
        


    return render(request, 'Movimiento/verhistorial.html',{'datos':datos})   


@login_required
def ver(request, t):
    ver = Venta.objects.get(token=t)
    datos = Detalle.objects.filter(token=t)
    return render(request, 'Movimiento/ver2.html', {'ver': ver, 'detalle': datos})


@login_required
def movi(request):

    b = False

    if request.method == 'POST':

        if request.POST['tienda'] == "":
            messages.error(request, f'Campo Tienda No Puede Estar Vacio')
            return redirect('ConsultaFecha', b)
        else:
            if request.POST['tienda'] == 'Todas':
                ini = request.POST['inicio']
                final = request.POST['fin']
                busqueda = Venta.objects.filter(
                    fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin']).order_by("-fecha")
                conteo = Venta.objects.filter(
                    fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin']).count()
                total = Venta.objects.filter(
                    fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'], estado=1).aggregate(t=Sum('total'))
                b = True
                return render(request, 'Movimiento/consulta_fecha.html', {'b': b, 'venta': busqueda, 'c': conteo, 't': total['t'], 'i': ini, 'f': final})
            else:
                ini = request.POST['inicio']
                final = request.POST['fin']
                busqueda = Venta.objects.filter(
                    fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'], tienda=request.POST['tienda']).order_by("-fecha")
                conteo = Venta.objects.filter(
                    fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'], tienda=request.POST['tienda']).count()
                total = Venta.objects.filter(
                    fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'], estado=1, tienda=request.POST['tienda']).aggregate(t=Sum('total'))
                b = True
                return render(request, 'Movimiento/consulta_fecha.html', {'b': b, 'venta': busqueda, 'c': conteo, 't': total['t'], 'i': ini, 'f': final})


    return render(request, 'Movimiento/consulta_fecha.html', {'b': b})



@login_required
def consignacion(request):

    b = False

    if request.method == 'POST':

        if request.POST['tienda'] == "":
            messages.error(request, f'Campo Tienda No Puede Estar Vacio')
            return redirect('ConsultaFecha', b)
        else:
            if request.POST['tienda'] == 'Todas':
                ini = request.POST['inicio']
                final = request.POST['fin']
                busqueda = Traslado.objects.filter(
                    fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'])
                conteo = Traslado.objects.filter(
                    fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin']).count()
                
                b = True
                return render(request, 'Movimiento/consulta_consignacion.html', {'b': b, 'venta': busqueda, 'c': conteo, 'i': ini, 'f': final})
            else:
                ini = request.POST['inicio']
                final = request.POST['fin']
                busqueda = Traslado.objects.filter(
                    fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'], tienda=request.POST['tienda'])
                conteo = Traslado.objects.filter(
                    fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'], tienda=request.POST['tienda']).count()
                
                b = True
                return render(request, 'Movimiento/consulta_consignacion.html', {'b': b, 'venta': busqueda, 'c': conteo, 'i': ini, 'f': final})

    return render(request, 'Movimiento/consulta_consignacion.html', {'b': b})


@login_required
def compras(request):

    b = False

    if request.method == 'POST':

        if request.POST['tienda'] == "":
            messages.error(request, f'Campo Tienda No Puede Estar Vacio')
            return redirect('ConsultaFecha', b)
        else:
                if request.user.rol == "admin":

                    if request.POST['tienda'] == 'Todas':
                        if request.POST['tipo'] == 'Compra':
                            ini = request.POST['inicio']
                            final = request.POST['fin']
                            busqueda = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tipo='Compra')
                            conteo = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tipo='Compra').count()
                            b = True
                            return render(request, 'Movimiento/consulta_compra_fecha.html', {'b': b, 'compra': busqueda, 'c': conteo, 'i': ini, 'f': final,'tipo':'Compra'})
                        
                        elif request.POST['tipo'] == 'Envio':
                            ini = request.POST['inicio']
                            final = request.POST['fin']
                            busqueda = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tipo='Envio')
                            conteo = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tipo='Envio').count()
                            b = True
                            return render(request, 'Movimiento/consulta_compra_fecha.html', {'b': b, 'compra': busqueda, 'c': conteo, 'i': ini, 'f': final,'tipo':'Envio'})
                        
                        elif request.POST['tipo'] == 'Vale':
                            ini = request.POST['inicio']
                            final = request.POST['fin']
                            busqueda = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tipo='Vale')
                            conteo = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tipo='Vale').count()
                            b = True
                            return render(request, 'Movimiento/consulta_compra_fecha.html', {'b': b, 'compra': busqueda, 'c': conteo, 'i': ini, 'f': final,'tipo':'Vale'})
                        
                        elif request.POST['tipo'] == 'FEL':
                            ini = request.POST['inicio']
                            final = request.POST['fin']
                            busqueda = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tipo='FEL')
                            conteo = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tipo='FEL').count()
                            b = True
                            return render(request, 'Movimiento/consulta_compra_fecha.html', {'b': b, 'compra': busqueda, 'c': conteo, 'i': ini, 'f': final,'tipo':'FEL'})
                        

                        elif request.POST['tipo'] == 'Servicio-FEL':
                            ini = request.POST['inicio']
                            final = request.POST['fin']
                            busqueda = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tipo='Servicio-FEL')
                            conteo = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tipo='Servicio-FEL').count()
                            b = True
                            return render(request, 'Movimiento/consulta_compra_fecha.html', {'b': b, 'compra': busqueda, 'c': conteo, 'i': ini, 'f': final,'tipo':'Servicio-FEL'})
                        
                        elif request.POST['tipo'] == 'Consignacion':
                            ini = request.POST['inicio']
                            final = request.POST['fin']
                            busqueda = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tipo='Consignacion')
                            conteo = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tipo='Consignacion').count()
                            b = True
                            return render(request, 'Movimiento/consulta_compra_fecha.html', {'b': b, 'compra': busqueda, 'c': conteo, 'i': ini, 'f': final,'tipo':'Consignacion'})

                        else:
                            ini = request.POST['inicio']
                            final = request.POST['fin']
                            busqueda = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'])
                            conteo = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin']).count()
                            b = True
                            return render(request, 'Movimiento/consulta_compra_fecha.html', {'b': b, 'compra': busqueda, 'c': conteo, 'i': ini, 'f': final,'tipo':'Todas'})
                    
                    else:

                        if request.POST['tipo'] == 'Compra':
                            ini = request.POST['inicio']
                            final = request.POST['fin']
                            busqueda = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'], tienda=request.POST['tienda'],tipo='Compra')
                            conteo = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tienda=request.POST['tienda'],tipo='Compra').count()
                            b = True
                            return render(request, 'Movimiento/consulta_compra_fecha.html', {'b': b, 'compra': busqueda, 'c': conteo, 'i': ini, 'f': final,'tipo':'Compra'})
                        
                        elif request.POST['tipo'] == 'Envio':
                            ini = request.POST['inicio']
                            final = request.POST['fin']
                            busqueda = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'], tienda=request.POST['tienda'],tipo='Envio')
                            conteo = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tienda=request.POST['tienda'],tipo='Envio').count()
                            b = True
                            return render(request, 'Movimiento/consulta_compra_fecha.html', {'b': b, 'compra': busqueda, 'c': conteo, 'i': ini, 'f': final,'tipo':'Envio'})
                        
                        elif request.POST['tipo'] == 'Vale':
                            ini = request.POST['inicio']
                            final = request.POST['fin']
                            busqueda = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'], tienda=request.POST['tienda'],tipo='Vale')
                            conteo = Movimientos.objects.filter(
                                fecha_factura__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tienda=request.POST['tienda'],tipo='Vale').count()
                            b = True
                            return render(request, 'Movimiento/consulta_compra_fecha.html', {'b': b, 'compra': busqueda, 'c': conteo, 'i': ini, 'f': final,'tipo':'Vale'})
                        
                        elif request.POST['tipo'] == 'FEL':
                            ini = request.POST['inicio']
                            final = request.POST['fin']
                            busqueda = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'], tienda=request.POST['tienda'],tipo='FEL')
                            conteo = Movimientos.objects.filter(
                                fecha_factura__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tienda=request.POST['tienda'],tipo='FEL').count()
                            b = True
                            return render(request, 'Movimiento/consulta_compra_fecha.html', {'b': b, 'compra': busqueda, 'c': conteo, 'i': ini, 'f': final,'tipo':'FEL'})
                        

                        elif request.POST['tipo'] == 'Servicio-FEL':
                            ini = request.POST['inicio']
                            final = request.POST['fin']
                            busqueda = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'], tienda=request.POST['tienda'],tipo='Servicio-FEL')
                            conteo = Movimientos.objects.filter(
                                fecha_factura__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tienda=request.POST['tienda'],tipo='Servicio-FEL').count()
                            b = True
                            return render(request, 'Movimiento/consulta_compra_fecha.html', {'b': b, 'compra': busqueda, 'c': conteo, 'i': ini, 'f': final,'tipo':'Servicio-FEL'})
                        
                        
                        elif request.POST['tipo'] == 'Consignacion':
                            ini = request.POST['inicio']
                            final = request.POST['fin']
                            busqueda = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'], tienda=request.POST['tienda'],tipo='Consignacion')
                            conteo = Movimientos.objects.filter(
                                fecha_factura__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tienda=request.POST['tienda'],tipo='Consignacion').count()
                            b = True
                            return render(request, 'Movimiento/consulta_compra_fecha.html', {'b': b, 'compra': busqueda, 'c': conteo, 'i': ini, 'f': final,'tipo':'Consignacion'})


                        else:
                            ini = request.POST['inicio']
                            final = request.POST['fin']
                            busqueda = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'], tienda=request.POST['tienda'])
                            conteo = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'], tienda=request.POST['tienda']).count()
                            b = True
                            return render(request, 'Movimiento/consulta_compra_fecha.html', {'b': b, 'compra': busqueda, 'c': conteo, 'i': ini, 'f': final,'tipo':'Todas'})
                else:

                        if request.POST['tipo'] == 'Compra':
                            ini = request.POST['inicio']
                            final = request.POST['fin']
                            busqueda = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'], tienda=request.user.tienda,tipo='Compra')
                            conteo = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tienda=request.user.tienda,tipo='Compra').count()
                            b = True
                            return render(request, 'Movimiento/consulta_compra_fecha.html', {'b': b, 'compra': busqueda, 'c': conteo, 'i': ini, 'f': final,'tipo':'Compra'})
                        
                        elif request.POST['tipo'] == 'Envio':
                            ini = request.POST['inicio']
                            final = request.POST['fin']
                            busqueda = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'], tienda=request.user.tienda,tipo='Envio')
                            conteo = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tienda=request.user.tienda,tipo='Envio').count()
                            b = True
                            return render(request, 'Movimiento/consulta_compra_fecha.html', {'b': b, 'compra': busqueda, 'c': conteo, 'i': ini, 'f': final,'tipo':'Envio'})
                        
                        elif request.POST['tipo'] == 'Vale':
                            ini = request.POST['inicio']
                            final = request.POST['fin']
                            busqueda = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'], tienda=request.user.tienda,tipo='Vale')
                            conteo = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tienda=request.user.tienda,tipo='Vale').count()
                            b = True
                            return render(request, 'Movimiento/consulta_compra_fecha.html', {'b': b, 'compra': busqueda, 'c': conteo, 'i': ini, 'f': final,'tipo':'Vale'})
                        
                        elif request.POST['tipo'] == 'FEL':
                            ini = request.POST['inicio']
                            final = request.POST['fin']
                            busqueda = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'], tienda=request.user.tienda,tipo='FEL')
                            conteo = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tienda=request.user.tienda,tipo='FEL').count()
                            b = True
                            return render(request, 'Movimiento/consulta_compra_fecha.html', {'b': b, 'compra': busqueda, 'c': conteo, 'i': ini, 'f': final,'tipo':'FEL'})
                        
                        elif request.POST['tipo'] == 'Servicio-FEL':
                            ini = request.POST['inicio']
                            final = request.POST['fin']
                            busqueda = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'], tienda=request.user.tienda,tipo='Servicio-FEL')
                            conteo = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tienda=request.user.tienda,tipo='Servicio-FEL').count()
                            b = True
                            return render(request, 'Movimiento/consulta_compra_fecha.html', {'b': b, 'compra': busqueda, 'c': conteo, 'i': ini, 'f': final,'tipo':'Servicio-FEL'})
                        
                        elif request.POST['tipo'] == 'Consignacion':
                            ini = request.POST['inicio']
                            final = request.POST['fin']
                            busqueda = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'], tienda=request.POST['tienda'],tipo='Consignacion')
                            conteo = Movimientos.objects.filter(
                                fecha_factura__gte=request.POST['inicio'], fecha__lte=request.POST['fin'],tienda=request.POST['tienda'],tipo='Consignacion').count()
                            b = True
                            return render(request, 'Movimiento/consulta_compra_fecha.html', {'b': b, 'compra': busqueda, 'c': conteo, 'i': ini, 'f': final,'tipo':'Consignacion'})

                        else:
                            ini = request.POST['inicio']
                            final = request.POST['fin']
                            busqueda = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'], tienda=request.user.tienda)
                            conteo = Movimientos.objects.filter(
                                fecha__gte=request.POST['inicio'], fecha__lte=request.POST['fin'], tienda=request.user.tienda).count()
                            b = True
                            return render(request, 'Movimiento/consulta_compra_fecha.html', {'b': b, 'compra': busqueda, 'c': conteo, 'i': ini, 'f': final,'tipo':'Todas'})
                        

                
            
    return render(request, 'Movimiento/consulta_compra_fecha.html', {'b': b})


@login_required
def movi_prod(request):

    b = False
    if request.method == 'POST':

        # busqueda = Producto.objects.filter(
        #    Q(nombre__icontains=request.POST['buscar']), tienda=request.POST['tienda'])
        if request.POST['tienda'] == 'Todas':
            busqueda = Producto.objects.all()
            b = True
        else:
            busqueda = Producto.objects.filter(tienda=request.POST['tienda'])
            b = True

        return render(request, 'Movimiento/consulta_prod_mov.html', {'b': b, 'prod': busqueda})

    return render(request, 'Movimiento/consulta_prod_mov.html', {'b': b})

# ver inventario



@login_required
def ver_consignacion(request, id):

    t = Traslado.objects.get(traslado=id)
    detalle = DetalleTraslado.objects.filter(traslado=id)

    return render(request, 'Movimiento/verconsignacion.html', {'t': t, 'd': detalle})

@login_required
def ver_inventario(request, id):

    prod = Producto.objects.get(id=id)
    if prod.stock == (prod.ingreso-prod.salio):
        cuadre = 'Cuadrado'
        return render(request, 'Movimiento/verinventario.html', {'prod': prod, 'cuadre': cuadre})
    else:
        cuadre = 'No Cuadrado'
        return render(request, 'Movimiento/verinventario.html', {'prod': prod, 'cuadre': cuadre})

# ver compras


@login_required
def ver_compras(request, id):
    try:
        prod = Producto.objects.get(id=id)
        detalles = DetalleFactura.objects.filter(id_prod=id)
        return render(request, 'Movimiento/vercompras.html', {'prod': prod, 'detalle': detalles})
    except:
        prod = {}
        detalles = {}
        return render(request, 'Movimiento/vercompras.html', {'prod': prod, 'detalle': detalles})


# ver traslados
@login_required
def ver_traslados(request, id):
    try:
        prod = Producto.objects.get(id=id)
        detalles = DetalleTraslado.objects.filter(id_prod=id)
        return render(request, 'Movimiento/vertraslados.html', {'prod': prod, 'detalle': detalles})
    except:
        prod = {}
        detalles = {}
        return render(request, 'Movimiento/vertraslados.html', {'prod': prod, 'detalle': detalles})

# ver envios


@login_required
def ver_envios(request, id):
    try:
        prod = Producto.objects.get(id=id)
        detalles = DetalleEnvio.objects.filter(id_prod=id)
        return render(request, 'Movimiento/verenvios.html', {'prod': prod, 'detalle': detalles})
    except:
        prod = {}
        detalles = {}
        return render(request, 'Movimiento/verenvios.html', {'prod': prod, 'detalle': detalles})


# ver ventas
@login_required
def ver_ventas(request, id):
    try:
        prod = Producto.objects.get(id=id)
        detalles = Detalle.objects.filter(id_prod=id)
        return render(request, 'Movimiento/verventas.html', {'prod': prod, 'detalle': detalles})
    except:
        prod = {}
        detalles = {}
        return render(request, 'Movimiento/verventas.html', {'prod': prod, 'detalle': detalles})


@login_required
def ver_movi_prod(request, id):
    prod = Producto.objects.get(id=id)
    mx = Detalle.objects.filter(id_prod=id).aggregate(tx=Max('total'))
    mn = Detalle.objects.filter(id_prod=id).aggregate(tm=Min('total'))
    v = Detalle.objects.filter(id_prod=id).count()
    
    
    if request.method == 'POST':
        if Movimientos.objects.filter(id_prod=id):
            datos = Movimientos.objects.filter(
                id_prod=id, fecha__range=[request.POST['inicio'], request.POST['fin']], tienda=prod.tienda)
        else:
            datos = {}
    else:
        if Movimientos.objects.filter(id_prod=id):
            datos = Movimientos.objects.filter(id_prod=id)
        else:
            datos = {}

    return render(request, 'Movimiento/vermoviprod.html', {'prod': datos, 'p': prod, 'mx': mx['tx'], 'mn': mn['tm'], 'v': v})


@login_required
def ver_movi_prod_deta_fac(request, f):
    ver = ProductoFactura.objects.get(factura=f)
    datos = DetalleFactura.objects.filter(factura=f)
    return render(request, 'Movimiento/verfacturadetalleprod.html', {'ver': ver, 'detalle': datos})
