{% extends 'Base/base.html' %}
{% block title %}Listado Todos Pagos Creditos{% endblock %}

{% block content %}



<div class="content-wrapper">

  <div class="container-xxl flex-grow-1 container-p-y">

    <div class="row">
      <!-- Basic Layout -->
      <div class="col-md-12">
        <div class="card md-6">
          <div class="card-header d-flex align-items-center justify-content-between">
            <h5 class="mb-0">Listado de Pago Creditos</h5>
            <small class="text-muted float-end">Lista Pago Credito</small>
          </div>
          <div class="card-body">
            <div class="table-responsive text-nowrap">

              <input class="form-control col-md-3 light-table-filter" data-table="order-table" type="text"
                placeholder="Buscar.." style="border: 1px solid black;" name="buscar" autofocus="buscar"><br>

              <div class="container" align="right">
                <a href="{% url 'PDFCREDITO' %}">
                  <i style="color: purple; font-size: 35px;" class='bx bxs-file-export' title="Estado de Cuenta"></i>
                </a>
              </div><br>

              <table class="table table-bordered order-table">
                <thead>
                  <tr>
                    <th>Credito</th>
                    <th>Nit</th>
                    <th>Nombre de Cliente</th>
                    <th>Direccion de Cliente</th>
                    <th>Credito</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for c in credito %}
                  <tr>
                    <td>{{c.factura}}</td>
                    <td>{{c.nit}}</td>
                    <td>{{c.factura.nombre}}</td>
                    <td>{{c.factura.direccion}}</td>
                    <td>Q.{{c.abono}}</td>
                    <td>
                      <a href="{% url 'PDFCOMPROBANTE' c.factura %}">
                        <i style="color: blue; font-size: 25px;" class='bx bx-notepad' title="Comprobante"></i>
                      </a>
                      {% if c.estado == 1 %}
                      <a href="#">
                        <i style="color: red; font-size: 25px;" class='bx bx-rfid' title="Eliminar"></i>
                      </a>
                      {% else %}
                      {% endif %}
                    </td>
                  </tr>
                  {% empty %}
                  <caption>SIN PAGOS</caption>
                  {% endfor %}

                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

    </div>

  </div>

</div>

<script type="text/javascript">
  (function (document) {
    'use strict';

    var LightTableFilter = (function (Arr) {

      var _input;

      function _onInputEvent(e) {
        _input = e.target;
        var tables = document.getElementsByClassName(_input.getAttribute('data-table'));
        Arr.forEach.call(tables, function (table) {
          Arr.forEach.call(table.tBodies, function (tbody) {
            Arr.forEach.call(tbody.rows, _filter);
          });
        });
      }

      function _filter(row) {
        var text = row.textContent.toLowerCase(), val = _input.value.toLowerCase();
        row.style.display = text.indexOf(val) === -1 ? 'none' : 'table-row';
      }

      return {
        init: function () {
          var inputs = document.getElementsByClassName('light-table-filter');
          Arr.forEach.call(inputs, function (input) {
            input.oninput = _onInputEvent;
          });
        }
      };
    })(Array.prototype);

    document.addEventListener('readystatechange', function () {
      if (document.readyState === 'complete') {
        LightTableFilter.init();
      }
    });

  })(document);
</script>

<script type="text/javascript">
  function popUp(URL) {
    window.open(URL, 'Ver Compra', 'toolbar=0,scrollbars=0,location=0,statusbar=0,menubar=0,resizable=1,width=500,height=400,left = 390,top = 50');
  }
</script>

{% endblock %}