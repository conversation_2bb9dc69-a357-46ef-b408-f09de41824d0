(function(){var a=window.AmCharts;a.GaugeAxis=a.Class({construct:function(b){this.cname="GaugeAxis";this.radius="95%";this.createEvents("rollOverBand","rollOutBand","clickBand");this.labelsEnabled=!0;this.startAngle=-120;this.endAngle=120;this.startValue=0;this.endValue=200;this.gridCount=5;this.tickLength=10;this.minorTickLength=5;this.tickColor="#555555";this.labelFrequency=this.tickThickness=this.tickAlpha=1;this.inside=!0;this.labelOffset=10;this.showLastLabel=this.showFirstLabel=!0;this.axisThickness=1;this.axisColor="#000000";this.axisAlpha=1;this.gridInside=!0;this.topTextYOffset=0;this.topTextBold=!0;this.bottomTextYOffset=0;this.bottomTextBold=!0;this.centerY=this.centerX="0%";this.bandOutlineAlpha=this.bandOutlineThickness=0;this.bandOutlineColor="#000000";this.bandAlpha=1;this.bcn="gauge-axis";a.applyTheme(this,b,"GaugeAxis")},value2angle:function(b){return(b-this.startValue)/(this.endValue-this.startValue)*(this.endAngle-this.startAngle)+this.startAngle},setTopText:function(f){if(void 0!==f){this.topText=f;var d=this.chart;if(this.axisCreated){this.topTF&&this.topTF.remove();var h=this.topTextFontSize;h||(h=d.fontSize);var g=this.topTextColor;g||(g=d.color);f=a.text(d.container,f,g,d.fontFamily,h,void 0,this.topTextBold);a.setCN(d,f,"axis-top-label");f.translate(this.centerXReal,this.centerYReal-this.radiusReal/2+this.topTextYOffset);this.set.push(f);this.topTF=f}}},setBottomText:function(f){if(void 0!==f){this.bottomText=f;var d=this.chart;if(this.axisCreated){this.bottomTF&&this.bottomTF.remove();var h=this.bottomTextFontSize;h||(h=d.fontSize);var g=this.bottomTextColor;g||(g=d.color);f=a.text(d.container,f,g,d.fontFamily,h,void 0,this.bottomTextBold);a.setCN(d,f,"axis-bottom-label");f.translate(this.centerXReal,this.centerYReal+this.radiusReal/2+this.bottomTextYOffset);this.bottomTF=f;this.set.push(f)}}},draw:function(){var an=this.chart,am=an.container.set();this.set=am;a.setCN(an,am,this.bcn);a.setCN(an,am,this.bcn+"-"+this.id);an.graphsSet.push(am);this.bandSet=an.container.set();this.set.push(this.bandSet);var al=this.startValue,ak=this.endValue,ai=this.valueInterval;isNaN(ai)&&(ai=(ak-al)/this.gridCount);var af=this.minorTickInterval;isNaN(af)&&(af=ai/5);var ad=this.startAngle,ah=this.endAngle,ag=this.tickLength,ac=(ak-al)/ai+1,aj=(ah-ad)/(ac-1);this.singleValueAngle=aj/ai;var ae=an.container,P=this.tickColor,j=this.tickAlpha,s=this.tickThickness,af=ai/af,i=aj/af,O=this.minorTickLength,N=this.labelFrequency,R=this.radiusReal;this.inside||(R-=15);this.radiusRealReal=R;var ab=an.centerX+a.toCoordinate(this.centerX,an.realWidth),aa=an.centerY+a.toCoordinate(this.centerY,an.realHeight);this.centerXReal=ab;this.centerYReal=aa;var V={fill:this.axisColor,"fill-opacity":this.axisAlpha,"stroke-width":0,"stroke-opacity":0},Y,X;this.gridInside?X=Y=R:(Y=R-ag,X=Y+O);this.minorTickRadius=X;this.drawBands();var Z=this.axisThickness/2,ah=a.wedge(ae,ab,aa,ad,ah-ad,Y+Z,Y+Z,Y-Z,0,V);a.setCN(an,ah.wedge,"axis-line");am.push(ah);ah=a.doNothing;a.isModern||(ah=Math.round);V=a.getDecimals(al);Y=a.getDecimals(ak);ak=a.getDecimals(ai);ak=Math.max(ak,V,Y);ai=a.roundTo(ai,ak+1);for(V=0;V<ac;V++){Z=a.roundTo(al+V*ai,ak);Y=ad+V*aj;var T=ah(ab+R*Math.sin(Y/180*Math.PI)),S=ah(aa-R*Math.cos(Y/180*Math.PI)),M=ah(ab+(R-ag)*Math.sin(Y/180*Math.PI)),o=ah(aa-(R-ag)*Math.cos(Y/180*Math.PI)),T=a.line(ae,[T,M],[S,o],P,j,s,0,!1,!1,!0);a.setCN(an,T,"axis-tick");am.push(T);T=-1;M=this.labelOffset;this.inside||(M=-M-ag,T=1);var S=ab+(R-ag-M)*Math.sin(Y/180*Math.PI),M=aa-(R-ag-M)*Math.cos(Y/180*Math.PI),W=this.fontSize;isNaN(W)&&(W=an.fontSize);var o=Math.sin((Y-90)/180*Math.PI),d=Math.cos((Y-90)/180*Math.PI);if(0<N&&this.labelsEnabled&&V/N==Math.round(V/N)&&(this.showLastLabel||V!=ac-1)&&(this.showFirstLabel||0!==V)){var U;U=this.usePrefixes?a.addPrefix(Z,an.prefixesOfBigNumbers,an.prefixesOfSmallNumbers,an.nf,!0):a.formatNumber(Z,an.nf,ak);var Q=this.unit;Q&&(U="left"==this.unitPosition?Q+U:U+Q);(Q=this.labelFunction)&&(U=Q(Z));Z=this.color;void 0===Z&&(Z=an.color);Z=a.text(ae,U,Z,an.fontFamily,W);a.setCN(an,Z,"axis-label");W=Z.getBBox();Z.translate(S+T*W.width/2*d,M+T*W.height/2*o);am.push(Z)}if(V<ac-1){for(Z=1;Z<af;Z++){o=Y+i*Z,T=ah(ab+X*Math.sin(o/180*Math.PI)),S=ah(aa-X*Math.cos(o/180*Math.PI)),M=ah(ab+(X-O)*Math.sin(o/180*Math.PI)),o=ah(aa-(X-O)*Math.cos(o/180*Math.PI)),T=a.line(ae,[T,M],[S,o],P,j,s,0,!1,!1,!0),a.setCN(an,T,"axis-tick-minor"),am.push(T)}}}this.axisCreated=!0;this.setTopText(this.topText);this.setBottomText(this.bottomText);an=an.graphsSet.getBBox();this.width=an.width;this.height=an.height},drawBands:function(){var e=this.bands;if(e){for(var d=0;d<e.length;d++){var f=e[d];f&&(f.axis=this,a.processObject(f,a.GaugeBand,this.theme),f.draw(f.startValue,f.endValue))}}},fireEvent:function(e,d,f){this.fire({type:e,dataItem:d,chart:this,event:f})},addEventListeners:function(f,d){var h=this,g=h.chart;f.mouseover(function(b){g.showBalloon(d.balloonText,d.color,!0);h.fireEvent("rollOverBand",d,b)}).mouseout(function(b){g.hideBalloon();h.fireEvent("rollOutBand",d,b)}).click(function(b){h.fireEvent("clickBand",d,b);a.getURL(d.url,g.urlTarget)}).touchend(function(b){h.fireEvent("clickBand",d,b);a.getURL(d.url,g.urlTarget)})}})})();(function(){var a=window.AmCharts;a.GaugeArrow=a.Class({construct:function(b){this.cname="GaugeArrow";this.color="#000000";this.nailAlpha=this.alpha=1;this.startWidth=this.nailRadius=8;this.endWidth=0;this.borderAlpha=1;this.radius="90%";this.nailBorderAlpha=this.innerRadius=0;this.nailBorderThickness=1;this.frame=0;a.applyTheme(this,b,"GaugeArrow")},setValue:function(d){var c=this.chart;c?c.setValue?c.setValue(this,d):this.previousValue=this.value=d:this.previousValue=this.value=d}});a.GaugeBand=a.Class({construct:function(){this.cname="GaugeBand";this.frame=0},draw:function(y,x){var v=this.axis;this.bandGraphics&&this.bandGraphics.remove();var u=v.chart,s=v.startAngle,o=v.radiusRealReal,i=v.singleValueAngle,r=u.container,q=v.minorTickLength,d=a.toCoordinate(this.radius,o);isNaN(d)&&(d=v.minorTickRadius);o=a.toCoordinate(this.innerRadius,o);isNaN(o)&&(o=d-q);var s=s+i*(y-v.startValue),q=i*(x-y),t=this.outlineColor;void 0===t&&(t=v.bandOutlineColor);var j=this.outlineThickness;isNaN(j)&&(j=v.bandOutlineThickness);var z=this.outlineAlpha;isNaN(z)&&(z=v.bandOutlineAlpha);i=this.alpha;isNaN(i)&&(i=v.bandAlpha);t={fill:this.color,stroke:t,"stroke-width":j,"stroke-opacity":z};this.url&&(t.cursor="pointer");j=this.gradientRatio;j||(j=v.bandMegaRatio);r=a.wedge(r,v.centerXReal,v.centerYReal,s,q,d,d,o,0,t,j,void 0,void 0,"radial");a.setCN(u,r.wedge,"axis-band");void 0!==this.id&&a.setCN(u,r.wedge,"axis-band-"+this.id);r.setAttr("opacity",i);v.bandSet.push(r);this.bandGraphics=r;this.currentStartValue=y;this.currentEndValue=x;v.addEventListeners(r,this)},update:function(){var e=this.axis,d=e.chart;if(e&&e.value2angle){if(this.frame>=d.totalFrames){d=this.endValue,e=this.startValue}else{this.frame++;var f=a.getEffect(d.startEffect),e=a[f](0,this.frame,this.previousStartValue,this.startValue-this.previousStartValue,d.totalFrames),d=a[f](0,this.frame,this.previousEndValue,this.endValue-this.previousEndValue,d.totalFrames);isNaN(e)&&(e=this.startValue);isNaN(d)&&(d=this.endValue)}e==this.currentStartValue&&d==this.currentEndValue||this.draw(e,d)}},setStartValue:function(b){this.previousStartValue=this.startValue;this.startValue=b;this.frame=0},setEndValue:function(b){this.previousEndValue=this.endValue;this.endValue=b;this.frame=0}})})();(function(){var a=window.AmCharts;a.AmAngularGauge=a.Class({inherits:a.AmChart,construct:function(b){this.cname="AmAngularGauge";a.AmAngularGauge.base.construct.call(this,b);this.theme=b;this.type="gauge";this.minRadius=this.marginRight=this.marginBottom=this.marginTop=this.marginLeft=10;this.faceColor="#FAFAFA";this.faceAlpha=0;this.faceBorderWidth=1;this.faceBorderColor="#555555";this.faceBorderAlpha=0;this.arrows=[];this.axes=[];this.startDuration=1;this.startEffect="easeOutSine";this.adjustSize=!0;this.extraHeight=this.extraWidth=0;a.applyTheme(this,b,this.cname)},addAxis:function(b){b.chart=this;this.axes.push(b)},formatString:function(d,c){return d=a.formatValue(d,c,["value"],this.nf,"",this.usePrefixes,this.prefixesOfSmallNumbers,this.prefixesOfBigNumbers)},initChart:function(){a.AmAngularGauge.base.initChart.call(this);var f;0===this.axes.length&&(f=new a.GaugeAxis(this.theme),this.addAxis(f));var d;for(d=0;d<this.axes.length;d++){f=this.axes[d],f=a.processObject(f,a.GaugeAxis,this.theme),f.id||(f.id="axisAuto"+d+"_"+(new Date).getTime()),f.chart=this,this.axes[d]=f}var h=this.arrows;for(d=0;d<h.length;d++){f=h[d];f=a.processObject(f,a.GaugeArrow,this.theme);f.id||(f.id="arrowAuto"+d+"_"+(new Date).getTime());f.chart=this;h[d]=f;var g=f.axis;a.isString(g)&&(f.axis=a.getObjById(this.axes,g));f.axis||(f.axis=this.axes[0]);isNaN(f.value)&&f.setValue(f.axis.startValue);isNaN(f.previousValue)&&(f.previousValue=f.axis.startValue)}this.setLegendData(h);this.drawChart();this.totalFrames=this.startDuration*a.updateRate},drawChart:function(){a.AmAngularGauge.base.drawChart.call(this);var v=this.container,u=this.updateWidth();this.realWidth=u;var t=this.updateHeight();this.realHeight=t;var s=a.toCoordinate,q=s(this.marginLeft,u),j=s(this.marginRight,u),i=s(this.marginTop,t)+this.getTitleHeight(),o=s(this.marginBottom,t),m=s(this.radius,u,t),s=u-q-j,d=t-i-o+this.extraHeight;m||(m=Math.min(s,d)/2);m<this.minRadius&&(m=this.minRadius);this.radiusReal=m;this.centerX=(u-q-j)/2+q;this.centerY=(t-i-o)/2+i+this.extraHeight/2;isNaN(this.gaugeX)||(this.centerX=this.gaugeX);isNaN(this.gaugeY)||(this.centerY=this.gaugeY);var u=this.faceAlpha,t=this.faceBorderAlpha,r;if(0<u||0<t){r=a.circle(v,m,this.faceColor,u,this.faceBorderWidth,this.faceBorderColor,t,!1),r.translate(this.centerX,this.centerY),r.toBack(),(v=this.facePattern)&&r.pattern(v,NaN,this.path)}for(u=m=v=0;u<this.axes.length;u++){t=this.axes[u],q=t.radius,t.radiusReal=a.toCoordinate(q,this.radiusReal),t.draw(),j=1,-1!==String(q).indexOf("%")&&(j=1+(100-Number(q.substr(0,q.length-1)))/100),t.width*j>v&&(v=t.width*j),t.height*j>m&&(m=t.height*j)}(u=this.legend)&&u.invalidateSize();if(this.adjustSize&&!this.sizeAdjusted){r&&(r=r.getBBox(),r.width>v&&(v=r.width),r.height>m&&(m=r.height));r=0;if(d>m||s>v){r=Math.min(d-m,s-v)}5<r&&(this.extraHeight=r,this.sizeAdjusted=!0,this.validateNow())}s=this.arrows.length;for(u=0;u<s;u++){d=this.arrows[u],d.drawnAngle=NaN}this.dispDUpd()},validateSize:function(){this.extraHeight=this.extraWidth=0;this.chartCreated=this.sizeAdjusted=!1;a.AmAngularGauge.base.validateSize.call(this)},addArrow:function(b){this.arrows.push(b)},removeArrow:function(b){a.removeFromArray(this.arrows,b);this.validateNow()},removeAxis:function(b){a.removeFromArray(this.axes,b);this.validateNow()},drawArrow:function(A,y){A.set&&A.set.remove();var x=this.container;A.set=x.set();a.setCN(this,A.set,"gauge-arrow");a.setCN(this,A.set,"gauge-arrow-"+A.id);var u=A.axis,s=u.radiusReal,o=u.centerXReal,i=u.centerYReal,r=A.startWidth,q=A.endWidth,d=a.toCoordinate(A.innerRadius,u.radiusReal),t=a.toCoordinate(A.radius,u.radiusReal);u.inside||(t-=15);var j=A.nailColor;j||(j=A.color);var B=A.nailColor;B||(B=A.color);0<A.nailRadius&&(j=a.circle(x,A.nailRadius,j,A.nailAlpha,A.nailBorderThickness,j,A.nailBorderAlpha),a.setCN(this,j,"gauge-arrow-nail"),A.set.push(j),j.translate(o,i));isNaN(t)&&(t=s-u.tickLength);var u=Math.sin(y/180*Math.PI),s=Math.cos(y/180*Math.PI),j=Math.sin((y+90)/180*Math.PI),v=Math.cos((y+90)/180*Math.PI),x=a.polygon(x,[o-r/2*j+d*u,o+t*u-q/2*j,o+t*u+q/2*j,o+r/2*j+d*u],[i+r/2*v-d*s,i-t*s+q/2*v,i-t*s-q/2*v,i-r/2*v-d*s],A.color,A.alpha,1,B,A.borderAlpha,void 0,!0);a.setCN(this,x,"gauge-arrow");A.set.push(x);this.graphsSet.push(A.set);A.hidden&&this.hideArrow(A)},setValue:function(e,d){e.axis&&e.axis.value2angle&&(e.frame=0,e.previousValue=e.value);e.value=d;var f=this.legend;f&&f.updateValues();this.accessible&&this.background&&this.makeAccessible(this.background,d)},handleLegendEvent:function(d){var c=d.type;d=d.dataItem;if(!this.legend.data&&d){switch(c){case"hideItem":this.hideArrow(d);break;case"showItem":this.showArrow(d)}}},hideArrow:function(b){b.set.hide();b.hidden=!0;this.legend&&this.legend.invalidateSize()},showArrow:function(b){b.set.show();b.hidden=!1;this.legend&&this.legend.invalidateSize()},updateAnimations:function(){a.AmAngularGauge.base.updateAnimations.call(this);for(var f=this.arrows.length,d,j,i=0;i<f;i++){d=this.arrows[i],d.axis&&d.axis.value2angle&&(d.frame>=this.totalFrames?j=d.value:(d.frame++,d.clockWiseOnly&&d.value<d.previousValue&&(j=d.axis,d.previousValue-=j.endValue-j.startValue),j=a.getEffect(this.startEffect),j=a[j](0,d.frame,d.previousValue,d.value-d.previousValue,this.totalFrames),isNaN(j)&&(j=d.value)),j=d.axis.value2angle(j),d.drawnAngle!=j&&(this.drawArrow(d,j),d.drawnAngle=j))}f=this.axes;for(d=f.length-1;0<=d;d--){if(j=f[d],j.bands){for(i=j.bands.length-1;0<=i;i--){var h=j.bands[i];h.update&&h.update()}}}}})})();