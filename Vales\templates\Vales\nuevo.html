{% extends 'Base/base.html' %}
{% block title %}Nuevo Vale{% endblock %}

{% block content %}

{% if messages %}
{% for message in messages %}
{% if message.tags == "success" %}
<script>
    Swal.fire({
        "title": "Informacion Sistema",
        "text": "Vale Finalizado!",
        "html": "<a href='{% url 'PDFVale' message %}'' class='btn btn-danger'>PDF Vale</a>",
        "icon": "{{message.tags}}"
    })
</script>
{% else %}
<script>
    Swal.fire({
        "title": "Informacion Sistema",
        "text": "Vale Descartado!",
        "icon": "{{message.tags}}"
    })
</script>
{% endif %}
{% endfor %}
{% endif %}

<div class="content-wrapper">

    {{form.errors}}

    <div class="container-xxl flex-grow-1 container-p-y">


        <!-- Basic Layout -->
        <div class="col-md-12">
            <div class="card md-6">
                <div class="card-header d-flex align-items-center justify-content-between">
                    <h5 class="mb-0">Formulario Nuevo Vale</h5>
                    <small class="text-muted float-end">Vales</small>
                </div>
                <div class="card-body">
                    <form action="#" method="POST" enctype="multipart/form-data">{% csrf_token %}
                        <div class="row">
                            <div class="col-md-6">
                                <label>Sucursal Origen</label>
                                {% if user.rol == "admin" %}
                                <input type="text" name="origen" class="form-control" required placeholder="Origen">
                                {% else %}
                                <input type="text" name="origen" class="form-control" required value="{{user.tienda}}"
                                    readonly>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <label>Sucursal Destino</label>
                                {{form.destino}}
                            </div>
                            <div class="col-md-12"><br>
                                <label>Observaciones</label>
                                {{form.obs}}
                            </div>
                        </div><br>
                        <div class="row">
                            <div class="col-md-4">
                                <label>Fecha</label>
                                <input type="text" class="form-control" readonly value="{% now 'd-m-Y H:m:s' %}" />
                            </div>
                            <div class="col-md-4">
                                <label>Usuario</label>
                                <input type="text" class="form-control" readonly value="{{user.username}}" />
                            </div>
                        </div><br>
                        <div class="row">
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary">Iniciar</button>
                                <button type="reset" class="btn btn-danger">Cancelar</button>
                            </div>
                        </div>
                </div>
                </form>
            </div>
        </div>


    </div>

</div>



{% endblock %}