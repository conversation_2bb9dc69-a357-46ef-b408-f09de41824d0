{% extends 'Base/base.html' %}
{% block title %}Nueva Consignacion{% endblock %}

{% block content %}

{% if messages %}
{% for message in messages %}
<script>
    Swal.fire({
        "title": "Informacion Sistema",
        "text": "{{message}}",
        "icon": "{{message.tags}}"
    })
</script>
{% endfor %}
{% endif %}

{{form.errors}}

<div class="content-wrapper">

    <div class="container-xxl flex-grow-1 container-p-y">


        <!-- Basic Layout -->
        <div class="col-md-12">
            <div class="card md-6">
                <div class="card-header d-flex align-items-center justify-content-between">
                    <h5 class="mb-0">Formulario Nueva Consignacion</h5>
                    <small class="text-muted float-end">Consignaciones</small>
                </div>
                <div class="card-body">
                    <form action="#" method="POST" enctype="multipart/form-data">{% csrf_token %}
                        <div class="row">
                            <div class="col-md-4">
                                <label>Numero Consignacion</label>
                                {{form.traslado}}
                            </div>
                            <div class="col-md-4">
                                <label>Proveedor</label>
                                <select name="id_prov" class="form-control" required>
                                    <option value="">----------------</option>
                                    {% for p in prov %}
                                    <option value="{{p.nit}}">{{p.nombre}}</option>
                                    {% endfor%}
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label>Fecha Consignacion</label>
                                {{form.fecha}}
                            </div>
                        </div><br>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <label>Tienda</label>
                                {% if user.rol == 'admin' %}
                                <select name="tienda" class="form-control" required>
                                  <option value="Todas">Todas</option>
                                  <option value="Estanzuela">Estanzuela</option>
                                  <option value="Teculutan">Teculutan</option>
                                  <option value="Zacapa">Zacapa</option>
                                  <option value="Gualan">Gualan</option>
                                </select>
                                {% else %}
                                <select name="tienda" class="form-control" required>
                                  <option value="{{user.tienda}}">{{user.tienda}}</option>
                                </select>
                                {% endif %}
                              </div>
                            <div class="col-md-4">
                                <label>Usuario</label>
                                <input type="text" class="form-control" readonly value="{{user.username}}" />
                            </div>
                        </div><br>
                        <div class="row">
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary">Guardar</button>
                                <button type="reset" class="btn btn-danger">Cancelar</button>
                            </div>
                        </div>
                </div>
                </form>
            </div>
        </div>


    </div>

</div>



{% endblock %}