(function(){var a=window.AmCharts;a.AmRectangularChart=a.Class({inherits:a.AmCoordinate<PERSON><PERSON>,construct:function(b){a.AmRectangularChart.base.construct.call(this,b);this.theme=b;this.createEvents("zoomed","changed");this.marginRight=this.marginBottom=this.marginTop=this.marginLeft=20;this.depth3D=this.angle=0;this.plotAreaFillColors="#FFFFFF";this.plotAreaFillAlphas=0;this.plotAreaBorderColor="#000000";this.plotAreaBorderAlpha=0;this.maxZoomFactor=20;this.zoomOutButtonImageSize=19;this.zoomOutButtonImage="lens";this.zoomOutText="Show all";this.zoomOutButtonColor="#e5e5e5";this.zoomOutButtonAlpha=0;this.zoomOutButtonRollOverAlpha=1;this.zoomOutButtonPadding=8;this.trendLines=[];this.autoMargins=!0;this.marginsUpdated=!1;this.autoMarginOffset=10;a.applyTheme(this,b,"AmRectangularChart")},initChart:function(){a.AmRectangularChart.base.initChart.call(this);this.updateDxy();!this.marginsUpdated&&this.autoMargins&&(this.resetMargins(),this.drawGraphs=!1);this.processScrollbars();this.updateMargins();this.updatePlotArea();this.updateScrollbars();this.updateTrendLines();this.updateChartCursor();this.updateValueAxes();this.scrollbarOnly||this.updateGraphs()},drawChart:function(){a.AmRectangularChart.base.drawChart.call(this);this.drawPlotArea();if(a.ifArray(this.chartData)){var b=this.chartCursor;b&&b.draw()}},resetMargins:function(){var f={},e;if("xy"==this.type){var j=this.xAxes,i=this.yAxes;for(e=0;e<j.length;e++){var h=j[e];h.ignoreAxisWidth||(h.setOrientation(!0),h.fixAxisPosition(),f[h.position]=!0)}for(e=0;e<i.length;e++){j=i[e],j.ignoreAxisWidth||(j.setOrientation(!1),j.fixAxisPosition(),f[j.position]=!0)}}else{i=this.valueAxes;for(e=0;e<i.length;e++){j=i[e],j.ignoreAxisWidth||(j.setOrientation(this.rotate),j.fixAxisPosition(),f[j.position]=!0)}(e=this.categoryAxis)&&!e.ignoreAxisWidth&&(e.setOrientation(!this.rotate),e.fixAxisPosition(),e.fixAxisPosition(),f[e.position]=!0)}f.left&&(this.marginLeft=0);f.right&&(this.marginRight=0);f.top&&(this.marginTop=0);f.bottom&&(this.marginBottom=0);this.fixMargins=f},measureMargins:function(){var t=this.valueAxes,s,r=this.autoMarginOffset,q=this.fixMargins,n=this.realWidth,k=this.realHeight,o=r,p=r,j=n;s=k;var i;for(i=0;i<t.length;i++){t[i].handleSynchronization(),s=this.getAxisBounds(t[i],o,j,p,s),o=Math.round(s.l),j=Math.round(s.r),p=Math.round(s.t),s=Math.round(s.b)}if(t=this.categoryAxis){s=this.getAxisBounds(t,o,j,p,s),o=Math.round(s.l),j=Math.round(s.r),p=Math.round(s.t),s=Math.round(s.b)}q.left&&o<r&&(this.marginLeft=Math.round(-o+r),!isNaN(this.minMarginLeft)&&this.marginLeft<this.minMarginLeft&&(this.marginLeft=this.minMarginLeft));q.right&&j>=n-r&&(this.marginRight=Math.round(j-n+r),!isNaN(this.minMarginRight)&&this.marginRight<this.minMarginRight&&(this.marginRight=this.minMarginRight));q.top&&p<r+this.titleHeight&&(this.marginTop=Math.round(this.marginTop-p+r+this.titleHeight),!isNaN(this.minMarginTop)&&this.marginTop<this.minMarginTop&&(this.marginTop=this.minMarginTop));q.bottom&&s>k-r&&(this.marginBottom=Math.round(this.marginBottom+s-k+r),!isNaN(this.minMarginBottom)&&this.marginBottom<this.minMarginBottom&&(this.marginBottom=this.minMarginBottom));this.initChart()},getAxisBounds:function(i,e,n,m,k){if(!i.ignoreAxisWidth){var j=i.labelsSet,l=i.tickLength;i.inside&&(l=0);if(j){switch(j=i.getBBox(),i.position){case"top":i=j.y;m>i&&(m=i);break;case"bottom":i=j.y+j.height;k<i&&(k=i);break;case"right":i=j.x+j.width+l+3;n<i&&(n=i);break;case"left":i=j.x-l,e>i&&(e=i)}}}return{l:e,t:m,r:n,b:k}},drawZoomOutButton:function(){var y=this;if(!y.zbSet){var x=y.container.set();y.zoomButtonSet.push(x);var w=y.color,v=y.fontSize,s=y.zoomOutButtonImageSize,r=y.zoomOutButtonImage.replace(/\.[a-z]*$/i,""),t=y.langObj.zoomOutText||y.zoomOutText,q=y.zoomOutButtonColor,o=y.zoomOutButtonAlpha,j=y.zoomOutButtonFontSize,e=y.zoomOutButtonPadding;isNaN(j)||(v=j);(j=y.zoomOutButtonFontColor)&&(w=j);var j=y.zoomOutButton,i;j&&(j.fontSize&&(v=j.fontSize),j.color&&(w=j.color),j.backgroundColor&&(q=j.backgroundColor),isNaN(j.backgroundAlpha)||(y.zoomOutButtonRollOverAlpha=j.backgroundAlpha));var z=j=0,z=y.pathToImages;if(r){if(a.isAbsolute(r)||void 0===z){z=""}i=y.container.image(z+r+y.extension,0,0,s,s);a.setCN(y,i,"zoom-out-image");x.push(i);i=i.getBBox();j=i.width+5}void 0!==t&&(w=a.text(y.container,t,w,y.fontFamily,v,"start"),a.setCN(y,w,"zoom-out-label"),v=w.getBBox(),z=i?i.height/2-3:v.height/2,w.translate(j,z),x.push(w));i=x.getBBox();w=1;a.isModern||(w=0);q=a.rect(y.container,i.width+2*e+5,i.height+2*e-2,q,1,1,q,w);q.setAttr("opacity",o);q.translate(-e,-e);a.setCN(y,q,"zoom-out-bg");x.push(q);q.toBack();y.zbBG=q;i=q.getBBox();x.translate(y.marginLeftReal+y.plotAreaWidth-i.width+e,y.marginTopReal+e);x.hide();x.mouseover(function(){y.rollOverZB()}).mouseout(function(){y.rollOutZB()}).click(function(){y.clickZB()}).touchstart(function(){y.rollOverZB()}).touchend(function(){y.rollOutZB();y.clickZB()});for(o=0;o<x.length;o++){x[o].attr({cursor:"pointer"})}void 0!==y.zoomOutButtonTabIndex&&(x.setAttr("tabindex",y.zoomOutButtonTabIndex),x.setAttr("role","menuitem"),x.keyup(function(c){13==c.keyCode&&y.clickZB()}));y.zbSet=x}},rollOverZB:function(){this.rolledOverZB=!0;this.zbBG.setAttr("opacity",this.zoomOutButtonRollOverAlpha)},rollOutZB:function(){this.rolledOverZB=!1;this.zbBG.setAttr("opacity",this.zoomOutButtonAlpha)},clickZB:function(){this.rolledOverZB=!1;this.zoomOut()},zoomOut:function(){this.zoomOutValueAxes()},drawPlotArea:function(){var t=this.dx,s=this.dy,r=this.marginLeftReal,q=this.marginTopReal,o=this.plotAreaWidth-1,n=this.plotAreaHeight-1,p=this.plotAreaFillColors,j=this.plotAreaFillAlphas,i=this.plotAreaBorderColor,e=this.plotAreaBorderAlpha;"object"==typeof j&&(j=j[0]);p=a.polygon(this.container,[0,o,o,0,0],[0,0,n,n,0],p,j,1,i,e,this.plotAreaMegaAngle);a.setCN(this,p,"plot-area");p.translate(r+t,q+s);this.set.push(p);0!==t&&0!==s&&(p=this.plotAreaFillColors,"object"==typeof p&&(p=p[0]),p=a.adjustLuminosity(p,-0.15),o=a.polygon(this.container,[0,t,o+t,o,0],[0,s,s,0,0],p,j,1,i,e),a.setCN(this,o,"plot-area-bottom"),o.translate(r,q+n),this.set.push(o),t=a.polygon(this.container,[0,0,t,t,0],[0,n,n+s,s,0],p,j,1,i,e),a.setCN(this,t,"plot-area-left"),t.translate(r,q),this.set.push(t));(r=this.bbset)&&this.scrollbarOnly&&r.remove()},updatePlotArea:function(){var f=this.updateWidth(),e=this.updateHeight(),h=this.container;this.realWidth=f;this.realWidth=e;h&&this.container.setSize(f,e);var h=this.marginLeftReal,g=this.marginTopReal,f=f-h-this.marginRightReal-this.dx,e=e-g-this.marginBottomReal;1>f&&(f=1);1>e&&(e=1);this.plotAreaWidth=Math.round(f);this.plotAreaHeight=Math.round(e);this.plotBalloonsSet.translate(h,g)},updateDxy:function(){this.dx=Math.round(this.depth3D*Math.cos(this.angle*Math.PI/180));this.dy=Math.round(-this.depth3D*Math.sin(this.angle*Math.PI/180));this.d3x=Math.round(this.columnSpacing3D*Math.cos(this.angle*Math.PI/180));this.d3y=Math.round(-this.columnSpacing3D*Math.sin(this.angle*Math.PI/180))},updateMargins:function(){var b=this.getTitleHeight();this.titleHeight=b;this.marginTopReal=this.marginTop-this.dy;this.fixMargins&&!this.fixMargins.top&&(this.marginTopReal+=b);this.marginBottomReal=this.marginBottom;this.marginLeftReal=this.marginLeft;this.marginRightReal=this.marginRight},updateValueAxes:function(){var e=this.valueAxes,d;for(d=0;d<e.length;d++){var f=e[d];this.setAxisRenderers(f);this.updateObjectSize(f)}},setAxisRenderers:function(b){b.axisRenderer=a.RecAxis;b.guideFillRenderer=a.RecFill;b.axisItemRenderer=a.RecItem;b.marginsChanged=!0},updateGraphs:function(){var e=this.graphs,d;for(d=0;d<e.length;d++){var f=e[d];f.index=d;f.rotate=this.rotate;this.updateObjectSize(f)}},updateObjectSize:function(b){b.width=this.plotAreaWidth-1;b.height=this.plotAreaHeight-1;b.x=this.marginLeftReal;b.y=this.marginTopReal;b.dx=this.dx;b.dy=this.dy},updateChartCursor:function(){var b=this.chartCursor;b&&(b=a.processObject(b,a.ChartCursor,this.theme),this.updateObjectSize(b),this.addChartCursor(b),b.chart=this)},processScrollbars:function(){var b=this.chartScrollbar;b&&(b=a.processObject(b,a.ChartScrollbar,this.theme),this.addChartScrollbar(b))},updateScrollbars:function(){},removeChartCursor:function(){a.callMethod("destroy",[this.chartCursor]);this.chartCursor=null},zoomTrendLines:function(){var e=this.trendLines,d;for(d=0;d<e.length;d++){var f=e[d];f.valueAxis.recalculateToPercents?f.set&&f.set.hide():(f.x=this.marginLeftReal,f.y=this.marginTopReal,f.draw())}},handleCursorValueZoom:function(){},addTrendLine:function(b){this.trendLines.push(b)},zoomOutValueAxes:function(){for(var d=this.valueAxes,c=0;c<d.length;c++){d[c].zoomOut()}},removeTrendLine:function(e){var d=this.trendLines,f;for(f=d.length-1;0<=f;f--){d[f]==e&&d.splice(f,1)}},adjustMargins:function(f,e){var h=f.position,g=f.scrollbarHeight+f.offset;f.enabled&&("top"==h?e?this.marginLeftReal+=g:this.marginTopReal+=g:e?this.marginRightReal+=g:this.marginBottomReal+=g)},getScrollbarPosition:function(f,e,j){var i="bottom",h="top";f.oppositeAxis||(h=i,i="top");f.position=e?"bottom"==j||"left"==j?i:h:"top"==j||"right"==j?i:h},updateChartScrollbar:function(j,i){if(j){j.rotate=i;var p=this.marginTopReal,o=this.marginLeftReal,l=j.scrollbarHeight,k=this.dx,m=this.dy,n=j.offset;"top"==j.position?i?(j.y=p,j.x=o-l-n):(j.y=p-l+m-n,j.x=o+k):i?(j.y=p+m,j.x=o+this.plotAreaWidth+k+n):(j.y=p+this.plotAreaHeight+n,j.x=this.marginLeftReal)}},showZB:function(d){var c=this.zbSet;d&&(c=this.zoomOutText,""!==c&&c&&this.drawZoomOutButton());if(c=this.zbSet){this.zoomButtonSet.push(c),d?c.show():c.hide(),this.rollOutZB()}},handleReleaseOutside:function(b){a.AmRectangularChart.base.handleReleaseOutside.call(this,b);(b=this.chartCursor)&&b.handleReleaseOutside&&b.handleReleaseOutside()},handleMouseDown:function(d){a.AmRectangularChart.base.handleMouseDown.call(this,d);var c=this.chartCursor;c&&c.handleMouseDown&&!this.rolledOverZB&&c.handleMouseDown(d)},update:function(){a.AmRectangularChart.base.update.call(this);this.chartCursor&&this.chartCursor.update&&this.chartCursor.update()},handleScrollbarValueZoom:function(b){this.relativeZoomValueAxes(b.target.valueAxes,b.relativeStart,b.relativeEnd);this.zoomAxesAndGraphs()},zoomValueScrollbar:function(f){if(f&&f.enabled){var e=f.valueAxes[0],h=e.relativeStart,g=e.relativeEnd;e.reversed&&(g=1-h,h=1-e.relativeEnd);f.percentZoom(h,g)}},zoomAxesAndGraphs:function(){if(!this.scrollbarOnly){var d=this.valueAxes,c;for(c=0;c<d.length;c++){d[c].zoom(this.start,this.end)}d=this.graphs;for(c=0;c<d.length;c++){d[c].zoom(this.start,this.end)}(c=this.chartCursor)&&c.clearSelection();this.zoomTrendLines()}},handleValueAxisZoomReal:function(f,e){var j=f.relativeStart,i=f.relativeEnd;if(j>i){var h=j,j=i,i=h}this.relativeZoomValueAxes(e,j,i);this.updateAfterValueZoom()},updateAfterValueZoom:function(){this.zoomAxesAndGraphs();this.zoomScrollbar()},relativeZoomValueAxes:function(f,e,l){this.hideBalloonReal();e=a.fitToBounds(e,0,1);l=a.fitToBounds(l,0,1);if(e>l){var k=e;e=l;l=k}var k=1/this.maxZoomFactor,j=a.getDecimals(k)+4;l-e<k&&(l=e+(l-e)/2,e=l-k/2,l+=k/2,1<l&&(e-=l-1,l=1),0>e&&(e=0,l=k));e=a.roundTo(e,j);l=a.roundTo(l,j);k=!1;if(f){for(j=0;j<f.length;j++){var i=f[j].zoomToRelativeValues(e,l,!0);i&&(k=i)}this.showZB()}return k},addChartCursor:function(b){a.callMethod("destroy",[this.chartCursor]);b&&(this.listenTo(b,"moved",this.handleCursorMove),this.listenTo(b,"zoomed",this.handleCursorZoom),this.listenTo(b,"zoomStarted",this.handleCursorZoomStarted),this.listenTo(b,"panning",this.handleCursorPanning),this.listenTo(b,"onHideCursor",this.handleCursorHide));this.chartCursor=b},handleCursorChange:function(){},handleCursorMove:function(f){var e,h=this.valueAxes;for(e=0;e<h.length;e++){if(!f.panning){var g=h[e];g&&g.showBalloon&&g.showBalloon(f.x,f.y)}}},handleCursorZoom:function(r){if(this.skipZoomed){this.skipZoomed=!1}else{var q=this.startX0,p=this.endX0,o=this.endY0,k=this.startY0,j=r.startX,m=r.endX,n=r.startY,i=r.endY;this.startX0=this.endX0=this.startY0=this.endY0=NaN;this.handleCursorZoomReal(q+j*(p-q),q+m*(p-q),k+n*(o-k),k+i*(o-k),r)}},handleCursorHide:function(){var d,c=this.valueAxes;for(d=0;d<c.length;d++){c[d].hideBalloon()}c=this.graphs;for(d=0;d<c.length;d++){c[d].hideBalloonReal()}}})})();(function(){var a=window.AmCharts;a.AmSerialChart=a.Class({inherits:a.AmRectangularChart,construct:function(d){this.type="serial";a.AmSerialChart.base.construct.call(this,d);this.cname="AmSerialChart";this.theme=d;this.columnSpacing=5;this.columnSpacing3D=0;this.columnWidth=0.8;var c=new a.CategoryAxis(d);c.chart=this;this.categoryAxis=c;this.zoomOutOnDataUpdate=!0;this.mouseWheelZoomEnabled=this.mouseWheelScrollEnabled=this.rotate=this.skipZoom=!1;this.minSelectedTime=0;a.applyTheme(this,d,this.cname)},initChart:function(){a.AmSerialChart.base.initChart.call(this);this.updateCategoryAxis(this.categoryAxis,this.rotate,"categoryAxis");if(this.dataChanged){this.parseData()}else{this.onDataUpdated()}this.drawGraphs=!0},onDataUpdated:function(){var f=this.countColumns(),e=this.chartData,j=this.graphs,i;for(i=0;i<j.length;i++){var h=j[i];h.data=e;h.columnCount=f}0<e.length&&(this.firstTime=this.getStartTime(e[0].time),this.lastTime=this.getEndTime(e[e.length-1].time));this.drawChart();this.autoMargins&&!this.marginsUpdated?(this.marginsUpdated=!0,this.measureMargins()):this.dispDUpd()},syncGrid:function(){if(this.synchronizeGrid){var i=this.valueAxes,e,n;if(0<i.length){var m=0;for(n=0;n<i.length;n++){e=i[n],m<e.gridCountReal&&(m=e.gridCountReal)}var k=!1;for(n=0;n<i.length;n++){if(e=i[n],e.gridCountReal<m){var j=(m-e.gridCountReal)/2,l=k=j;0!==j-Math.round(j)&&(k-=0.5,l+=0.5);0<=e.min&&0>e.min-k*e.step&&(l+=k,k=0);0>=e.max&&0<e.max+l*e.step&&(k+=l,l=0);j=a.getDecimals(e.step);e.minimum=a.roundTo(e.min-k*e.step,j);e.maximum=a.roundTo(e.max+l*e.step,j);e.setStep=e.step;k=e.strictMinMax=!0}}k&&this.updateAfterValueZoom();for(n=0;n<i.length;n++){e=i[n],e.minimum=NaN,e.maximum=NaN,e.setStep=NaN,e.strictMinMax=!1}}}},handleWheelReal:function(r,q){if(!this.wheelBusy){var p=this.categoryAxis,o=p.parseDates,j=p.minDuration(),n=1,m=1;this.mouseWheelZoomEnabled?q||(n=-1):q&&(n=-1);var i=this.chartCursor;if(i){var h=i.mouseX,i=i.mouseY;n!=m&&(h=this.rotate?i/this.plotAreaHeight:h/this.plotAreaWidth,n*=h,m*=1-h);h=0.05*(this.end-this.start);o&&(h=0.05*(this.endTime-this.startTime)/j);1>h&&(h=1);n*=h;m*=h;if(!o||p.equalSpacing){n=Math.round(n),m=Math.round(m)}}i=this.chartData.length;p=this.lastTime;h=this.firstTime;0>r?o?(i=this.endTime-this.startTime,o=this.startTime+n*j,j=this.endTime+m*j,0<m&&0<n&&j>=p&&(j=p,o=p-i),this.zoomToDates(new Date(o),new Date(j))):(0<m&&0<n&&this.end>=i-1&&(n=m=0),o=this.start+n,j=this.end+m,this.zoomToIndexes(o,j)):o?(i=this.endTime-this.startTime,o=this.startTime-n*j,j=this.endTime-m*j,0<m&&0<n&&o<=h&&(o=h,j=h+i),this.zoomToDates(new Date(o),new Date(j))):(0<m&&0<n&&1>this.start&&(n=m=0),o=this.start-n,j=this.end-m,this.zoomToIndexes(o,j))}},validateData:function(f){this.marginsUpdated=!1;this.zoomOutOnDataUpdate&&!f&&(this.endTime=this.end=this.startTime=this.start=NaN);var e=f=!1,h=!1,g=this.chartScrollbar;g&&(g.dragging&&(f=!0,g.handleDragStop()),g.resizingRight&&(h=!0,g.rightDragStop()),g.resizingLeft&&(e=!0,g.leftDragStop()));a.AmSerialChart.base.validateData.call(this);f&&g.handleDragStart();h&&g.rightDragStart();e&&g.leftDragStart()},drawChart:function(){if(0<this.realWidth&&0<this.realHeight){a.AmSerialChart.base.drawChart.call(this);var f=this.chartData;if(a.ifArray(f)){var e=this.chartScrollbar;!e||!this.marginsUpdated&&this.autoMargins||e.draw();(e=this.valueScrollbar)&&e.draw();var e=f.length-1,h,g;h=this.categoryAxis;if(h.parseDates&&!h.equalSpacing){if(h=this.startTime,g=this.endTime,isNaN(h)||isNaN(g)){h=this.firstTime,g=this.lastTime}}else{h=this.start;g=this.end;if(isNaN(h)||isNaN(g)){g=h=NaN}isNaN(h)&&(isNaN(this.startTime)||(h=this.getClosestIndex(f,"time",this.startTime,!0,0,f.length)));isNaN(g)&&(isNaN(this.endTime)||(g=this.getClosestIndex(f,"time",this.endTime,!1,0,f.length)));if(isNaN(h)||isNaN(g)){h=0,g=e}}this.endTime=this.startTime=this.end=this.start=void 0;this.zoom(h,g)}}else{this.cleanChart()}},cleanChart:function(){a.callMethod("destroy",[this.valueAxes,this.graphs,this.categoryAxis,this.chartScrollbar,this.chartCursor,this.valueScrollbar])},updateCategoryAxis:function(e,d,f){e.chart=this;e.id=f;e.rotate=d;e.setOrientation(!this.rotate);e.init();this.setAxisRenderers(e);this.updateObjectSize(e)},updateValueAxes:function(){a.AmSerialChart.base.updateValueAxes.call(this);var f=this.valueAxes,e;for(e=0;e<f.length;e++){var h=f[e],g=this.rotate;h.rotate=g;h.setOrientation(g);g=this.categoryAxis;if(!g.startOnAxis||g.parseDates){h.expandMinMax=!0}}},getStartTime:function(d){var c=this.categoryAxis;return a.resetDateToMin(new Date(d),c.minPeriod,1,c.firstDayOfWeek).getTime()},getEndTime:function(d){var c=a.extractPeriod(this.categoryAxis.minPeriod);return a.changeDate(new Date(d),c.period,c.count,!0).getTime()-1},updateMargins:function(){a.AmSerialChart.base.updateMargins.call(this);var b=this.chartScrollbar;b&&(this.getScrollbarPosition(b,this.rotate,this.categoryAxis.position),this.adjustMargins(b,this.rotate));if(b=this.valueScrollbar){this.getScrollbarPosition(b,!this.rotate,this.valueAxes[0].position),this.adjustMargins(b,!this.rotate)}},updateScrollbars:function(){a.AmSerialChart.base.updateScrollbars.call(this);this.updateChartScrollbar(this.chartScrollbar,this.rotate);this.updateChartScrollbar(this.valueScrollbar,!this.rotate)},zoom:function(e,d){var f=this.categoryAxis;f.parseDates&&!f.equalSpacing?(this.timeZoom(e,d),isNaN(e)&&this.zoomOutValueAxes()):this.indexZoom(e,d);(f=this.chartCursor)&&(f.pan||f.hideCursorReal());this.updateLegendValues()},timeZoom:function(i,e){var p=this.maxSelectedTime;isNaN(p)||(e!=this.endTime&&e-i>p&&(i=e-p),i!=this.startTime&&e-i>p&&(e=i+p));var o=this.minSelectedTime;if(0<o&&e-i<o){var m=Math.round(i+(e-i)/2),o=Math.round(o/2);i=m-o;e=m+o}o=this.chartData;m=this.categoryAxis;if(a.ifArray(o)&&(i!=this.startTime||e!=this.endTime)){var l=m.minDuration(),n=this.firstTime,j=this.lastTime;i||(i=n,isNaN(p)||(i=j-p));e||(e=j);i>j&&(i=j);e<n&&(e=n);i<n&&(i=n);e>j&&(e=j);e<i&&(e=i+l);e-i<l/5&&(e<j?e=i+l/5:i=e-l/5);this.startTime=i;this.endTime=e;p=o.length-1;l=this.getClosestIndex(o,"time",i,!0,0,p);o=this.getClosestIndex(o,"time",e,!1,l,p);m.timeZoom(i,e);m.zoom(l,o);this.start=a.fitToBounds(l,0,p);this.end=a.fitToBounds(o,0,p);this.zoomAxesAndGraphs();this.zoomScrollbar();this.fixCursor();this.showZB();this.syncGrid();this.updateColumnsDepth();this.dispatchTimeZoomEvent()}},showZB:function(){var d,c=this.categoryAxis;c&&c.parseDates&&!c.equalSpacing&&(this.startTime>this.firstTime&&(d=!0),this.endTime<this.lastTime&&(d=!0));0<this.start&&(d=!0);this.end<this.chartData.length-1&&(d=!0);if(c=this.valueAxes){c=c[0],isNaN(c.relativeStart)||(0!==a.roundTo(c.relativeStart,3)&&(d=!0),1!=a.roundTo(c.relativeEnd,3)&&(d=!0))}a.AmSerialChart.base.showZB.call(this,d)},updateAfterValueZoom:function(){a.AmSerialChart.base.updateAfterValueZoom.call(this);this.updateColumnsDepth()},indexZoom:function(f,e){var j=this.maxSelectedSeries,i=!1;isNaN(j)||(e!=this.end&&e-f>j&&(f=e-j,i=!0),f!=this.start&&e-f>j&&(e=f+j,i=!0));if(i&&(i=this.chartScrollbar)&&i.dragger){var h=i.dragger.getBBox();i.maxWidth=h.width;i.maxHeight=h.height}if(f!=this.start||e!=this.end){i=this.chartData.length-1,isNaN(f)&&(f=0,isNaN(j)||(f=i-j)),isNaN(e)&&(e=i),e<f&&(e=f),e>i&&(e=i),f>i&&(f=i-1),0>f&&(f=0),this.start=f,this.end=e,this.categoryAxis.zoom(f,e),this.zoomAxesAndGraphs(),this.zoomScrollbar(),this.fixCursor(),0!==f||e!=this.chartData.length-1?this.showZB(!0):this.showZB(!1),this.syncGrid(),this.updateColumnsDepth(),this.dispatchIndexZoomEvent()}},updateGraphs:function(){a.AmSerialChart.base.updateGraphs.call(this);var e=this.graphs,d;for(d=0;d<e.length;d++){var f=e[d];f.columnWidthReal=this.columnWidth;f.categoryAxis=this.categoryAxis;a.isString(f.fillToGraph)&&(f.fillToGraph=this.graphsById[f.fillToGraph])}},zoomAxesAndGraphs:function(){a.AmSerialChart.base.zoomAxesAndGraphs.call(this);this.updateColumnsDepth()},updateColumnsDepth:function(){if(0!==this.depth3D||0!==this.angle){var f,e=this.graphs,j;this.columnsArray=[];for(f=0;f<e.length;f++){j=e[f];var i=j.columnsArray;if(i){var h;for(h=0;h<i.length;h++){this.columnsArray.push(i[h])}}}this.columnsArray.sort(this.compareDepth);e=this.columnsSet;if(0<this.columnsArray.length){i=this.container.set();this.columnSet.push(i);for(f=0;f<this.columnsArray.length;f++){i.push(this.columnsArray[f].column.set)}j&&i.translate(j.x,j.y);this.columnsSet=i}a.remove(e)}},compareDepth:function(d,c){return d.depth>c.depth?1:-1},zoomScrollbar:function(){var e=this.chartScrollbar,d=this.categoryAxis;if(e){if(!this.zoomedByScrollbar){var f=e.dragger;f&&f.stop()}this.zoomedByScrollbar=!1;d.parseDates&&!d.equalSpacing?e.timeZoom(this.startTime,this.endTime):e.zoom(this.start,this.end)}this.zoomValueScrollbar(this.valueScrollbar)},updateTrendLines:function(){var e=this.trendLines,d;for(d=0;d<e.length;d++){var f=e[d],f=a.processObject(f,a.TrendLine,this.theme);e[d]=f;f.chart=this;f.id||(f.id="trendLineAuto"+d+"_"+(new Date).getTime());a.isString(f.valueAxis)&&(f.valueAxis=this.getValueAxisById(f.valueAxis));f.valueAxis||(f.valueAxis=this.valueAxes[0]);f.categoryAxis=this.categoryAxis}},countColumns:function(){var t=0,s=this.valueAxes.length,r=this.graphs.length,q,n,p=!1,o,j;for(j=0;j<s;j++){n=this.valueAxes[j];var i=n.stackType,h=0;if("100%"==i||"regular"==i){for(p=!1,o=0;o<r;o++){q=this.graphs[o],q.tcc=1,q.valueAxis==n&&"column"==q.type&&(!p&&q.stackable&&(t++,p=!0),(!q.stackable&&q.clustered||q.newStack&&0!==h)&&t++,q.columnIndex=t-1,q.clustered||(q.columnIndex=0),h++)}}if("none"==i||"3d"==i){h=!1;for(o=0;o<r;o++){q=this.graphs[o],q.valueAxis==n&&"column"==q.type&&(q.clustered?(q.tcc=1,q.newStack&&(t=0),q.hidden||(q.columnIndex=t,t++)):q.hidden||(h=!0,q.tcc=1,q.columnIndex=0))}h&&0===t&&(t=1)}if("3d"==i){n=1;for(h=0;h<r;h++){q=this.graphs[h],q.newStack&&n++,q.depthCount=n,q.tcc=t}t=n}}return t},parseData:function(){a.AmSerialChart.base.parseData.call(this);this.parseSerialData(this.dataProvider)},getCategoryIndexByValue:function(e){var d=this.chartData,f;for(f=0;f<d.length;f++){if(d[f].category==e){return f}}},handleScrollbarZoom:function(b){this.zoomedByScrollbar=!0;this.zoom(b.start,b.end)},dispatchTimeZoomEvent:function(){if(this.drawGraphs&&(this.prevStartTime!=this.startTime||this.prevEndTime!=this.endTime)){var e={type:"zoomed"};e.startDate=new Date(this.startTime);e.endDate=new Date(this.endTime);e.startIndex=this.start;e.endIndex=this.end;this.startIndex=this.start;this.endIndex=this.end;this.startDate=e.startDate;this.endDate=e.endDate;this.prevStartTime=this.startTime;this.prevEndTime=this.endTime;var d=this.categoryAxis,f=a.extractPeriod(d.minPeriod).period,d=d.dateFormatsObject[f];e.startValue=a.formatDate(e.startDate,d,this);e.endValue=a.formatDate(e.endDate,d,this);e.chart=this;e.target=this;this.fire(e)}},dispatchIndexZoomEvent:function(){if(this.drawGraphs&&(this.prevStartIndex!=this.start||this.prevEndIndex!=this.end)){this.startIndex=this.start;this.endIndex=this.end;var d=this.chartData;if(a.ifArray(d)&&!isNaN(this.start)&&!isNaN(this.end)){var c={chart:this,target:this,type:"zoomed"};c.startIndex=this.start;c.endIndex=this.end;c.startValue=d[this.start].category;c.endValue=d[this.end].category;this.categoryAxis.parseDates&&(this.startTime=d[this.start].time,this.endTime=d[this.end].time,c.startDate=new Date(this.startTime),c.endDate=new Date(this.endTime));this.prevStartIndex=this.start;this.prevEndIndex=this.end;this.fire(c)}}},updateLegendValues:function(){this.legend&&this.legend.updateValues()},getClosestIndex:function(i,h,p,o,l,n){0>l&&(l=0);n>i.length-1&&(n=i.length-1);var m=l+Math.round((n-l)/2),j=i[m][h];return p==j?m:1>=n-l?o?l:Math.abs(i[l][h]-p)<Math.abs(i[n][h]-p)?l:n:p==j?m:p<j?this.getClosestIndex(i,h,p,o,l,m):this.getClosestIndex(i,h,p,o,m,n)},zoomToIndexes:function(f,e){var h=this.chartData;if(h){var g=h.length;0<g&&(0>f&&(f=0),e>g-1&&(e=g-1),g=this.categoryAxis,g.parseDates&&!g.equalSpacing?this.zoom(h[f].time,this.getEndTime(h[e].time)):this.zoom(f,e))}},zoomToDates:function(f,e){var h=this.chartData;if(h){if(this.categoryAxis.equalSpacing){var g=this.getClosestIndex(h,"time",f.getTime(),!0,0,h.length);e=a.resetDateToMin(e,this.categoryAxis.minPeriod,1);h=this.getClosestIndex(h,"time",e.getTime(),!1,0,h.length);this.zoom(g,h)}else{this.zoom(f.getTime(),e.getTime())}}},zoomToCategoryValues:function(d,c){this.chartData&&this.zoom(this.getCategoryIndexByValue(d),this.getCategoryIndexByValue(c))},formatPeriodString:function(ab,aa){if(aa){aa.periodDataItem={};aa.periodPercentDataItem={};var Z=["value","open","low","high","close"],Y="value open low high close average sum count".split(" "),W=aa.valueAxis,V=this.chartData,X=aa.numberFormatter;X||(X=this.nf);for(var U=0;U<Z.length;U++){for(var T=Z[U],S=0,Q=0,R=0,H=0,s,j,I,J,M,O,N,o,i,L,G=this.start;G<=this.end;G++){var K=V[G];if(K){var P=K.axes[W.id].graphs[aa.id];if(P){if(P.values){var e=P.values[T],K=K.x.categoryAxis;if(this.rotate){if(0>K||K>P.graph.height){e=NaN}}else{if(0>K||K>P.graph.width){e=NaN}}if(!isNaN(e)){isNaN(s)&&(s=e);j=e;if(isNaN(I)||I>e){I=e}if(isNaN(J)||J<e){J=e}M=a.getDecimals(S);K=a.getDecimals(e);S+=e;S=a.roundTo(S,Math.max(M,K));Q++;M=S/Q}}if(P.percents&&(P=P.percents[T],!isNaN(P))){isNaN(O)&&(O=P);N=P;if(isNaN(o)||o>P){o=P}if(isNaN(i)||i<P){i=P}L=a.getDecimals(R);e=a.getDecimals(P);R+=P;R=a.roundTo(R,Math.max(L,e));H++;L=R/H}}}}S={open:s,close:j,high:J,low:I,average:M,sum:S,count:Q};R={open:O,close:N,high:i,low:o,average:L,sum:R,count:H};ab=a.formatValue(ab,S,Y,X,T+"\\.",this.usePrefixes,this.prefixesOfSmallNumbers,this.prefixesOfBigNumbers);ab=a.formatValue(ab,R,Y,this.pf,"percents\\."+T+"\\.");aa.periodDataItem[T]=S;aa.periodPercentDataItem[T]=R}}return ab=a.cleanFromEmpty(ab)},formatString:function(i,e,n){if(e){var m=e.graph;if(void 0!==i){if(-1!=i.indexOf("[[category]]")){var k=e.serialDataItem.category;if(this.categoryAxis.parseDates){var j=this.balloonDateFormat,l=this.chartCursor;l&&l.categoryBalloonDateFormat&&(j=l.categoryBalloonDateFormat);j=a.formatDate(k,j,this);-1!=j.indexOf("fff")&&(j=a.formatMilliseconds(j,k));k=j}i=i.replace(/\[\[category\]\]/g,String(k.replace("$","$$$")))}k=m.numberFormatter;k||(k=this.nf);j=e.graph.valueAxis;(l=j.duration)&&!isNaN(e.values.value)&&(l=a.formatDuration(e.values.value,l,"",j.durationUnits,j.maxInterval,k),i=i.replace(RegExp("\\[\\[value\\]\\]","g"),l));"date"==j.type&&(j=a.formatDate(new Date(e.values.value),m.dateFormat,this),l=RegExp("\\[\\[value\\]\\]","g"),i=i.replace(l,j),j=a.formatDate(new Date(e.values.open),m.dateFormat,this),l=RegExp("\\[\\[open\\]\\]","g"),i=i.replace(l,j));m="value open low high close total".split(" ");j=this.pf;i=a.formatValue(i,e.percents,m,j,"percents\\.");i=a.formatValue(i,e.values,m,k,"",this.usePrefixes,this.prefixesOfSmallNumbers,this.prefixesOfBigNumbers);i=a.formatValue(i,e.values,["percents"],j);-1!=i.indexOf("[[")&&(i=a.formatDataContextValue(i,e.dataContext));-1!=i.indexOf("[[")&&e.graph.customData&&(i=a.formatDataContextValue(i,e.graph.customData));i=a.AmSerialChart.base.formatString.call(this,i,e,n)}return i}},updateChartCursor:function(){a.AmSerialChart.base.updateChartCursor.call(this);var i=this.chartCursor,e=this.categoryAxis;if(i){var p=i.categoryBalloonAlpha,o=i.categoryBalloonColor,m=i.color;void 0===o&&(o=i.cursorColor);var l=i.valueZoomable,n=i.zoomable,j=i.valueLineEnabled;this.rotate?(i.vLineEnabled=j,i.hZoomEnabled=l,i.vZoomEnabled=n):(i.hLineEnabled=j,i.vZoomEnabled=l,i.hZoomEnabled=n);if(i.valueLineBalloonEnabled){for(j=0;j<this.valueAxes.length;j++){l=this.valueAxes[j],(n=l.balloon)||(n={}),n=a.extend(n,this.balloon,!0),n.fillColor=o,n.balloonColor=o,n.fillAlpha=p,n.borderColor=o,n.color=m,l.balloon=n}}else{for(n=0;n<this.valueAxes.length;n++){l=this.valueAxes[n],l.balloon&&(l.balloon=null)}}e&&(e.balloonTextFunction=i.categoryBalloonFunction,i.categoryLineAxis=e,e.balloonText=i.categoryBalloonText,i.categoryBalloonEnabled&&((n=e.balloon)||(n={}),n=a.extend(n,this.balloon,!0),n.fillColor=o,n.balloonColor=o,n.fillAlpha=p,n.borderColor=o,n.color=m,e.balloon=n),e.balloon&&(e.balloon.enabled=i.categoryBalloonEnabled))}},addChartScrollbar:function(b){a.callMethod("destroy",[this.chartScrollbar]);b&&(b.chart=this,this.listenTo(b,"zoomed",this.handleScrollbarZoom));this.rotate?void 0===b.width&&(b.width=b.scrollbarHeight):void 0===b.height&&(b.height=b.scrollbarHeight);b.gridAxis=this.categoryAxis;this.chartScrollbar=b},addValueScrollbar:function(d){a.callMethod("destroy",[this.valueScrollbar]);d&&(d.chart=this,this.listenTo(d,"zoomed",this.handleScrollbarValueZoom),this.listenTo(d,"zoomStarted",this.handleCursorZoomStarted));var c=d.scrollbarHeight;this.rotate?void 0===d.height&&(d.height=c):void 0===d.width&&(d.width=c);d.gridAxis||(d.gridAxis=this.valueAxes[0]);d.valueAxes=this.valueAxes;this.valueScrollbar=d},removeChartScrollbar:function(){a.callMethod("destroy",[this.chartScrollbar]);this.chartScrollbar=null},removeValueScrollbar:function(){a.callMethod("destroy",[this.valueScrollbar]);this.valueScrollbar=null},handleReleaseOutside:function(b){a.AmSerialChart.base.handleReleaseOutside.call(this,b);a.callMethod("handleReleaseOutside",[this.chartScrollbar,this.valueScrollbar])},update:function(){a.AmSerialChart.base.update.call(this);this.chartScrollbar&&this.chartScrollbar.update&&this.chartScrollbar.update();this.valueScrollbar&&this.valueScrollbar.update&&this.valueScrollbar.update()},processScrollbars:function(){a.AmSerialChart.base.processScrollbars.call(this);var b=this.valueScrollbar;b&&(b=a.processObject(b,a.ChartScrollbar,this.theme),b.id="valueScrollbar",this.addValueScrollbar(b))},handleValueAxisZoom:function(b){this.handleValueAxisZoomReal(b,this.valueAxes)},zoomOut:function(){a.AmSerialChart.base.zoomOut.call(this);this.zoom();this.syncGrid()},getNextItem:function(f){var e=f.index,h=this.chartData,g=f.graph;if(e+1<h.length){for(e+=1;e<h.length;e++){if(f=h[e]){if(f=f.axes[g.valueAxis.id].graphs[g.id],!isNaN(f.y)){return f}}}}},handleCursorZoomReal:function(i,g,p,o,n){var l=n.target,m,j;this.rotate?(isNaN(i)||isNaN(g)||this.relativeZoomValueAxes(this.valueAxes,i,g)&&this.updateAfterValueZoom(),l.vZoomEnabled&&(m=n.start,j=n.end)):(isNaN(p)||isNaN(o)||this.relativeZoomValueAxes(this.valueAxes,p,o)&&this.updateAfterValueZoom(),l.hZoomEnabled&&(m=n.start,j=n.end));isNaN(m)||isNaN(j)||(i=this.categoryAxis,i.parseDates&&!i.equalSpacing?this.zoomToDates(new Date(m),new Date(j)):this.zoomToIndexes(m,j))},handleCursorZoomStarted:function(){var e=this.valueAxes;if(e){var e=e[0],d=e.relativeStart,f=e.relativeEnd;e.reversed&&(d=1-e.relativeEnd,f=1-e.relativeStart);this.rotate?(this.startX0=d,this.endX0=f):(this.startY0=d,this.endY0=f)}this.categoryAxis&&(this.start0=this.start,this.end0=this.end,this.startTime0=this.startTime,this.endTime0=this.endTime)},fixCursor:function(){this.chartCursor&&this.chartCursor.fixPosition();this.prevCursorItem=null},handleCursorMove:function(x){a.AmSerialChart.base.handleCursorMove.call(this,x);var w=x.target,v=this.categoryAxis;if(x.panning){this.handleCursorHide(x)}else{if(this.chartData&&!w.isHidden){var u=this.graphs;if(u){var s;s=v.xToIndex(this.rotate?x.y:x.x);if(s=this.chartData[s]){var r,t,q,o;if(w.oneBalloonOnly&&w.valueBalloonsEnabled){var j=Infinity;for(r=u.length-1;0<=r;r--){if(t=u[r],t.balloon.enabled&&t.showBalloon&&!t.hidden){q=t.valueAxis.id;q=s.axes[q].graphs[t.id];if(w.showNextAvailable&&isNaN(q.y)&&(q=this.getNextItem(q),!q)){continue}q=q.y;"top"==t.showBalloonAt&&(q=0);"bottom"==t.showBalloonAt&&(q=this.height);var e=w.mouseX,i=w.mouseY;q=this.rotate?Math.abs(e-q):Math.abs(i-q);q<j&&(j=q,o=t)}}w.mostCloseGraph=o}if(this.prevCursorItem!=s||o!=this.prevMostCloseGraph){j=[];for(r=0;r<u.length;r++){t=u[r];q=t.valueAxis.id;q=s.axes[q].graphs[t.id];if(w.showNextAvailable&&isNaN(q.y)&&(q=this.getNextItem(q),!q&&t.balloon)){t.balloon.hide();continue}o&&t!=o?(t.showGraphBalloon(q,w.pointer,!1,w.graphBulletSize,w.graphBulletAlpha),t.balloon.hide(0)):w.valueBalloonsEnabled?(t.balloon.showBullet=w.bulletsEnabled,t.balloon.bulletSize=w.bulletSize/2,x.hideBalloons||(t.showGraphBalloon(q,w.pointer,!1,w.graphBulletSize,w.graphBulletAlpha),t.balloon.set&&j.push({balloon:t.balloon,y:t.balloon.pointToY}))):(t.currentDataItem=q,t.resizeBullet(q,w.graphBulletSize,w.graphBulletAlpha))}w.avoidBalloonOverlapping&&this.arrangeBalloons(j);this.prevCursorItem=s}this.prevMostCloseGraph=o}}v.showBalloon(x.x,x.y,w.categoryBalloonDateFormat,x.skip);this.updateLegendValues()}}},handleCursorHide:function(d){a.AmSerialChart.base.handleCursorHide.call(this,d);d=this.categoryAxis;this.prevCursorItem=null;this.updateLegendValues();d&&d.hideBalloon();d=this.graphs;var c;for(c=0;c<d.length;c++){d[c].currentDataItem=null}},handleCursorPanning:function(x){var w=x.target,v,u=x.deltaX,s=x.deltaY,r=x.delta2X,t=x.delta2Y;x=!1;if(this.rotate){isNaN(r)&&(r=u,x=!0);var q=this.endX0;v=this.startX0;var o=q-v,q=q-o*r,j=o;x||(j=0);x=a.fitToBounds(v-o*u,0,1-j)}else{isNaN(t)&&(t=s,x=!0),q=this.endY0,v=this.startY0,o=q-v,q+=o*s,j=o,x||(j=0),x=a.fitToBounds(v+o*t,0,1-j)}v=a.fitToBounds(q,j,1);var e;w.valueZoomable&&(e=this.relativeZoomValueAxes(this.valueAxes,x,v));var i;v=this.categoryAxis;this.rotate&&(u=s,r=t);x=!1;isNaN(r)&&(r=u,x=!0);if(w.zoomable&&(0<Math.abs(u)||0<Math.abs(r))){if(v.parseDates&&!v.equalSpacing){if(t=this.startTime0,s=this.endTime0,v=s-t,r*=v,o=this.firstTime,q=this.lastTime,j=v,x||(j=0),x=Math.round(a.fitToBounds(t-v*u,o,q-j)),r=Math.round(a.fitToBounds(s-r,o+j,q)),this.startTime!=x||this.endTime!=r){i={chart:this,target:w,type:"zoomed",start:x,end:r},this.skipZoomed=!0,w.fire(i),this.zoom(x,r),i=!0}}else{if(t=this.start0,s=this.end0,v=s-t,u=Math.round(v*u),r=Math.round(v*r),o=this.chartData.length-1,x||(v=0),x=a.fitToBounds(t-u,0,o-v),v=a.fitToBounds(s-r,v,o),this.start!=x||this.end!=v){this.skipZoomed=!0,w.fire({chart:this,target:w,type:"zoomed",start:x,end:v}),this.zoom(x,v),i=!0}}}!i&&e&&this.updateAfterValueZoom()},arrangeBalloons:function(i){var g=this.plotAreaHeight;i.sort(this.compareY);var p,o,n,l=this.plotAreaWidth,m=i.length;for(p=0;p<m;p++){o=i[p].balloon,o.setBounds(0,0,l,g),o.restorePrevious(),o.draw(),g=o.yPos-3}i.reverse();for(p=0;p<m;p++){o=i[p].balloon;var g=o.bottom,j=o.bottom-o.yPos;0<p&&g-j<n+3&&o.setBounds&&(o.setBounds(0,n+3,l,n+j+3),o.restorePrevious(),o.draw());o.set&&o.set.show();n=o.bottom}},compareY:function(d,c){return d.y<c.y?1:-1}})})();(function(){var a=window.AmCharts;a.Cuboid=a.Class({construct:function(C,A,z,y,w,r,s,q,o,j,g,i,F,D,B,H,G){this.set=C.set();this.container=C;this.h=Math.round(z);this.w=Math.round(A);this.dx=y;this.dy=w;this.colors=r;this.alpha=s;this.bwidth=q;this.bcolor=o;this.balpha=j;this.dashLength=D;this.topRadius=H;this.pattern=B;this.rotate=F;this.bcn=G;F?0>A&&0===g&&(g=180):0>z&&270==g&&(g=90);this.gradientRotation=g;0===y&&0===w&&(this.cornerRadius=i);this.draw()},draw:function(){var ay=this.set;ay.clear();var ax=this.container,aw=ax.chart,av=this.w,at=this.h,ar=this.dx,au=this.dy,aq=this.colors,ap=this.alpha,ao=this.bwidth,am=this.bcolor,an=this.balpha,ai=this.gradientRotation,ah=this.cornerRadius,af=this.dashLength,Y=this.pattern,aj=this.topRadius,ak=this.bcn,ab=aq,al=aq;"object"==typeof aq&&(ab=aq[0],al=aq[aq.length-1]);var ag,ae,aa,X,Z,ac,ad,R,s,e=ap;Y&&(ap=0);var W,V,U,T,S=this.rotate;if(0<Math.abs(ar)||0<Math.abs(au)){if(isNaN(aj)){ad=al,al=a.adjustLuminosity(ab,-0.2),al=a.adjustLuminosity(ab,-0.2),ag=a.polygon(ax,[0,ar,av+ar,av,0],[0,au,au,0,0],al,ap,1,am,0,ai),0<an&&(s=a.line(ax,[0,ar,av+ar],[0,au,au],am,an,ao,af)),ae=a.polygon(ax,[0,0,av,av,0],[0,at,at,0,0],al,ap,1,am,0,ai),ae.translate(ar,au),0<an&&(aa=a.line(ax,[ar,ar],[au,au+at],am,an,ao,af)),X=a.polygon(ax,[0,0,ar,ar,0],[0,at,at+au,au,0],al,ap,1,am,0,ai),Z=a.polygon(ax,[av,av,av+ar,av+ar,av],[0,at,at+au,au,0],al,ap,1,am,0,ai),0<an&&(ac=a.line(ax,[av,av+ar,av+ar,av],[0,au,at+au,at],am,an,ao,af)),al=a.adjustLuminosity(ad,0.2),ad=a.polygon(ax,[0,ar,av+ar,av,0],[at,at+au,at+au,at,at],al,ap,1,am,0,ai),0<an&&(R=a.line(ax,[0,ar,av+ar],[at,at+au,at+au],am,an,ao,af))}else{var o,j,i;S?(o=at/2,al=ar/2,i=at/2,j=av+ar/2,V=Math.abs(at/2),W=Math.abs(ar/2)):(al=av/2,o=au/2,j=av/2,i=at+au/2+1,W=Math.abs(av/2),V=Math.abs(au/2));U=W*aj;T=V*aj;0.1<W&&0.1<W&&(ag=a.circle(ax,W,ab,ap,ao,am,an,!1,V),ag.translate(al,o));0.1<U&&0.1<U&&(ad=a.circle(ax,U,a.adjustLuminosity(ab,0.5),ap,ao,am,an,!1,T),ad.translate(j,i))}}ap=e;1>Math.abs(at)&&(at=0);1>Math.abs(av)&&(av=0);!isNaN(aj)&&(0<Math.abs(ar)||0<Math.abs(au))?(aq=[ab],aq={fill:aq,stroke:am,"stroke-width":ao,"stroke-opacity":an,"fill-opacity":ap},S?(ap="M0,0 L"+av+","+(at/2-at/2*aj),ao=" B",0<av&&(ao=" A"),a.VML?(ap+=ao+Math.round(av-U)+","+Math.round(at/2-T)+","+Math.round(av+U)+","+Math.round(at/2+T)+","+av+",0,"+av+","+at,ap=ap+(" L0,"+at)+(ao+Math.round(-W)+","+Math.round(at/2-V)+","+Math.round(W)+","+Math.round(at/2+V)+",0,"+at+",0,0")):(ap+="A"+U+","+T+",0,0,0,"+av+","+(at-at/2*(1-aj))+"L0,"+at,ap+="A"+W+","+V+",0,0,1,0,0"),W=90):(ao=av/2-av/2*aj,ap="M0,0 L"+ao+","+at,a.VML?(ap="M0,0 L"+ao+","+at,ao=" B",0>at&&(ao=" A"),ap+=ao+Math.round(av/2-U)+","+Math.round(at-T)+","+Math.round(av/2+U)+","+Math.round(at+T)+",0,"+at+","+av+","+at,ap+=" L"+av+",0",ap+=ao+Math.round(av/2+W)+","+Math.round(V)+","+Math.round(av/2-W)+","+Math.round(-V)+","+av+",0,0,0"):(ap+="A"+U+","+T+",0,0,0,"+(av-av/2*(1-aj))+","+at+"L"+av+",0",ap+="A"+W+","+V+",0,0,1,0,0"),W=180),ax=ax.path(ap).attr(aq),ax.gradient("linearMega",[ab,a.adjustLuminosity(ab,-0.3),a.adjustLuminosity(ab,-0.3),ab],W),S?ax.translate(ar/2,0):ax.translate(0,au/2)):ax=0===at?a.line(ax,[0,av],[0,0],am,an,ao,af):0===av?a.line(ax,[0,0],[0,at],am,an,ao,af):0<ah?a.rect(ax,av,at,aq,ap,ao,am,an,ah,ai,af):a.polygon(ax,[0,0,av,av,0],[0,at,at,0,0],aq,ap,ao,am,an,ai,!1,af);av=isNaN(aj)?0>at?[ag,s,ae,aa,X,Z,ac,ad,R,ax]:[ad,R,ae,aa,X,Z,ag,s,ac,ax]:S?0<av?[ag,ax,ad]:[ad,ax,ag]:0>at?[ag,ax,ad]:[ad,ax,ag];a.setCN(aw,ax,ak+"front");a.setCN(aw,ae,ak+"back");a.setCN(aw,ad,ak+"top");a.setCN(aw,ag,ak+"bottom");a.setCN(aw,X,ak+"left");a.setCN(aw,Z,ak+"right");for(ag=0;ag<av.length;ag++){if(ae=av[ag]){ay.push(ae),a.setCN(aw,ae,ak+"element")}}Y&&ax.pattern(Y,NaN,aw.path)},width:function(b){isNaN(b)&&(b=0);this.w=Math.round(b);this.draw()},height:function(b){isNaN(b)&&(b=0);this.h=Math.round(b);this.draw()},animateHeight:function(e,d){var f=this;f.animationFinished=!1;f.easing=d;f.totalFrames=e*a.updateRate;f.rh=f.h;f.frame=0;f.height(1);setTimeout(function(){f.updateHeight.call(f)},1000/a.updateRate)},updateHeight:function(){var d=this;d.frame++;var c=d.totalFrames;d.frame<=c?(c=d.easing(0,d.frame,1,d.rh-1,c),d.height(c),window.requestAnimationFrame?window.requestAnimationFrame(function(){d.updateHeight.call(d)}):setTimeout(function(){d.updateHeight.call(d)},1000/a.updateRate)):(d.height(d.rh),d.animationFinished=!0)},animateWidth:function(e,d){var f=this;f.animationFinished=!1;f.easing=d;f.totalFrames=e*a.updateRate;f.rw=f.w;f.frame=0;f.width(1);setTimeout(function(){f.updateWidth.call(f)},1000/a.updateRate)},updateWidth:function(){var d=this;d.frame++;var c=d.totalFrames;d.frame<=c?(c=d.easing(0,d.frame,1,d.rw-1,c),d.width(c),window.requestAnimationFrame?window.requestAnimationFrame(function(){d.updateWidth.call(d)}):setTimeout(function(){d.updateWidth.call(d)},1000/a.updateRate)):(d.width(d.rw),d.animationFinished=!0)}})})();(function(){var a=window.AmCharts;a.CategoryAxis=a.Class({inherits:a.AxisBase,construct:function(b){this.cname="CategoryAxis";a.CategoryAxis.base.construct.call(this,b);this.minPeriod="DD";this.equalSpacing=this.parseDates=!1;this.position="bottom";this.startOnAxis=!1;this.gridPosition="middle";this.safeDistance=30;this.stickBalloonToCategory=!1;a.applyTheme(this,b,this.cname)},draw:function(){a.CategoryAxis.base.draw.call(this);this.generateDFObject();var Z=this.chart.chartData;this.data=Z;this.labelRotationR=this.labelRotation;this.type=null;if(a.ifArray(Z)){var Y,X=this.chart;"scrollbar"!=this.id?(a.setCN(X,this.set,"category-axis"),a.setCN(X,this.labelsSet,"category-axis"),a.setCN(X,this.axisLine.axisSet,"category-axis")):this.bcn=this.id+"-";var W=this.start,U=this.labelFrequency,T=0,V=this.end-W+1,S=this.gridCountR,R=this.showFirstLabel,Q=this.showLastLabel,O,P="",P=a.extractPeriod(this.minPeriod),z=a.getPeriodDuration(P.period,P.count),o,i,G,H,K,M=this.rotate,L=this.firstDayOfWeek,j=this.boldPeriodBeginning;Y=a.resetDateToMin(new Date(Z[Z.length-1].time******z),this.minPeriod,1,L).getTime();this.firstTime=X.firstTime;this.endTime>Y&&(this.endTime=Y);K=this.minorGridEnabled;i=this.gridAlpha;var e=0,J=0;if(this.widthField){for(Y=this.start;Y<=this.end;Y++){if(H=this.data[Y]){var s=Number(this.data[Y].dataContext[this.widthField]);isNaN(s)||(e+=s,H.widthValue=s)}}}if(this.parseDates&&!this.equalSpacing){this.lastTime=Z[Z.length-1].time,this.maxTime=a.resetDateToMin(new Date(this.lastTime******z),this.minPeriod,1,L).getTime(),this.timeDifference=this.endTime-this.startTime,this.parseDatesDraw()}else{if(!this.parseDates){if(this.cellWidth=this.getStepWidth(V),V<S&&(S=V),T+=this.start,this.stepWidth=this.getStepWidth(V),0<S){for(L=Math.floor(V/S),H=this.chooseMinorFrequency(L),V=T,V/2==Math.round(V/2)&&V--,0>V&&(V=0),j=0,this.widthField&&(V=this.start,L=1),this.end-V+1>=this.autoRotateCount&&(this.labelRotationR=this.autoRotateAngle),Y=V;Y<=this.end+2;Y++){S=!1;0<=Y&&Y<this.data.length?(o=this.data[Y],P=o.category,S=o.forceShow):P="";if(K&&!isNaN(H)){if(Y/H==Math.round(Y/H)||S){Y/L==Math.round(Y/L)||S||(this.gridAlpha=this.minorGridAlpha,P=void 0)}else{continue}}else{if(Y/L!=Math.round(Y/L)&&!S){continue}}V=this.getCoordinate(Y-T);S=0;"start"==this.gridPosition&&(V-=this.cellWidth/2,S=this.cellWidth/2);O=!0;G=S;"start"==this.tickPosition&&(G=0,O=!1,S=0);if(Y==W&&!R||Y==this.end&&!Q){P=void 0}Math.round(j/U)!=j/U&&(P=void 0);j++;Z=this.cellWidth;M&&(Z=NaN,this.ignoreAxisWidth||!X.autoMargins)&&(Z="right"==this.position?X.marginRight-this.titleWidth:X.marginLeft-this.titleWidth,Z-=this.tickLength+10);this.labelFunction&&o&&(P=this.labelFunction(P,o,this));P=a.fixBrakes(P);z=!1;this.boldLabels&&(z=!0);Y>this.end&&"start"==this.tickPosition&&(P=" ");this.rotate&&this.inside&&(S-=2);isNaN(o.widthValue)||(o.percentWidthValue=o.widthValue/e*100,Z=this.rotate?this.height*o.widthValue/e:this.width*o.widthValue/e,V=J,J+=Z,G=S=Z/2);O=new this.axisItemRenderer(this,V,P,O,Z,S,void 0,z,G,!1,o.labelColor,o.className);O.serialDataItem=o;this.pushAxisItem(O);this.gridAlpha=i}}}else{if(this.parseDates&&this.equalSpacing){T=this.start;this.startTime=this.data[this.start].time;this.endTime=this.data[this.end].time;this.timeDifference=this.endTime-this.startTime;Y=this.choosePeriod(0);U=Y.period;o=Y.count;Y=a.getPeriodDuration(U,o);Y<z&&(U=P.period,o=P.count,Y=z);i=U;"WW"==i&&(i="DD");this.currentDateFormat=this.dateFormatsObject[i];this.stepWidth=this.getStepWidth(V);S=Math.ceil(this.timeDifference/Y)+1;P=a.resetDateToMin(new Date(this.startTime-Y),U,o,L).getTime();this.cellWidth=this.getStepWidth(V);V=Math.round(P/Y);W=-1;V/2==Math.round(V/2)&&(W=-2,P-=Y);V=this.start;V/2==Math.round(V/2)&&V--;0>V&&(V=0);J=this.end+2;J>=this.data.length&&(J=this.data.length);Z=!1;Z=!R;this.previousPos=-1000;20<this.labelRotationR&&(this.safeDistance=5);s=V;if(this.data[V].time!=a.resetDateToMin(new Date(this.data[V].time),U,o,L).getTime()){var z=0,I=P;for(Y=V;Y<J;Y++){H=this.data[Y].time,this.checkPeriodChange(U,o,H,I)&&(z++,2<=z&&(s=Y,Y=J),I=H)}}K&&1<o&&(H=this.chooseMinorFrequency(o),a.getPeriodDuration(U,H));if(0<this.gridCountR){for(Y=V;Y<J;Y++){if(H=this.data[Y].time,this.checkPeriodChange(U,o,H,P)&&Y>=s){V=this.getCoordinate(Y-this.start);K=!1;this.nextPeriod[i]&&(K=this.checkPeriodChange(this.nextPeriod[i],1,H,P,i))&&a.resetDateToMin(new Date(H),this.nextPeriod[i],1,L).getTime()!=H&&(K=!1);z=!1;K&&this.markPeriodChange?(K=this.dateFormatsObject[this.nextPeriod[i]],z=!0):K=this.dateFormatsObject[i];P=a.formatDate(new Date(H),K,X);if(Y==W&&!R||Y==S&&!Q){P=" "}Z?Z=!1:(j||(z=!1),V-this.previousPos>this.safeDistance*Math.cos(this.labelRotationR*Math.PI/180)&&(this.labelFunction&&(P=this.labelFunction(P,new Date(H),this,U,o,G)),this.boldLabels&&(z=!0),O=new this.axisItemRenderer(this,V,P,void 0,void 0,void 0,void 0,z),K=O.graphics(),this.pushAxisItem(O),K=K.getBBox().width,a.isModern||(K-=V),this.previousPos=V+K));G=P=H}}}}}}for(Y=R=0;Y<this.data.length;Y++){if(H=this.data[Y]){this.parseDates&&!this.equalSpacing?(Q=H.time,W=this.cellWidth,"MM"==this.minPeriod&&(W=86400000*a.daysInMonth(new Date(Q))*this.stepWidth,H.cellWidth=W),Q=Math.round((Q-this.startTime)*this.stepWidth+W/2)):Q=this.getCoordinate(Y-T),H.x[this.id]=Q}}if(this.widthField){for(Y=this.start;Y<=this.end;Y++){H=this.data[Y],W=H.widthValue,H.percentWidthValue=W/e*100,this.rotate?(Q=this.height*W/e/2+R,R=this.height*W/e+R):(Q=this.width*W/e/2+R,R=this.width*W/e+R),H.x[this.id]=Q}}e=this.guides.length;for(Y=0;Y<e;Y++){if(R=this.guides[Y],L=L=L=K=W=NaN,Q=R.above,R.toCategory&&(L=X.getCategoryIndexByValue(R.toCategory),isNaN(L)||(W=this.getCoordinate(L-T),R.expand&&(W+=this.cellWidth/2),O=new this.axisItemRenderer(this,W,"",!0,NaN,NaN,R),this.pushAxisItem(O,Q))),R.category&&(L=X.getCategoryIndexByValue(R.category),isNaN(L)||(K=this.getCoordinate(L-T),R.expand&&(K-=this.cellWidth/2),L=(W-K)/2,O=new this.axisItemRenderer(this,K,R.label,!0,NaN,L,R),this.pushAxisItem(O,Q))),j=X.dataDateFormat,R.toDate&&(!j||R.toDate instanceof Date||(R.toDate=R.toDate.toString()+" |"),R.toDate=a.getDate(R.toDate,j),this.equalSpacing?(L=X.getClosestIndex(this.data,"time",R.toDate.getTime(),!1,0,this.data.length-1),isNaN(L)||(W=this.getCoordinate(L-T))):W=(R.toDate.getTime()-this.startTime)*this.stepWidth,O=new this.axisItemRenderer(this,W,"",!0,NaN,NaN,R),this.pushAxisItem(O,Q)),R.date&&(!j||R.date instanceof Date||(R.date=R.date.toString()+" |"),R.date=a.getDate(R.date,j),this.equalSpacing?(L=X.getClosestIndex(this.data,"time",R.date.getTime(),!1,0,this.data.length-1),isNaN(L)||(K=this.getCoordinate(L-T))):K=(R.date.getTime()-this.startTime)*this.stepWidth,L=(W-K)/2,O=!0,R.toDate&&(O=!1),O="H"==this.orientation?new this.axisItemRenderer(this,K,R.label,O,2*L,NaN,R):new this.axisItemRenderer(this,K,R.label,!1,NaN,L,R),this.pushAxisItem(O,Q)),O&&(L=O.label)&&this.addEventListeners(L,R),0<W||0<K){L=!1;if(this.rotate){if(W<this.height||K<this.height){L=!0}}else{if(W<this.width||K<this.width){L=!0}}L&&(W=new this.guideFillRenderer(this,K,W,R),K=W.graphics(),this.pushAxisItem(W,Q),R.graphics=K,K.index=Y,this.addEventListeners(K,R))}}if(X=X.chartCursor){M?X.fixHeight(this.cellWidth):(X.fixWidth(this.cellWidth),X.fullWidth&&this.balloon&&(this.balloon.minWidth=this.cellWidth))}this.previousHeight=N}this.axisCreated=!0;this.set.translate(this.x,this.y);this.labelsSet.translate(this.x,this.y);this.labelsSet.show();this.positionTitle();(M=this.axisLine.set)&&M.toFront();var N=this.getBBox().height;2<N-this.previousHeight&&this.autoWrap&&!this.parseDates&&(this.axisCreated=this.chart.marginsUpdated=!1)},xToIndex:function(i){var e=this.data,p=this.chart,o=p.rotate,m=this.stepWidth,l;if(this.parseDates&&!this.equalSpacing){i=this.startTime+Math.round(i/m)-this.minDuration()/2,l=p.getClosestIndex(e,"time",i,!1,this.start,this.end+1)}else{if(this.widthField){for(p=Infinity,m=this.start;m<=this.end;m++){var n=this.data[m];n&&(n=Math.abs(n.x[this.id]-i),n<p&&(p=n,l=m))}}else{this.startOnAxis||(i-=m/2),l=this.start+Math.round(i/m)}}l=a.fitToBounds(l,0,e.length-1);var j;e[l]&&(j=e[l].x[this.id]);o?j>this.height+1&&l--:j>this.width+1&&l--;0>j&&l++;return l=a.fitToBounds(l,0,e.length-1)},dateToCoordinate:function(b){return this.parseDates&&!this.equalSpacing?(b.getTime()-this.startTime)*this.stepWidth:this.parseDates&&this.equalSpacing?(b=this.chart.getClosestIndex(this.data,"time",b.getTime(),!1,0,this.data.length-1),this.getCoordinate(b-this.start)):NaN},categoryToCoordinate:function(b){if(this.chart){if(this.parseDates){return this.dateToCoordinate(new Date(b))}b=this.chart.getCategoryIndexByValue(b);if(!isNaN(b)){return this.getCoordinate(b-this.start)}}else{return NaN}},coordinateToDate:function(b){return this.equalSpacing?(b=this.xToIndex(b),new Date(this.data[b].time)):new Date(this.startTime+b/this.stepWidth)},coordinateToValue:function(b){b=this.xToIndex(b);if(b=this.data[b]){return this.parseDates?b.time:b.category}},getCoordinate:function(b){b*=this.stepWidth;this.startOnAxis||(b+=this.stepWidth/2);return Math.round(b)},formatValue:function(d,c){c||(c=this.currentDateFormat);this.parseDates&&(d=a.formatDate(new Date(d),c,this.chart));return d},showBalloonAt:function(d,c){void 0===c&&(c=this.parseDates?this.dateToCoordinate(new Date(d)):this.categoryToCoordinate(d));return this.adjustBalloonCoordinate(c)},formatBalloonText:function(i,e,p){var o="",m="",l=this.chart,n=this.data[e];if(n){if(this.parseDates){o=a.formatDate(n.category,p,l),e=a.changeDate(new Date(n.category),this.minPeriod,1),m=a.formatDate(e,p,l),-1!=o.indexOf("fff")&&(o=a.formatMilliseconds(o,n.category),m=a.formatMilliseconds(m,e))}else{var j;this.data[e+1]&&(j=this.data[e+1]);o=a.fixNewLines(n.category);j&&(m=a.fixNewLines(j.category))}}i=i.replace(/\[\[category\]\]/g,String(o));return i=i.replace(/\[\[toCategory\]\]/g,String(m))},adjustBalloonCoordinate:function(i,g){var p=this.xToIndex(i),o=this.chart.chartCursor;if(this.stickBalloonToCategory){var n=this.data[p];n&&(i=n.x[this.id]);this.stickBalloonToStart&&(i-=this.cellWidth/2);var l=0;if(o){var m=o.limitToGraph;if(m){var j=m.valueAxis.id;m.hidden||(l=n.axes[j].graphs[m.id].y)}this.rotate?("left"==this.position?(m&&(l-=o.width),0<l&&(l=0)):0>l&&(l=0),o.fixHLine(i,l)):("top"==this.position?(m&&(l-=o.height),0<l&&(l=0)):0>l&&(l=0),o.fullWidth&&(i+=1),o.fixVLine(i,l))}}o&&!g&&(o.setIndex(p),this.parseDates&&o.setTimestamp(this.coordinateToDate(i).getTime()));return i}})})();