{% extends 'Base/base.html' %}
{% block title %}Nuevo Vale{% endblock %}

{% block content %}

{% if messages %}
{% for message in messages %}
<script>
    Swal.fire({
        "title": "Informacion Sistema",
        "text": "{{message}}",
        "icon": "{{message.tags}}"
    })
</script>
{% endfor %}
{% endif %}

<div class="content-wrapper">

    <div class="container-xxl flex-grow-1 container-p-y">


        <!-- Basic Layout -->
        <div class="col-md-12">
            <div class="card md-6">
                <div class="card-header d-flex align-items-center justify-content-between">
                    <h5 class="mb-0">Formulario Nuevo Vale</h5>
                    {% if user.tienda == "Estanzuela" %}
                    <small class="text-muted float-end">Ultimo Vale {{c}}.1</small>
                    {% elif user.tienda == "Teculutan" %}
                    <small class="text-muted float-end">Ultimo Vale {{c}}.2</small>
                    {% elif user.tienda == "Zacapa" %}
                    <small class="text-muted float-end">Ultimo Vale {{c}}.3</small>
                    {% else %}
                    <small class="text-muted float-end">Ultimo Vale {{c}}.4</small>
                    {% endif %}
                </div>
                <div class="card-body">
                    <form action="#" method="POST" enctype="multipart/form-data">{% csrf_token %}
                        <div class="row">
                            <div class="col-md-2">
                                <label>Sucursal Origen</label>
                                {% if user.rol == "admin" %}
                                <select name="origen" class="form-control" required>
                                    <option value="Estanzuela">Estanzuela</option>
                                    <option value="Teculutan">Teculutan</option>
                                    <option value="Zacapa">Zacapa</option>
                                    <option value="Santa Cruz">Santa Cruz</option>
                                </select>
                                {% else %}
                                <input type="text" class="form-control" name="origen" readonly value="{{user.tienda}}">
                                {% endif %}
                            </div>
                            <div class="col-md-4">
                                <label>Sucursal Destino</label>
                                {{form.destino}}
                            </div>
                            
                        </div><br>
                        <div class="row">
                            <div class="col-md-12">
                                <label>Observaciones</label>
                                {{form.observacion}}
                            </div>
                        </div><br>
                        <div class="row">
                            <div class="col-md-4">
                                <label>Fecha</label>
                                <input type="text" class="form-control" readonly value="{% now 'd-m-Y' %}" />
                            </div>
                            <div class="col-md-4">
                                <label>Usuario</label>
                                <input type="text" class="form-control" readonly value="{{user.username}}" />
                            </div>
                        </div><br>
                        <div class="row">
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary">Generar</button>
                                <button type="reset" class="btn btn-danger">Cancelar</button>
                            </div>
                        </div>
                </div>
                </form>
            </div>
        </div>


    </div>

</div>



{% endblock %}