# Generated by Django 4.1.7 on 2023-09-14 12:14

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Cliente',
            fields=[
                ('nit', models.CharField(max_length=15, primary_key=True, serialize=False)),
                ('nombre', models.CharField(max_length=550)),
                ('direccion', models.CharField(max_length=850)),
                ('tel', models.CharField(blank=True, default='0000-0000', max_length=9, null=True)),
                ('compras_contado', models.IntegerField(blank=True, default=0, null=True)),
                ('total_contado', models.DecimalField(blank=True, decimal_places=2, default=0.0, max_digits=18, null=True)),
                ('compras_credito', models.IntegerField(blank=True, default=0, null=True)),
                ('total_credito', models.DecimalField(blank=True, decimal_places=2, default=0.0, max_digits=18, null=True)),
                ('total_credito_pagado', models.DecimalField(blank=True, decimal_places=2, default=0.0, max_digits=18, null=True)),
                ('fecha', models.CharField(blank=True, max_length=10, null=True)),
                ('usuario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['nit'],
            },
        ),
    ]
