{% extends 'Base/base.html' %}
{% block title %}Ingreso de Productos Por Factura{% endblock %}

{% block content %}

{% if messages %}
{% for message in messages %}
<script>
  Swal.fire({
    "title": "Informacion Sistema",
    "text": "{{message}}",
    "icon": "{{message.tags}}"
  })
</script>
{% endfor %}
{% endif %}



<div class="content-wrapper">

  <div class="container-xxl flex-grow-1 container-p-y">

    <div class="card-body" style="background-color: white;">

      <div class="row">

        <div class="col-md-2">
          <label for="">Factura</label>
          <p style="color: red;">{{c.factura}}</p>
        </div>

        <div class="col-md-3">
          <label for="">Proveedor</label>
          <p style="color: red;">{{c.id_prov.nombre}}</p>
        </div>

        <div class="col-md-2">
          <label for="">Items</label>
          <p style="color: red;">{{c.cantidad}}
            {% if i == c.cantidad %}
            <i style="color:green" class='bx bxs-check-shield'></i>
            {% else %}
            <i style="color:red" class='bx bx-error'></i>
            {% endif %}
          </p>
        </div>

        <div class="col-md-2">
          <label for="">Total</label>
          <p style="color: red;">Q.{{c.total}}
            {% if total == c.total %}
            <i style="color:green" class='bx bxs-check-shield'></i>
            {% else %}
            <i style="color:red" class='bx bx-error'></i>
            {% endif %}
          </p>
        </div>

        <div class="col-md-2">
          <label for="">Fecha</label>
          <p style="color: red;">{{c.fecha|date:"d-m-Y"}}</p>
        </div>

      </div><br><br>

      <div class="row">

        <div class="col-md-2">
          <label for="">Articulos Ingresados</label>
          {% if i == None %}
          <p style="color: red;">0
            {% else %}
          <p style="color: red;">{{i}}
            {% endif %}
            {% if i == c.cantidad %}
            <i style="color:green" class='bx bxs-check-shield'></i>
            {% else %}
            <i style="color:red" class='bx bx-error'></i>
            {% endif %}
          </p>
        </div>

        <div class="col-md-2">
          <label for="">Total Ingresado</label>
          {% if total == None %}
          <p style="color: red;">Q.0.00
            {% if total == c.total %}
            <i style="color:green" class='bx bxs-check-shield'></i>
            {% else %}
            <i style="color:red" class='bx bx-error'></i>
            {% endif %}
          </p>
          {% else %}
          <p style="color: red;">Q.{{total}}
            {% if total == c.total %}
            <i style="color:green" class='bx bxs-check-shield'></i>
            {% else %}
            <i style="color:red" class='bx bx-error'></i>
            {% endif %}
          </p>
          {% endif %}
        </div>

        <div class="col-md-2">
          <label for="">Estado Ingreso</label>
          {% if total == c.total and i == c.cantidad %}
          <p style="color: green;">INGRESO CUADRADO
            <i style="color:green" class='bx bxs-check-shield'></i>
            {% else %}
            <i style="color:red" class='bx bx-error'></i>
            {% endif %}
          </p>
        </div>

      </div><br>


      <hr>

    <form action="#" method="POST">{% csrf_token %}
        <div class="row" style="border-bottom: 2px solid black;" align="center">


          <div class="col-md-3">

            <label for="">Tipo Busqueda</label>
            <select name="tipo" class="form-control" required>
              <option value="nombre">Nombre Producto</option>
              <option value="codigo">Codigo Producto</option>
            </select>
          </div>

          <div class="col-md-6">
            <label for="">Busqueda de Productos</label><br>
            <input type="text" name="buscar" class="form-control" autofocus="buscar" placeholder="Agregar/Buscar">
      </form><br>
    </div>
    <div class="col-md-2" style="margin-top: 15px;">
      <form action="#" method="POST">{% csrf_token %}

        <button name="terminar" class="btn btn-danger">Terminar</button>

      </form>
    </div>
 
  </div>

  <div class="row">

    <div class="col-md-12">

      {% if b %}
      <div style="overflow-y: scroll; max-height: 11rem;">
        <div class="table-responsive">
          <table class="table table-bordered table-sm">
            <thead>
              <tr>
                <th scope="col">Codigo</th>
                <th scope="col">Producto</th>
                <th scope="col">Stock</th>
                <th scope="col">Precio Compra</th>
                <th scope="col">Precio Venta</th>
                <th scope="col">Nuevo Precio Compra</th>
                <th scope="col">Nuevo Precio Venta</th>
                <th scope="col">Cant</th>
              </tr>
            </thead>
            <tbody>
              {% for p in buscar %}
              <tr>
                <td>{{p.id}}</td>
                <td>{{p.nombre}}</td>
                <td>{{p.stock}}</td>
                <td>Q.{{p.precio_compra}}</td>
                <td>Q.{{p.precio_venta}}</td>
                <form action="#" method="POST">{% csrf_token %}
                  <input type="hidden" value="{{p.id}}" name="id">
                  <td><input type="text" name="nuevocompra" placeholder="0"></td>
                  <td><input type="text" name="nuevoventa" placeholder="0"></td>
                  <td><input type="text" name="cantidad" placeholder="0"></td>
                  
                  <td><button name="agregar" class="btn btn-sm btn-success"><i style="color: white;"
                        class="bx bx-plus-circle"></i></button>
                  </td>
                </form>
              </tr>      
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
      {% else %}
      <br>
      
      {% if h %}
      <caption>PRODUCTO NO EXISTE CREALO</caption>
      <caption>
        <form action="#" method="POST">{% csrf_token %}
          <div class="row">
            <div class="col-md-6">
              <input type="text" name="nuevo" class="form-control" value="{{buscar}}" placeholder="Nombre del Nuevo Producto">
            </div>
            <div class="col-md-3">
              <select name="cate" class="form-control">
                {% for c in cate %}
                <option value="{{c.id}}">{{c.nombre}}</option>
                {% endfor %}
              </select>
            </div>
            <div class="col-md-3">
              <button type="submit" name="hacer" class="btn btn-success btn-sm">CREAR</button>
            </div>
          </div>
        </form>
      </caption>
      {% else %}
      {% endif %}
      {% endif %}

    </div>

  </div><br>



  <div class="row" align="center">

    <div class="col-md-12">

      <label for="">Productos Agregados</label>

      <div style="height: 12rem; overflow-y: scroll;">
        <div class="table-responsive">
          <table class="table">
            <thead>
              <tr>
                <th scope="col">Cod</th>
                <th scope="col">Prod</th>
                <th scope="col">Cant</th>
                <th scope="col">Precio Compra</th>
                <th scope="col">Precio Venta</th>
                <th scope="col">Total</th>
              </tr>
            </thead>
            <tbody>
              {% for d in d %}
              <tr>
                <th scope="row">{{d.id_prod.id}}</th>
                <td>{{d.id_prod.nombre}}</td>
                <td>{{d.cantidad}}</td>
                <td>Q{{d.compra_ahora}}</td>
                <td>Q{{d.venta_ahora}}</td>
                <td>Q.{{d.total}}</td>
                {% if c.estado == 1 %}
                {% else %}
                <form action="#" method="POST">{% csrf_token %}
                  <input type="hidden" name="corr" value="{{d.id}}">
                  <td><button name="quitar" class="btn btn-sm btn-danger"><i style="color: white;"
                        class="bx bx-x"></i></button>
                  </td>
                </form>
                {% endif %}
              </tr>
              {% empty %}
              <caption>SIN PRODUCTOS</caption>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>

    </div>

  </div>



</div>

</div>






{% endblock %}