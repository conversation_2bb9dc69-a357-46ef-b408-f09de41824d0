from django.db import models
from user.models import User

class Gasto(models.Model):
    nombre = models.CharField(max_length=250,blank=False,null=False)
    cantidad = models.IntegerField(blank=False,null=False)
    precio = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False)
    total = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False)
    fecha = models.DateField(blank=False,null=False)
    estado = models.IntegerField(blank=False,null=False)
    usuario = models.ForeignKey(User,on_delete=models.CASCADE,blank=False,null=False)
    tienda = models.CharField(max_length=550,blank=True,null=True,default='')

    class Meta:
        ordering = ["nombre"]

    def __str__(self):
        return self.nombre



