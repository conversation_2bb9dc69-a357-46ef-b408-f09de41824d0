{% extends 'Base/base.html' %}
{% block title %}Nuevo Ingreso Factura{% endblock %}

{% block content %}

{% if messages %}
{% for message in messages %}
<script>
    Swal.fire({
        "title": "Informacion Sistema",
        "text": "{{message}}",
        "icon": "{{message.tags}}"
    })
</script>
{% endfor %}
{% endif %}

<div class="content-wrapper">

    {{form.errors}}

    <div class="container-xxl flex-grow-1 container-p-y">

        <div class="row">
            <!-- Basic Layout -->
            <div class="col-md-10">
                <div class="card md-6">
                    <div class="card-header d-flex align-items-center justify-content-between">
                        <h5 class="mb-0">Formulario Nuevo Factura</h5>
                        <small class="text-muted float-end">Factura</small>
                    </div>
                    <div class="card-body">
                        <form action="#" method="POST" enctype="multipart/form-data">{% csrf_token %}

                            <div class="row">
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-name">Factura</label>
                                    {{form.factura}}
                                </div>
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-name">Serie</label>
                                    {{form.serie}}
                                </div>
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-name">Fecha</label>
                                    {{form.fecha_factura}}
                                </div>

                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-company">Proveedor</label>
                                    <select name="id_prov" class="form-control">
                                        <option value="">Elija Proveedor</option>
                                        {% for c in c %}
                                        <option value="{{c.nit}}">{{c.nombre}}</option>
                                        {% endfor %}
                                    </select>
                                </div>

                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-name">Piezas</label>
                                    {{form.cantidad}}
                                </div>

                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-name">Total</label>
                                    {{form.total}}
                                </div>

                            </div><br>

                            <div class="row">
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-company">Tienda</label>
                                    {% if user.rol == "admin" %}
                                    <select name="tienda" class="form-control">

                                        <option value="">Elija Tienda</option>
                                        <option value="Zacapa">Zacapa</option>
                                        <option value="Estanzuela">Estanzuela</option>
                                        <option value="Teculutan">Teculutan</option>

                                    </select>
                                    {% else %}
                                    <input type="text" readonly class="form-control" name="tienda" value="{{user.tienda}}">
                                    {% endif %}
                                </div>
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-company">Fecha
                                        Sistema</label>
                                    <input type="text" class="form-control" readonly value="{% now 'd-m-Y' %}" />
                                </div>
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-company">Usuario</label>
                                    <input type="text" class="form-control" readonly value="{{user.username}}" />
                                </div>

                            </div><br>

                            <div class="row justify-content-end">
                                <div class="col-md-4">
                                    <button type="submit" class="btn btn-primary">Guardar</button>
                                    <button type="reset" class="btn btn-danger">Cancelar</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

        </div>

    </div>

</div>

{% endblock %}